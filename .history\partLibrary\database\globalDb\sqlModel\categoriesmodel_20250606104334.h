#ifndef CATEGORIESMODEL_H
#define CATEGORIESMODEL_H

//============================================================================
/// file
/// brief      元器件分类数据模型
/// author     wm
/// version    1.0
/// date       2025/06/05
/// todo
//============================================================================

#include "sqlModelBase.h"
#include <memory>
#include <QUuid>

class CategoriesModel : public SqlModelBase
{
public:
    CategoriesModel() : id(QUuid::createUuid().toString()){}

public:
    // 模型属性
    QString id;                 ///< 分类uuid
    QString name;               ///< 分类名称
    QString description;        ///< 分类描述
    QString parentCategoryUUID; ///< 父分类UUID
    QString irdi;               ///< IRDI
    QString usageUnitJson;      ///< 用量单位JSON

public:
    QString tableName()       const override {return "Categories";}
    QString primaryKeyField() const override {return "CategoryUUID";}
    QVariantMap modelToDbMap() const override;
    void dbToModel(const QSqlRecord& record) override;

public:
    // 字段名称常量
    static const QString FIELD_ID;
    static const QString FIELD_NAME;
    static const QString FIELD_DESCRIPTION;
    static const QString FIELD_PARENT_CATEGORY_UUID;
    static const QString FIELD_IRDI;
    static const QString FIELD_USAGE_UNIT_JSON;
    static const QString FIELD_IS_DELETED;

    static std::shared_ptr<CategoriesModel> createPtr()
    {
        return std::make_shared<CategoriesModel>();
    }
};

using CategoriesModelPtr = std::shared_ptr<CategoriesModel>;
using CategoriesModelList = QHash<QString, CategoriesModelPtr>;

#endif // CATEGORIESMODEL_H 