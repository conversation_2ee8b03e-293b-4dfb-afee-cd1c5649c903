﻿/*
	Copyright 2006-2021 The QElectroTech Team
	This file is part of QElectroTech.

	QElectroTech is free software: you can redistribute it and/or modify
	it under the terms of the GNU General Public License as published by
	the Free Software Foundation, either version 2 of the License, or
	(at your option) any later version.

	QElectroTech is distributed in the hope that it will be useful,
	but WITHOUT ANY WARRANTY; without even the implied warranty of
	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
	GNU General Public License for more details.

	You should have received a copy of the GNU General Public License
	along with QElectroTech.  If not, see <http://www.gnu.org/licenses/>.
*/
#ifndef CONNECTORELEMENT_H
#define CONNECTORELEMENT_H

#include "../qetgraphicsitem/element.h"


/**
* @file
* @brief      ADE电缆分支图连接器模型元素
* <AUTHOR>
* @version    1.0
* @date       2023/09/25
* @todo
*/
class ConnectorElement : public Element
{
    Q_OBJECT

public:
    explicit ConnectorElement(const ElementsLocation &,
                              int * = nullptr,
                              const QString& uuid = QString());
    ~ConnectorElement() override;

private:
    ConnectorElement(const Element&);

public:
    enum { ADEType = 4 };
    int adeType() const override {return ADEType;}

public:
    bool        fromXml(QDomElement&, QHash<int,Terminal*>&) override;
    QDomElement toXml(QDomDocument&, QHash<Terminal*,int>&) const override;

protected:
    void mousePressEvent(QGraphicsSceneMouseEvent* event) override;
    void mouseMoveEvent(QGraphicsSceneMouseEvent* event) override;
    void mouseReleaseEvent(QGraphicsSceneMouseEvent* event) override;

private:
    bool mFirstMouseMove;
};

#endif // CONNECTORELEMENT_H
