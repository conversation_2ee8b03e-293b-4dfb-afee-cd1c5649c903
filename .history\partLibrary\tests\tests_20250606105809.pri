# For building the tests

# To enable these tests, add the following line to partLibrary.pri:
# include($$PWD/tests/tests.pri)

# You can wrap the include in a CONFIG block to only build tests in debug mode, e.g.:
# debug {
#     include($$PWD/tests/tests.pri)
# }

# Note: The tests are standalone applications. You will need to create separate
# build configurations or projects in Qt Creator to build and run them.
# This .pri file just adds the sources to the main project compilation,
# which is not ideal for executables with a main() function.

# A better approach would be to create a subdirs project.
# For now, to compile one of the tests, you might need to temporarily
# remove the main application sources from the linkerADE2.pro file and add a
# TEMPLATE = app for the test.

# To compile a test, you can temporarily add ONE of the following lines to your
# .pro file, and ensure no other main() function is being compiled.
# SOURCES += partLibrary/tests/test_globaldb.cpp
# SOURCES += partLibrary/tests/test_partdb.cpp

# Because these are standalone test applications with their own main() functions,
# they cannot be compiled together with your main application. You must compile them
# as separate targets. The .pri file is provided to keep the files organized.

# This pri file does not add any sources by default to avoid linker errors.
# The user should handle the compilation of these test files.

# Test executables
# You'll likely want to build these as separate projects, but for simplicity
# this adds them to the current project's sources.
# Be aware this will cause linker errors if the main project also has a main() function.

#SOURCES += $$PWD/tests/test_globaldb.cpp
#SOURCES += $$PWD/tests/test_partdb.cpp

# A better way for integration: create a test library or a separate test project.
# For now, providing the files. The user can decide how to integrate them.

HEADERS +=

SOURCES += \
    $$PWD/tests/test_globaldb.cpp \
    $$PWD/tests/test_partdb.cpp 