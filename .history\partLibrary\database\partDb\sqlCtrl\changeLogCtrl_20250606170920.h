#ifndef CHANGELOGCTRL_H
#define CHANGELOGCTRL_H

#include "../../sqlCtrlBase.h"
#include "../sqlModel/changeLogModel.h"
#include <QList>

class ChangeLogCtrl : public SqlCtrlBase
{
public:
    ChangeLogCtrl(const QSqlDatabase& db);
    ~ChangeLogCtrl() = default;

    // 数据库操作
    bool add(const ChangeLogModel& log);
    // ChangeLogs are typically immutable, so no update/delete methods are provided.

    // 查询
    QList<ChangeLogModel> getLogsForRecord(const QString& recordUuid) const;
    QList<ChangeLogModel> getAllLogs() const;

public:
    QString tableName() const override { return "ChangeLog"; }
    QString primaryKeyField() const override { return "ChangeLogUUID"; }
    bool recordExist(const QString& id) const override;
    virtual bool softDelete() const override { return true; }

private:
    QString tableSql() const override;
};

#endif // CHANGELOGCTRL_H 