﻿#ifndef PORTSNAPPOINTITEM_H
#define PORTSNAPPOINTITEM_H

#include <QGraphicsObject>

class Element;

/**
* @file
* @brief      圆形组件上可设置端口的定位点
* <AUTHOR>
* @version    1.0
* @date       2023/10/13
* @todo
*/
class PortSnapPointItem : public QGraphicsObject
{
    Q_OBJECT
public:
    PortSnapPointItem(Element *parentElmt);
    ~PortSnapPointItem() override;

    QRectF boundingRect() const override;

protected:
    void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget) override;

protected:
    void init();
    void setSize(qreal size);

protected:
    QRectF mBr;
    QRectF m_handler_rect;
    qreal  m_current_size;
    QColor m_color;

    Element *mParentElmt = nullptr;
};


class ContactorPortSnapPointItem : public PortSnapPointItem
{
    Q_OBJECT
public:
    explicit ContactorPortSnapPointItem(Element *parent);
    ~ContactorPortSnapPointItem() override;

    QRectF boundingRect() const override;

protected:
    void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget) override;
};


#endif // PORTSNAPPOINTITEM_H
