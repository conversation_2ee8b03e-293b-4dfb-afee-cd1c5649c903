#include "categoryattributelinksmodel.h"

CategoryAttributeLinksModel::CategoryAttributeLinksModel(QObject *parent) : SqlModelBase(parent)
{
    reset();
}

void CategoryAttributeLinksModel::fromJson(const QJsonObject &json)
{
    m_linkUUID = safeGetValue<QString>(json, "LinkUUID");
    m_categoryUUID = safeGetValue<QString>(json, "CategoryUUID");
    m_attributeUUID = safeGetValue<QString>(json, "AttributeUUID");
    m_defaultValue = safeGetValue<QString>(json, "DefaultValue");
}

QJsonObject CategoryAttributeLinksModel::toJsonObject() const
{
    QJsonObject json;
    json["LinkUUID"] = m_linkUUID;
    json["CategoryUUID"] = m_categoryUUID;
    json["AttributeUUID"] = m_attributeUUID;
    json["DefaultValue"] = m_defaultValue;
    return json;
}

void CategoryAttributeLinksModel::fromVariantMap(const QVariantMap &map)
{
    m_linkUUID = safeGetValueFromMap<QString>(map, "LinkUUID");
    m_categoryUUID = safeGetValueFromMap<QString>(map, "CategoryUUID");
    m_attributeUUID = safeGetValueFromMap<QString>(map, "AttributeUUID");
    m_defaultValue = safeGetValueFromMap<QString>(map, "DefaultValue");
}

QVariantMap CategoryAttributeLinksModel::toVariantMap() const
{
    QVariantMap map;
    map["LinkUUID"] = m_linkUUID;
    map["CategoryUUID"] = m_categoryUUID;
    map["AttributeUUID"] = m_attributeUUID;
    map["DefaultValue"] = m_defaultValue;
    return map;
}

void CategoryAttributeLinksModel::reset()
{
    m_linkUUID.clear();
    m_categoryUUID.clear();
    m_attributeUUID.clear();
    m_defaultValue.clear();
}

QString CategoryAttributeLinksModel::getLinkUUID() const { return m_linkUUID; }
void CategoryAttributeLinksModel::setLinkUUID(const QString &linkUUID) { m_linkUUID = linkUUID; }
QString CategoryAttributeLinksModel::getCategoryUUID() const { return m_categoryUUID; }
void CategoryAttributeLinksModel::setCategoryUUID(const QString &categoryUUID) { m_categoryUUID = categoryUUID; }
QString CategoryAttributeLinksModel::getAttributeUUID() const { return m_attributeUUID; }
void CategoryAttributeLinksModel::setAttributeUUID(const QString &attributeUUID) { m_attributeUUID = attributeUUID; }
QString CategoryAttributeLinksModel::getDefaultValue() const { return m_defaultValue; }
void CategoryAttributeLinksModel::setDefaultValue(const QString &defaultValue) { m_defaultValue = defaultValue; } 