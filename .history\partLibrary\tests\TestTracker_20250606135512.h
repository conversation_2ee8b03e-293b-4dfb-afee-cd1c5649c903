#ifndef TEST_TRACKER_H
#define TEST_TRACKER_H

#include <QDebug>
#include <QStringList>

#ifndef TEST_INFO
#define TEST_INFO(msg) qDebug("%s", qPrintable(msg))
#endif
#ifndef TEST_ERROR
#define TEST_ERROR(msg) qWarning("%s", qPrintable(msg))
#endif

/**
 * @brief A simple utility to track test results.
 */
struct TestTracker {
    int total = 0;
    int passed = 0;
    int failed = 0;
    QStringList failures;

    /**
     * @brief Checks a condition and records the result.
     * @param condition The boolean result of the test.
     * @param description A string describing the test case.
     */
    void check(bool condition, const QString& description) {
        total++;
        if (condition) {
            passed++;
            TEST_INFO(QString("  [PASS] %1").arg(description));
        } else {
            failed++;
            failures.append(description);
            TEST_ERROR(QString("  [FAIL] %1").arg(description));
        }
    }

    /**
     * @brief Resets all counters and failure messages.
     */
    void reset() {
        total = 0;
        passed = 0;
        failed = 0;
        failures.clear();
    }

    /**
     * @brief Prints a summary of the test suite results.
     * @param suiteName The name of the test suite.
     */
    void printSummary(const QString& suiteName) const {
        qDebug().noquote() << QString("\n--- Test Summary for %1 ---").arg(suiteName);
        qDebug() << "  Total tests:" << total;
        qDebug() << "  Passed:" << passed;
        qDebug().noquote() << QString("  Failed: %1").arg(failed);
        if (failed > 0) {
            qDebug() << "  Failures:";
            for (const QString& failure : failures) {
                qWarning() << "    -" << failure;
            }
        }
        qDebug().noquote() << QString("--- End Summary for %1 ---\n").arg(suiteName);
    }
};

#endif // TEST_TRACKER_H 