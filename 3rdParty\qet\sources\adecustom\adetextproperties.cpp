﻿#include "adetextproperties.h"

#include <QDebug>

ADETextProperties::ADETextProperties(const QString &srcText, Align textAlign):
    mSrcText(srcText),
    mTextAlign(textAlign)
{
    QStringList parts = srcText.split(";;", QString::SkipEmptyParts);
    if (!parts.isEmpty())
    {
        for (QString part : parts)
        {
            QStringList infos = part.split(";", QString::SkipEmptyParts);
            if (infos.size() != 3)
                continue;

            TextInfo textInfo;
            textInfo.textName = infos[0];
            textInfo.content = infos[1];
            textInfo.show = (QString(infos[2]).toInt() ? true : false);

            mTextInfos << textInfo;
        }
    }
}

bool ADETextProperties::isEmpty() const
{
    return mTextInfos.isEmpty();
}

QString ADETextProperties::toSourceText() const
{
    if (mTextInfos.isEmpty())
        return mSrcText;

    QString text;

    for (TextInfo info : mTextInfos)
    {
        QString show = (info.show ? "1" : "0");

        text += info.textName;
        text += ";";
        text += info.content;
        text += ";";
        text += show;
        text += ";;";
    }

    return text;
}

QString ADETextProperties::getOrganisedText(TextType type) const
{
    if (mTextInfos.isEmpty())
        return mSrcText;

    QString text;

    for (TextInfo info : mTextInfos)
    {
        if (!info.show)
            continue;

        if (type == TextType::Basic)
        {
            if (info.textName != "代号" && info.textName != "名称")
                continue;
        }

        text += info.content;
        text += "\n";
    }
    text.chop(1);

    return text;
}

bool ADETextProperties::hasADETextType() const
{
    for (TextInfo info : mTextInfos)
    {
        if (!info.content.isEmpty())
            return true;
    }

    return false;
}

void ADETextProperties::syncShowState(const ADETextProperties &other)
{
    QList<TextInfo> otherTextInfos = other.textInfos();

    QList<TextInfo> textInfos;

    for (TextInfo info : mTextInfos)
    {
        TextInfo copy = info;
        for (int i = 0; i < otherTextInfos.size(); i++)
        {
            TextInfo otherInfo = otherTextInfos.at(i);
            if (copy.textName == otherInfo.textName)
            {
                copy.show = otherInfo.show;
                otherTextInfos.removeAt(i);
                break;
            }
        }

        textInfos << copy;
    }
    textInfos << otherTextInfos;

    mTextInfos = textInfos;
}

bool ADETextProperties::setTextShowState(const QString &name, const bool &show)
{
    bool find = false;

    QList<TextInfo> textInfos;

    for (TextInfo info : mTextInfos)
    {
        if (info.textName == name)
        {
            info.show = show;
            find = true;
        }

        textInfos << info;
    }

    mTextInfos = textInfos;

    return find;
}

QString ADETextProperties::htmlStrMakeAlign(const QString &html)
{
    /*
    <!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0//EN" "http://www.w3.org/TR/REC-html40/strict.dtd">
    <html><head><meta name="qrichtext" content="1" /><style type="text/css">
    p, li { white-space: pre-wrap; }
    </style></head><body style=" font-family:'新宋体'; font-size:12px; font-weight:400; font-style:normal;">
    <p style=" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;">文本1</p>
    <p style=" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;">文本1</p></body></html>
    */

    // 找到 <body style 属性段落
    QString str = html;
    if (mTextAlign == Align::Left)
        str.replace("; }", "; text-align: left; }");
    else
        str.replace("; }", "; text-align: center; }");
    return str;
}

QString ADETextProperties::getBasicSourceText(const QString &mark, const QString &name,
                                              bool showMark, bool showName)
{
    QString markState = (showMark ? "1" : "0");
    QString nameState = (showName ? "1" : "0");

    QString text;
    text += QString("代号;%1;%2;;").arg(mark).arg(markState);
    text += QString("名称;%1;%2;;").arg(name).arg(nameState);
    return text;
}

QString ADETextProperties::getFullSourceText(QVector<QPair<QString, QString> > texts)
{
    QString srcText;
    for (QPair<QString, QString> text : texts)
    {
        srcText += QString("%1;%2;%3;;").arg(text.first).arg(text.second).arg("1");
    }
    return srcText;
}

void ADETextProperties::setBasicTextShowState(ADEItemTextShowState option)
{
    if (option == DGM_BothShow)
    {
        setTextShowState("代号", true);
        setTextShowState("名称", true);
    }
    else if (option == DGM_OnlyMarkShow)
    {
        setTextShowState("代号", true);
        setTextShowState("名称", false);
    }
    else if (option == DGM_OnlyNameShow)
    {
        setTextShowState("代号", false);
        setTextShowState("名称", true);
    }
}

void ADETextProperties::setTextAlignDirection(Align direction)
{
    mTextAlign = direction;
}

QString ADETextProperties::getContentByTextName(const QString &name) const
{
    for (auto t : mTextInfos)
    {
        if (t.textName == name)
            return t.content;
    }
    return QString();
}

ADETextProperties::ADEItemTextShowState ADETextProperties::textStateFromStr(const QString &state)
{
    if      (state == "1") return ADETextProperties::DGM_BothShow;
    else if (state == "2") return ADETextProperties::DGM_OnlyMarkShow;
    else if (state == "3") return ADETextProperties::DGM_OnlyNameShow;

    return ADETextProperties::DGM_BothShow;
}
