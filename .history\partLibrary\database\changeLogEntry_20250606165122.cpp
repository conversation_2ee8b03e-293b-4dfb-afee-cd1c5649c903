#include "changeLogEntry.h"
#include <QVariant>

namespace part_library
{
namespace database
{

QString ChangeLogEntry::tableName() const
{
    return "ChangeLog";
}

QString ChangeLogEntry::primaryKeyField() const
{
    return "ChangeLogUUID";
}

QVariantMap ChangeLogEntry::modelToDbMap() const
{
    QVariantMap map;
    map[":ChangeLogUUID"] = changeLogUUID;
    map[":Timestamp"] = timestamp;
    map[":UserName"] = userName;
    map[":Action"] = action;
    map[":RecordUUID"] = recordUUID;
    map[":FieldName"] = fieldName;
    map[":OldValue"] = oldValue;
    map[":NewValue"] = newValue;
    map[":Description"] = description;
    return map;
}

void ChangeLogEntry::dbToModel(const QSqlRecord& record)
{
    changeLogUUID = record.value("ChangeLogUUID").toString();
    timestamp = record.value("Timestamp").toDateTime();
    userName = record.value("UserName").toString();
    action = record.value("Action").toString();
    recordUUID = record.value("RecordUUID").toString();
    fieldName = record.value("FieldName").toString();
    oldValue = record.value("OldValue").toString();
    newValue = record.value("NewValue").toString();
    description = record.value("Description").toString();
}

} // namespace database
} // namespace part_library 