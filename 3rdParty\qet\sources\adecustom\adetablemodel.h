﻿#ifndef ADETABLEMODEL_H
#define ADETABLEMODEL_H

#include <QAbstractTableModel>
#include <QPointer>
#include <QDomElement>

class QETProject;


class ADETableModel : public QAbstractTableModel
{
	Q_OBJECT

	public:
        explicit ADETableModel(const QString &uuid, QObject *parent = nullptr);

		int rowCount(const QModelIndex &parent = QModelIndex()) const override;
		int columnCount(const QModelIndex &parent = QModelIndex()) const override;
		bool setHeaderData(int section,
				   Qt::Orientation orientation,
				   const QVariant &value,
				   int role = Qt::EditRole) override;
		QVariant headerData(int section,
				    Qt::Orientation orientation,
				    int role = Qt::DisplayRole) const override;
		bool setData(const QModelIndex &index,
			     const QVariant &value,
			     int role = Qt::EditRole) override;
		QVariant data(const QModelIndex &index,
			      int role = Qt::DisplayRole) const override;

		QDomElement toXml(QDomDocument &document) const;
		void fromXml(const QDomElement &element);
		void setIdentifier(const QString &identifier);
		QString identifier() const {return m_identifier;}
		static QString xmlTagName() {return QString("project_data_base_model");}

        // ADE新增的方法
        void setUuid(const QString &uuid);
        QString uuid() const {return m_uuid;}
        void setContent(const QStringList &headers,
                        const QVector<QStringList> &wholeContent);

	private:
		void dataBaseUpdated();
        void setHeaderString(const QStringList &headerLabels);

	private:
        QVector<QStringList> m_content;
		//First int = section, second int = Qt::role, QVariant = value
		QHash<int, QHash<int, QVariant>> m_header_data;
        QHash<int, QVariant> m_index_0_0_data;  ///< 记录单元格属性(字体、文本排列方式、文本与单元格边距)
		QString m_identifier = "unknow";

        // ADE新增成员变量
        QString m_uuid;             ///< 数据来源节点uuid, 对于接点信息表即为电缆uuid
        QString m_row_separator;    ///< 行文本分隔符
};

#endif // ADETABLEMODEL_H
