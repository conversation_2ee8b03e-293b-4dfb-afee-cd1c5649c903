#ifndef ADEDIAGRAMCABLESTYLEMANAGER_H
#define ADEDIAGRAMCABLESTYLEMANAGER_H

#include <QMap>
#include <QColor>
#include <QGraphicsItem>

#include "../CustomExtend/adeelementstyle.h"
#include "sql/sqlCtrl/interfacectrl.h"
#include "sql/sqlCtrl/prjinfoctrl.h"
#include "layoutDiagram/highlightSignal/interfacesignalconnection.h"

class Diagram;
class CustomConductor;
class InterfaceElement;

class ADEDiagramCableStyleManager
{
public:
    ADEDiagramCableStyleManager(Diagram *diagram);
    ~ADEDiagramCableStyleManager();

    enum DiagramCableStyle
    {
        DefaultStyle = 0,
        Custom,
        SpecifiedSignalCategoryStyle,
        SignalCategoryStyle,
        Empty
    };

public:
    QDomElement toXml(QDomElement &root);
    bool fromXml(const QDomElement &root);

    void switchDiagramCableStyle(DiagramCableStyle style);
    void switchDiagramCableStyleHighLightSpecifiedSignalCategory(int index, QList<SignalCategoryInfo> infos);

    void defaultCustomStyleWithoutXnlInfo(QList<QGraphicsItem *> qgis);
    void setCurrentStyle(DiagramCableStyle style);
    DiagramCableStyle getCurrentStyle() {return mCurrentStyle;}

    void updateCustomConductorStyles(QString uuid, AdeElementStyle style);
    void updateSgCategoryStyles();

    void refreshCableStyle(const QString &cableUuid, DiagramCableStyle style);

    void clearCustomConductorsStyles() {customConductorStyles.clear();}
    QMap<QString, AdeElementStyle> getCustomConductorStyles() {return customConductorStyles;}

    AdeElementStyle getCustomConductorStyle(const QString &uuid);

private:
    QMap<QString, QString> getSignalCategolyColors(const QString &jsonInfo); ///< QMap<category, colorName>

    void changeConductorStyle(CustomConductor *cdt, DiagramCableStyle style);
    void changeConductorStyleByInterfaceBranch(InterfaceElement *ifEle, DiagramCableStyle style);

private:
    QMap<QString, QStringList> mSgCategoryCableStyle;  ///< <cableUuid, colorName>
    QMap<QString, QStringList> mIfToSignalCategoryColors;    ///< <ifUuid, colorName>

    DiagramCableStyle mCurrentStyle = Custom;

    AdeElementStyle mDefaultNoStyle;

    interfaceCtrl* mIfCtrl;

    Diagram *mDiagram;

    QMap<QString, AdeElementStyle> customConductorStyles; ///< <conductorUuid, lineStyle>
};

#endif // ADEDIAGRAMCABLESTYLEMANAGER_H
