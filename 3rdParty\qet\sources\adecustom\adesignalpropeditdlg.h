﻿#ifndef ADESIGNALPROPEDITDLG_H
#define ADESIGNALPROPEDITDLG_H

#include <QDialog>

#include "adepinoutsignalproperty.h"

namespace Ui {
class ADESignalPropEditDlg;
}

/**
 * @brief The ADESignalPropEditDlg class 连接器模型引脚元素信号属性编辑对话框
 */
class ADESignalPropEditDlg : public QDialog
{
    Q_OBJECT

public:
    explicit ADESignalPropEditDlg(const QStringList &pinOutNums,
                                  const ADEPinoutSignalProperty &prop,
                                  QWidget *parent = 0);
    ~ADESignalPropEditDlg();


public:
    ADEPinoutSignalProperty getSignalProperty() const;

    bool isSignalNameChanged();

private slots:
    void onConfirmButtonClicked();

private:
    Ui::ADESignalPropEditDlg *ui;
};

#endif // ADESIGNALPROPEDITDLG_H
