﻿/*
	Copyright 2006-2021 The QElectroTech Team
	This file is part of QElectroTech.

	QElectroTech is free software: you can redistribute it and/or modify
	it under the terms of the GNU General Public License as published by
	the Free Software Foundation, either version 2 of the License, or
	(at your option) any later version.

	QElectroTech is distributed in the hope that it will be useful,
	but WITHOUT ANY WARRANTY; without even the implied warranty of
	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
	GNU General Public License for more details.

	You should have received a copy of the GNU General Public License
	along with QElectroTech.  If not, see <http://www.gnu.org/licenses/>.
*/
#ifndef INTERFACEMODELELEMENT_H
#define INTERFACEMODELELEMENT_H

#include "../qetgraphicsitem/element.h"


/**
* @file
* @brief      ADE电缆芯线连接图接口示意模型元素
* <AUTHOR>
* @version    1.0
* @date       2023/09/25
* @todo
*/
class InterfaceModelElement : public Element
{
    Q_OBJECT

public:
    explicit InterfaceModelElement(const ElementsLocation &,
                                   int * = nullptr,
                                   Element* parentElement = nullptr,
                                   const QString& uuid = QString());
    ~InterfaceModelElement() override;

private:
    InterfaceModelElement(const Element&);

public:
    enum { ADEType = 7 };
    int adeType() const override {return ADEType;}

public:
    bool        fromXml(QDomElement&, QHash<int,Terminal*>&) override;
    QDomElement toXml(QDomDocument&, QHash<Terminal*,int>&) const override;

    void changeLocation(const ElementsLocation &newLocation) override;

public:
    void setInterfaceConnectorId(const QString &uuid);
    QString getInterfaceConnectorId() const {return m_if_connector_id;}

    QString adeFatherUuid() const {return mADEFatherUuid;}
    void    setADEFatherUuid(const QString &uuid);

    void initSortedPoints(const QStringList points);
    void recordSortedPoints(const QStringList points);
    QStringList getSortedPoints(){return mAllSortedPoints;}

    QStringList getInitDisplayPoints(){return mInitDisplayPoints;}
    void setInitDisplayPoints(const QStringList points);
    QStringList getCurrentDiaplayPoints(){return mDiaplayPoints;}
    void recordCurDiaplayPoints(const QStringList points);

    QStringList getHidePoints();

    bool isContactorEleExist(const QString &pointName);
    Element* getChildEle(const QString &pointName);

    void updateSize(QDomElement &xml);

    void recordMirroredState(bool isMirrored);
    bool isMirroredState(){return mIsMirrored;}

    QStringList getAllPoints();  // 获取当前模型所有点位，包括隐藏点位
    void initAllPins();
    void changeChildPins();

    // 移除点位
    void removeChildContactorElement(const QString& pointName);
    // 增加点位
    void addChildContactorElement(const QString& pointName);
    // 点位排序
    void reorderContactorElements(const QStringList points);
    // 更新矩形大小
    void updateModelRect(int pointsNum);

protected:
    QVariant itemChange(GraphicsItemChange, const QVariant&) override;

private:
    QString m_if_connector_id;             ///< 接口元素的连接器uuid
    QString mADEFatherUuid;     ///< 为子腔体时的父接口uuid

    QStringList mAllSortedPoints;   ///< 点位交换后的所有点位顺序
    QStringList mDiaplayPoints;     ///< 模型展示点位
    QStringList mAllPins;

    bool mIsMirrored = false;       //< 被镜像状态

    QStringList mInitDisplayPoints;  ///< 记录初始显示点位
};

#endif // INTERFACEMODELELEMENT_H
