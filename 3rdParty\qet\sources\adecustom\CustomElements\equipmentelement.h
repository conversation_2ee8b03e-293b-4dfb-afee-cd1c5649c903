﻿/*
	Copyright 2006-2021 The QElectroTech Team
	This file is part of QElectroTech.

	QElectroTech is free software: you can redistribute it and/or modify
	it under the terms of the GNU General Public License as published by
	the Free Software Foundation, either version 2 of the License, or
	(at your option) any later version.

	QElectroTech is distributed in the hope that it will be useful,
	but WITHOUT ANY WARRANTY; without even the implied warranty of
	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
	GNU General Public License for more details.

	You should have received a copy of the GNU General Public License
	along with QElectroTech.  If not, see <http://www.gnu.org/licenses/>.
*/
#ifndef EQUIPMENTELEMENT_H
#define EQUIPMENTELEMENT_H

#include "../qetgraphicsitem/element.h"

#include "../CustomExtend/adeelementstyle.h"

class EquipmentRectItem;
class InterfaceElement;

/**
* @file
* @brief      ADE设备元素
* <AUTHOR>
* @version    1.0
* @date       2023/09/22
* @todo
*/
class EquipmentElement : public Element
{
    Q_OBJECT

public:
    explicit EquipmentElement(const ElementsLocation &,
                              int * = nullptr,
                              const QString &uuid = QString());
    ~EquipmentElement() override;

private:
    EquipmentElement(const Element&);

public:
    enum { ADEType = 1 };
    int adeType() const override {return ADEType;}

public:
    QRectF      boundingRect() const override;
    void        setMovable(bool movable) override;
    bool        fromXml(QDomElement&, QHash<int,Terminal*>&) override;
    QDomElement toXml(QDomDocument&, QHash<Terminal*,int>&) const override;

    QPainterPath shape() const override;

protected:
    QVariant itemChange(GraphicsItemChange, const QVariant&) override;
    void mouseMoveEvent(QGraphicsSceneMouseEvent* event) override;
    void mouseReleaseEvent(QGraphicsSceneMouseEvent* event) override;

    void paint(
            QPainter *painter,
            const QStyleOptionGraphicsItem *option,
            QWidget *widget) override;

    void drawHighlight(
        QPainter* painter,
        const QStyleOptionGraphicsItem*,
        const QColor& color);


private:
    void drawSelection(QPainter*,const QStyleOptionGraphicsItem*) override;
    void initADEStyle();

public:
    /// 更新形状大小
    void changeShapeSize(const QSize &size);
    void changeLocation(const ElementsLocation &newLocation) override;
    void changeElementStyle(AdeElementStyle &style);

    // ADE属性
    QString adeMark() const ;
    QString adeName() const ;
    AdeElementStyle getAdeElementStyle();

    //元素样式锁定：设备非自定义样式时锁定
    void setElementPatternLockState(bool lock) {mPtternLocked = lock;}
    bool elementPatternLockState();

    void highlightByAdecompartmentItem(bool hl);

private:
    void onElementPoseChanged() override;

    // 缩放抓手相关方法
public:
    /// 显示缩放状态
    void showStretchHandler(bool show);
    /// 创建抓手
    void createHandlerNode();
    /// 移除抓手
    void removeHandlerNode();
    /// 尝试旋转抓手
    void tryRotateHandlerItem();
private:
    void createHandlerItem();
    void deleteHandlerItem();
    void onUpdateHandlerState();
    void setIsMove(bool);
    // END


signals:
    void elementResized(const QPointF &point, const QSize &size);

private:
    EquipmentRectItem* mRectHandle = nullptr;
    AdeElementStyle mAdeStyle;
    QPointF m_origin_pos;

    bool m_selected = false;

    bool mPtternLocked = false; ///< QPen && QBrush

    bool mHighLight = false;
};

#endif // EQUIPMENTELEMENT_H
