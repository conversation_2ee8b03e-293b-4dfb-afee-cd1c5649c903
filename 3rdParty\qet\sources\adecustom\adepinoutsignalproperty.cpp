﻿#include "adepinoutsignalproperty.h"

ADEPinoutSignalProperty::ADEPinoutSignalProperty()
{
    mCustomCategory = qMakePair(QString("无"), QString("#FFFFFF"));

}

void ADEPinoutSignalProperty::init(const QString &signalName, const QString &type,
                                   const QString &current, const QString &voltage,
                                   const QString &frequency, QPair<QString, QString> category, bool used)
{
    mSignalName = signalName;
    mType = type;
    mCurrent = current;
    mVoltage = voltage;
    mFrequency = frequency;
    mCustomCategory = category;
    mUsedState = used;
}

void ADEPinoutSignalProperty::init(const QDomElement &xmlElmt)
{
    if (xmlElmt.tagName() == "signalProperty")
    {
        mSignalName = xmlElmt.attribute("signalName");
        mType = xmlElmt.attribute("type");
        mCurrent = xmlElmt.attribute("current");
        mVoltage = xmlElmt.attribute("voltage");
        mFrequency = xmlElmt.attribute("frequency");

        mCustomCategory.first = xmlElmt.attribute("customCategory");
        mCustomCategory.second = xmlElmt.attribute("customCategoryColor");
    }
}

QDomElement ADEPinoutSignalProperty::toXmlElement(QDomDocument &document) const
{
    QDomElement element = document.createElement("signalProperty");
    element.setAttribute("signalName", mSignalName);
    element.setAttribute("type", mType);
    element.setAttribute("current", mCurrent);
    element.setAttribute("voltage", mVoltage);
    element.setAttribute("frequency", mFrequency);

    element.setAttribute("customCategory", mCustomCategory.first);
    element.setAttribute("customCategoryColor", mCustomCategory.second);

    return element;
}

QString ADEPinoutSignalProperty::propToString(const QString &pinOutNum) const
{
    QString str;
    str += QString("点位：%1 \n").arg(pinOutNum);
    str += QString("信号名称：%1 \n").arg(mSignalName);
    str += QString("信号属性：%1 \n").arg(mType);
    str += QString("电流：%1 \n").arg(mCurrent);
    str += QString("电压：%1 \n").arg(mVoltage);
    str += QString("频率：%1").arg(mFrequency);

    return str;
}
