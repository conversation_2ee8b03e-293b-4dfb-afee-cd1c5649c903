﻿/*
	Copyright 2006-2021 The QElectroTech Team
	This file is part of QElectroTech.

	QElectroTech is free software: you can redistribute it and/or modify
	it under the terms of the GNU General Public License as published by
	the Free Software Foundation, either version 2 of the License, or
	(at your option) any later version.

	QElectroTech is distributed in the hope that it will be useful,
	but WITHOUT ANY WARRANTY; without even the implied warranty of
	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
	GNU General Public License for more details.

	You should have received a copy of the GNU General Public License
	along with QElectroTech.  If not, see <http://www.gnu.org/licenses/>.
*/
#ifndef PINOUTELEMENT_H
#define PINOUTELEMENT_H

#include "../qetgraphicsitem/element.h"
#include "../adepinoutsignalproperty.h"

/**
* @file
* @brief      ADE连接器模型引脚元素
* <AUTHOR>
* @version    1.0
* @date       2023/09/25
* @todo
*/
class PinOutElement : public Element
{
    Q_OBJECT

public:
    explicit PinOutElement(const ElementsLocation &,
                              int * = nullptr);
    ~PinOutElement() override;

private:
    PinOutElement(const Element&);

public:
    enum { ADEType = 6 };
    int adeType() const override {return ADEType;}

public:
    bool        fromXml(QDomElement&, QHash<int,Terminal*>&) override;
    QDomElement toXml(QDomDocument&, QHash<Terminal*,int>&) const override;
    void editProperty() override;
    void paint(QPainter*,
        const QStyleOptionGraphicsItem*,
        QWidget*w) override;

public:
    // 属性
    ADEPinoutSignalProperty m_pin_signal_prop;      ///< 连接器模型引脚元素信号属性
    QString getPinOutNum() const{return m_pinout_num;}

    bool isEditSignalPropMode() const {return m_edit_signal_prop_mode;}
    void setEditSignalPropModeEnabled(bool enabled);

    /// 接触件尺寸
    QString contactorType() const;

    // 功能
    void setPinoutSignalProp(const ADEPinoutSignalProperty& prop);
    void changePinoutSignalProp(const ADEPinoutSignalProperty& prop, bool isChanged);

    void clickPinoutHl();
    void hoverPinoutEvent();
    void editPinoutPropDlg(const QPointF &point);

public slots:
    // 根据信号名称判断是否高亮当前连接器模型引脚元素
    void setPinOutHighlightBySignalName(const QString& signalName, bool hl);
    // 清空点位高亮状态
    void resetPinOutHighlight(bool hl);
    // 更新当前连接器模型引脚元素信号属性
    void updatePinOutSignalProperty(const QStringList& pinOutNums, const ADEPinoutSignalProperty& prop);
    // 根据已使用的全部点号更新引脚元素使用状态
    void updatePinOutUseState(const QStringList& pinOutNums);

    void setDefaultPinoutEleEnabled(bool displayEnable);

    void showSignalCategoryColor(bool show);

protected:
    void mousePressEvent(QGraphicsSceneMouseEvent* event) override;
    void hoverEnterEvent(QGraphicsSceneHoverEvent* event) override;
    void mouseDoubleClickEvent(QGraphicsSceneMouseEvent *event) override;
    QVariant itemChange(GraphicsItemChange, const QVariant&) override;

    bool sceneEventFilter(QGraphicsItem *watched, QEvent *event);

signals:
    void pinOutSignalPropChanged(const QString& pinOutNum, const ADEPinoutSignalProperty& prop, bool isChanged);
    void pinOutSelected(const QString& signal, const QString pinoutNum, bool selected);

    void changePointSignalNameToolTip();

private:
    // 设置引脚是否可交互
    void setFocusable(const bool& movable);

    void onPinOutSelectionChange(bool selected);
    QList<PinOutElement *> sameSingalNameElements(QList<PinOutElement *> &otherElmts);    ///< 信号名称相同的连接器模型引脚元素

    void drawSelection(QPainter*painter, const QStyleOptionGraphicsItem*options) override;

private:
    QColor m_unused_state_color = QColor(108, 123, 139);  ///< 连接器模型引脚元素不使用时颜色
    bool m_can_move = true;
    QString m_pinout_num;                      ///< 连接器引脚元素的点位号
    bool m_edit_signal_prop_mode = false;   ///< 端面图模型引脚元素编辑信号属性模式

    QColor m_pinout_highLight = QColor(84, 207, 106);
    QColor m_disable = QColor(211, 211, 211);

    bool mTextEventFilterInstalled = false;

    bool m_show_category_color = false;

};

#endif // PINOUTELEMENT_H
