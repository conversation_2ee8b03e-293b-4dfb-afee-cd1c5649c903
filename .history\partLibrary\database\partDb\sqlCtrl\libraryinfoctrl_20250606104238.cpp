#include "libraryinfoctrl.h"

#include "3rdParty/logBusiness/logbusiness.h"

#include <QApplication>

LibraryInfoCtrl::LibraryInfoCtrl(const QSqlDatabase &db) :
    SqlCtrlBase(db), mObj(nullptr)
{
    if(!db.isValid())
    {
        LOG_ERROR("db not valid");
    }
    else
    {
        loadTable();
    }
}

LibraryInfoCtrl::~LibraryInfoCtrl()
{
    if(mObj)
    {
        mObj.reset();
    }
}

LibraryInfoModelPtr LibraryInfoCtrl::fetch() const
{
    return mObj;
}

bool LibraryInfoCtrl::insert(const LibraryInfoModelPtr &obj)
{
    if (obj.get() == nullptr)
        return false;

    // LibraryInfo should only have one record
    if(mObj)
    {
        LOG_ERROR("LibraryInfo record already exists.");
        return false;
    }

    if (insertTableRecord(*obj.get()))
    {
        mObj = obj;
        return true;
    }
    else
    {
        return false;
    }
}

bool LibraryInfoCtrl::modify(const LibraryInfoModelPtr &newValueObj)
{
    if (newValueObj.get() == nullptr || mObj.get() == nullptr)
        return false;

    if (updateTableRecord(*newValueObj.get()))
    {
        mObj->libraryName = newValueObj->libraryName;
        mObj->version = newValueObj->version;
        mObj->isOfficial = newValueObj->isOfficial;
        mObj->description = newValueObj->description;
        mObj->creationTimestamp = newValueObj->creationTimestamp;
        mObj->createdBy = newValueObj->createdBy;
        mObj->lastModifiedTimestamp = newValueObj->lastModifiedTimestamp;

        return true;
    }
    else
    {
        return false;
    }
}

bool LibraryInfoCtrl::recordExist(const QString &id) const
{
    if(mObj)
    {
        return mObj->id == id;
    }
    return false;
}

QString LibraryInfoCtrl::tableSql() const
{
    return QString(R"(
        CREATE TABLE LibraryInfo (
            LibraryUUID           TEXT PRIMARY KEY,        -- [主键] 当前库文件自身的唯一标识符，UUID格式
            LibraryName           TEXT NOT NULL,        -- [字段] 用户可读的库名称，如"我的高压元件库"
            Version               TEXT,                 -- [字段] 库文件的版本号，如 "v1.0.1"
            IsOfficial            INTEGER NOT NULL DEFAULT 0, -- [字段] 标志位，0表示用户库，1表示官方库
            Description           TEXT,                 -- [字段] 对当前库文件的详细描述
            CreationTimestamp     DATETIME DEFAULT CURRENT_TIMESTAMP, -- [字段] 库文件的创建时间戳
            CreatedBy             TEXT,                 -- [字段] 创建该库文件的用户名或系统标识
            LastModifiedTimestamp DATETIME DEFAULT CURRENT_TIMESTAMP -- [字段] 库文件元数据的最后修改时间戳
        );
    )");
}

void LibraryInfoCtrl::loadTable()
{
    QString sql = QString("select * from %1").arg(tableName());
    QSqlQuery query(getCurrentDatabase());
    if(query.exec(sql))
    {
        if (query.next())
        {
            QSqlRecord record = query.record();

            mObj.reset(new LibraryInfoModel);
            mObj->dbToModel(record);
        }
    }
} 