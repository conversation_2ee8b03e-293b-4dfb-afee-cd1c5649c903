#include "changeLogCtrl.h"
#include <QSqlQuery>
#include <QSqlRecord>
#include <QSqlError>
#include <QDebug>

namespace part_library
{
namespace database
{

bool ChangeLogCtrl::addLog(QSqlDatabase &db, const ChangeLogEntry &log)
{
    QSqlQuery query(db);
    query.prepare(log.getInsertSqlTemplate());
    query.bindValue(":ChangeLogUUID", log.changeLogUUID);
    query.bindValue(":Timestamp", log.timestamp);
    query.bindValue(":UserName", log.userName);
    query.bindValue(":Action", log.action);
    query.bindValue(":RecordUUID", log.recordUUID);
    query.bindValue(":FieldName", log.fieldName);
    query.bindValue(":OldValue", log.oldValue);
    query.bindValue(":NewValue", log.newValue);
    query.bindValue(":Description", log.description);

    if (!query.exec()) {
        qWarning() << "ChangeLogCtrl::addLog failed:" << query.lastError().text();
        return false;
    }
    return true;
}

QList<ChangeLogEntry> ChangeLogCtrl::getLogsForRecord(QSqlDatabase &db, const QString &recordUUID)
{
    QList<ChangeLogEntry> logs;
    QSqlQuery query(db);
    query.prepare("SELECT * FROM ChangeLog WHERE RecordUUID = :RecordUUID ORDER BY Timestamp DESC");
    query.bindValue(":RecordUUID", recordUUID);

    if (!query.exec()) {
        qWarning() << "ChangeLogCtrl::getLogsForRecord failed:" << query.lastError().text();
        return logs;
    }

    while (query.next()) {
        ChangeLogEntry log;
        log.dbToModel(query.record());
        logs.append(log);
    }

    return logs;
}

} // namespace database
} // namespace part_library 