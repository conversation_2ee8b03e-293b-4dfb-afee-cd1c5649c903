#include "partsmodel.h"

// 字段名称定义
const QString PartsModel::FIELD_ID = "PartUUID";
const QString PartsModel::FIELD_PART_NUMBER = "PartNumber";
const QString PartsModel::FIELD_CATEGORY_UUID = "CategoryUUID";
const QString PartsModel::FIELD_MANUFACTURER_UUID = "ManufacturerUUID";
const QString PartsModel::FIELD_DESCRIPTION = "Description";
const QString PartsModel::FIELD_LIFECYCLE_STATUS = "LifecycleStatus";
const QString PartsModel::FIELD_CORE_ATTRIBUTES_JSON = "CoreAttributesJson";
const QString PartsModel::FIELD_CUSTOM_ATTRIBUTES_JSON = "CustomAttributesJson";
const QString PartsModel::FIELD_TAGS_JSON = "TagsJson";
const QString PartsModel::FIELD_CREATION_TIMESTAMP = "CreationTimestamp";
const QString PartsModel::FIELD_CREATED_BY = "CreatedBy";
const QString PartsModel::FIELD_LAST_MODIFIED_TIMESTAMP = "LastModifiedTimestamp";
const QString PartsModel::FIELD_LAST_MODIFIED_BY = "LastModifiedBy";
const QString PartsModel::FIELD_REVISION = "Revision";
const QString PartsModel::FIELD_IS_DELETED = "IsDeleted";


QVariantMap PartsModel::modelToDbMap() const
{
    QVariantMap map;
    map.insert(FIELD_ID, id);
    map.insert(FIELD_PART_NUMBER, partNumber);
    map.insert(FIELD_CATEGORY_UUID, categoryUUID);
    map.insert(FIELD_MANUFACTURER_UUID, manufacturerUUID);
    map.insert(FIELD_DESCRIPTION, description);
    map.insert(FIELD_LIFECYCLE_STATUS, lifecycleStatus);
    map.insert(FIELD_CORE_ATTRIBUTES_JSON, coreAttributesJson);
    map.insert(FIELD_CUSTOM_ATTRIBUTES_JSON, customAttributesJson);
    map.insert(FIELD_TAGS_JSON, tagsJson);
    map.insert(FIELD_CREATION_TIMESTAMP, creationTimestamp);
    map.insert(FIELD_CREATED_BY, createdBy);
    map.insert(FIELD_LAST_MODIFIED_TIMESTAMP, lastModifiedTimestamp);
    map.insert(FIELD_LAST_MODIFIED_BY, lastModifiedBy);
    map.insert(FIELD_REVISION, revision);
    return map;
}

void PartsModel::dbToModel(const QSqlRecord &record)
{
    QMap<QString, QVariant> infos = getRecordValues(record);

    if (infos.contains(FIELD_ID)) this->id = infos[FIELD_ID].toString();
    if (infos.contains(FIELD_PART_NUMBER)) this->partNumber = infos[FIELD_PART_NUMBER].toString();
    if (infos.contains(FIELD_CATEGORY_UUID)) this->categoryUUID = infos[FIELD_CATEGORY_UUID].toString();
    if (infos.contains(FIELD_MANUFACTURER_UUID)) this->manufacturerUUID = infos[FIELD_MANUFACTURER_UUID].toString();
    if (infos.contains(FIELD_DESCRIPTION)) this->description = infos[FIELD_DESCRIPTION].toString();
    if (infos.contains(FIELD_LIFECYCLE_STATUS)) this->lifecycleStatus = infos[FIELD_LIFECYCLE_STATUS].toString();
    if (infos.contains(FIELD_CORE_ATTRIBUTES_JSON)) this->coreAttributesJson = infos[FIELD_CORE_ATTRIBUTES_JSON].toString();
    if (infos.contains(FIELD_CUSTOM_ATTRIBUTES_JSON)) this->customAttributesJson = infos[FIELD_CUSTOM_ATTRIBUTES_JSON].toString();
    if (infos.contains(FIELD_TAGS_JSON)) this->tagsJson = infos[FIELD_TAGS_JSON].toString();
    if (infos.contains(FIELD_CREATION_TIMESTAMP)) this->creationTimestamp = infos[FIELD_CREATION_TIMESTAMP].toDateTime();
    if (infos.contains(FIELD_CREATED_BY)) this->createdBy = infos[FIELD_CREATED_BY].toString();
    if (infos.contains(FIELD_LAST_MODIFIED_TIMESTAMP)) this->lastModifiedTimestamp = infos[FIELD_LAST_MODIFIED_TIMESTAMP].toDateTime();
    if (infos.contains(FIELD_LAST_MODIFIED_BY)) this->lastModifiedBy = infos[FIELD_LAST_MODIFIED_BY].toString();
    if (infos.contains(FIELD_REVISION)) this->revision = infos[FIELD_REVISION].toInt();
} 