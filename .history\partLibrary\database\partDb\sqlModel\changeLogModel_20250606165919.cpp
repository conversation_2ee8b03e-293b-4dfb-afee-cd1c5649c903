#include "changeLogModel.h"

const QString ChangeLogModel::FIELD_CHANGELOG_UUID = "ChangeLogUUID";
const QString ChangeLogModel::FIELD_TIMESTAMP = "Timestamp";
const QString ChangeLogModel::FIELD_USER_NAME = "UserName";
const QString ChangeLogModel::FIELD_ACTION = "Action";
const QString ChangeLogModel::FIELD_RECORD_UUID = "RecordUUID";
const QString ChangeLogModel::FIELD_FIELD_NAME = "FieldName";
const QString ChangeLogModel::FIELD_OLD_VALUE = "OldValue";
const QString ChangeLogModel::FIELD_NEW_VALUE = "NewValue";
const QString ChangeLogModel::FIELD_DESCRIPTION = "Description";

QVariantMap ChangeLogModel::modelToDbMap() const
{
    QVariantMap map;
    map.insert(FIELD_CHANGELOG_UUID, id);
    map.insert(FIELD_TIMESTAMP, timestamp);
    map.insert(FIELD_USER_NAME, userName);
    map.insert(FIELD_ACTION, action);
    map.insert(FIELD_RECORD_UUID, recordUuid);
    map.insert(FIELD_FIELD_NAME, fieldName);
    map.insert(FIELD_OLD_VALUE, oldValue);
    map.insert(FIELD_NEW_VALUE, newValue);
    map.insert(FIELD_DESCRIPTION, description);
    return map;
}

void ChangeLogModel::dbToModel(const QSqlRecord &record)
{
    QMap<QString, QVariant> infos = getRecordValues(record);

    if (infos.contains(FIELD_CHANGELOG_UUID)) this->id = infos[FIELD_CHANGELOG_UUID].toString();
    if (infos.contains(FIELD_TIMESTAMP)) this->timestamp = infos[FIELD_TIMESTAMP].toDateTime();
    if (infos.contains(FIELD_USER_NAME)) this->userName = infos[FIELD_USER_NAME].toString();
    if (infos.contains(FIELD_ACTION)) this->action = infos[FIELD_ACTION].toString();
    if (infos.contains(FIELD_RECORD_UUID)) this->recordUuid = infos[FIELD_RECORD_UUID].toString();
    if (infos.contains(FIELD_FIELD_NAME)) this->fieldName = infos[FIELD_FIELD_NAME].toString();
    if (infos.contains(FIELD_OLD_VALUE)) this->oldValue = infos[FIELD_OLD_VALUE].toString();
    if (infos.contains(FIELD_NEW_VALUE)) this->newValue = infos[FIELD_NEW_VALUE].toString();
    if (infos.contains(FIELD_DESCRIPTION)) this->description = infos[FIELD_DESCRIPTION].toString();
} 