﻿#include "connectorelement.h"

#include "../diagram.h"
#include "../qetxml.h"
#include "../qetgraphicsitem/terminal.h"
#include "../qetgraphicsitem/dynamicelementtextitem.h"

#include "../adetextproperties.h"
#include "branchendelement.h"

ConnectorElement::ConnectorElement(const ElementsLocation &location,
                                   int *state, const QString &uuid) :
      Element(location, nullptr, state, Element::Simple, uuid)
    , mFirstMouseMove(true)
{
    for (auto t : terminals())
    {
        t->setDrawTermianl(true);
        t->setDrawHover(true);
    }
}


ConnectorElement::~ConnectorElement()
{

}

bool ConnectorElement::fromXml(QDomElement& e, QHash<int, Terminal*>& table_id_adr)
{
    bool res = Element::fromXml(e, table_id_adr);

    if (res)
    {
        // 设置ADE文本显示
        for (auto dti : dynamicTextItems())
        {
            ADETextProperties textProp = ADETextProperties(dti->text());
            textProp.setTextAlignDirection(ADETextProperties::Left);
            dti->setTextProperty(textProp);
        }
    }

    return res;
}

QDomElement ConnectorElement::toXml(QDomDocument& document,
                                    QHash<Terminal*, int>& table_adr_id) const
{
    // 先在Element基类中创建xml元素, 再补充自定义信息
    QDomElement elmtXml = Element::toXml(document, table_adr_id);
    return elmtXml;
}

void ConnectorElement::mousePressEvent(QGraphicsSceneMouseEvent *event)
{
    mFirstMouseMove = true;
    Element::mousePressEvent(event);
}

void ConnectorElement::mouseMoveEvent(QGraphicsSceneMouseEvent *event)
{
    if (mFirstMouseMove)
    {
        QMap<QString, Element *> cntElmts = diagram()->elementsByType(ConnectorElement::ADEType);

        QList<Element *> connectedEndElmts;
        for (auto cntElmt : diagram()->elementsByType(ConnectorElement::ADEType))
        {
            if (cntElmt->isSelected())
            {
                auto elmtList = cntElmt->connectedElements();
                for (auto e : elmtList)
                {
                    if (e->adeType() == BranchEndElement::ADEType)
                        connectedEndElmts << element_cast<BranchEndElement *>(e);
                }
            }
        }
        // 分支图上移动连接器时，同步移动连接的端点元素，如果按住Ctrl时不移动
        if (!QApplication::queryKeyboardModifiers().testFlag(Qt::ControlModifier))
        {
            for (auto endElmt : connectedEndElmts)
                endElmt->setSelected(true);
        }
        else
        {
            for (auto endElmt : connectedEndElmts)
                endElmt->setSelected(false);
        }


        // auto elmtList = connectedElements();
        // QList<Element *> connectedEndElmts;
        // for (auto e : elmtList)
        // {
        //     if (e->adeType() == BranchEndElement::ADEType)
        //         connectedEndElmts << element_cast<BranchEndElement *>(e);
        // }

        // // 分支图上移动连接器时，同步移动连接的端点元素，如果按住Ctrl时不移动
        // if (!QApplication::queryKeyboardModifiers().testFlag(Qt::ControlModifier))
        // {
        //     for (auto endElmt : connectedEndElmts)
        //         endElmt->setSelected(true);
        // }
        // else
        // {
        //     for (auto endElmt : connectedEndElmts)
        //         endElmt->setSelected(false);
        // }

        mFirstMouseMove = false;
    }     

    Element::mouseMoveEvent(event);
}

void ConnectorElement::mouseReleaseEvent(QGraphicsSceneMouseEvent *event)
{
    Element::mouseReleaseEvent(event);
}

