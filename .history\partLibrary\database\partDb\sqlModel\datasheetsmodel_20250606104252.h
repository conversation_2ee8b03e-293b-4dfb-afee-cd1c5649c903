#ifndef DATASHEETSMODEL_H
#define DATASHEETSMODEL_H

//============================================================================
/// file
/// brief      Datasheet信息数据模型
/// author     wm
/// version    1.0
/// date       2025/06/05
/// todo
//============================================================================

#include "sqlModelBase.h"
#include <memory>
#include <QUuid>
#include <QDateTime>

class DatasheetsModel : public SqlModelBase
{
public:
    DatasheetsModel() : id(QUuid::createUuid().toString()){}

public:
    // 模型属性
    QString id;                 ///< Datasheet uuid
    QString title;              ///< 标题
    QString filePath;           ///< 文件路径
    QString md5Hash;            ///< MD5哈希
    QString revision;           ///< 版本
    QDateTime uploadTimestamp;    ///< 上传时间

public:
    QString tableName()       const override {return "Datasheets";}
    QString primaryKeyField() const override {return "DatasheetUUID";}
    QVariantMap modelToDbMap() const override;
    void dbToModel(const QSqlRecord& record) override;

public:
    // 字段名称常量
    static const QString FIELD_ID;
    static const QString FIELD_TITLE;
    static const QString FIELD_FILE_PATH;
    static const QString FIELD_MD5_HASH;
    static const QString FIELD_REVISION;
    static const QString FIELD_UPLOAD_TIMESTAMP;
    static const QString FIELD_IS_DELETED;

    static std::shared_ptr<DatasheetsModel> createPtr()
    {
        return std::make_shared<DatasheetsModel>();
    }
};

using DatasheetsModelPtr = std::shared_ptr<DatasheetsModel>;
using DatasheetsModelList = QHash<QString, DatasheetsModelPtr>;

#endif // DATASHEETSMODEL_H 