#include "attributedefinitionsctrl.h"
#include <QSqlQuery>
#include <QSqlError>
#include <QDebug>

AttributeDefinitionsCtrl::AttributeDefinitionsCtrl(const QString &dbPath, QObject *parent)
    : SqlCtrlBase(dbPath, "AttributeDefinitions", parent)
{
    m_uuidKeyName = "AttributeUUID";
}

bool AttributeDefinitionsCtrl::checkAndCreateTable()
{
    if (!m_db.isOpen()) {
        qCritical() << "Database not open!";
        return false;
    }

    QSqlQuery query(m_db);
    QString createTableQuery = R"(
        CREATE TABLE IF NOT EXISTS AttributeDefinitions (
            AttributeUUID     TEXT PRIMARY KEY,
            Name              TEXT NOT NULL UNIQUE,
            DataType          TEXT NOT NULL,
            Description       TEXT,
            IRDI              TEXT UNIQUE,
            Unit              TEXT,
            DefaultValue      TEXT,
            ValidationRule    TEXT,
            IsUserDefined     INTEGER NOT NULL DEFAULT 0,
            IsDeleted         INTEGER NOT NULL DEFAULT 0
        )
    )";

    if (!query.exec(createTableQuery)) {
        qCritical() << "Failed to create AttributeDefinitions table:" << query.lastError();
        return false;
    }
    return true;
}

bool AttributeDefinitionsCtrl::insert(const AttributeDefinitionsModel &model)
{
    return SqlCtrlBase::insert(model.toVariantMap());
}

bool AttributeDefinitionsCtrl::update(const AttributeDefinitionsModel &model)
{
    return SqlCtrlBase::update(model.getAttributeUUID(), model.toVariantMap());
}

bool AttributeDefinitionsCtrl::softDelete(const QString &uuid)
{
    return SqlCtrlBase::softDelete(uuid);
}

QSharedPointer<AttributeDefinitionsModel> AttributeDefinitionsCtrl::getByUUID(const QString &uuid)
{
    QVariantMap map = SqlCtrlBase::getByUUID(uuid);
    if (map.isEmpty()) {
        return nullptr;
    }
    auto model = QSharedPointer<AttributeDefinitionsModel>::create();
    model->fromVariantMap(map);
    return model;
}

QList<QSharedPointer<AttributeDefinitionsModel>> AttributeDefinitionsCtrl::getAll()
{
    QList<QVariantMap> maps = SqlCtrlBase::getAll();
    QList<QSharedPointer<AttributeDefinitionsModel>> models;
    for (const auto &map : maps) {
        auto model = QSharedPointer<AttributeDefinitionsModel>::create();
        model->fromVariantMap(map);
        models.append(model);
    }
    return models;
}

QList<QSharedPointer<AttributeDefinitionsModel>> AttributeDefinitionsCtrl::getAllActive()
{
    QList<QVariantMap> maps = SqlCtrlBase::getAllActive();
    QList<QSharedPointer<AttributeDefinitionsModel>> models;
    for (const auto &map : maps) {
        auto model = QSharedPointer<AttributeDefinitionsModel>::create();
        model->fromVariantMap(map);
        models.append(model);
    }
    return models;
} 