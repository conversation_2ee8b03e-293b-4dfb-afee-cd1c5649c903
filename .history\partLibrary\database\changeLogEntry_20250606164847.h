#pragma once

#include <QString>
#include <QDateTime>
#include <QUuid>

namespace part_library
{
namespace database
{

/**
 * @brief Represents a record in the ChangeLog table.
 * @note Corresponds to the ChangeLog table in the database design.
 */
struct ChangeLogEntry
{
    QString changeLogUUID{QUuid::createUuid().toString(QUuid::WithoutBraces)};
    QDateTime timestamp{QDateTime::currentDateTime()};
    QString userName;
    QString action; // 'CREATE', 'UPDATE', 'DELETE'
    QString recordUUID;
    QString fieldName;
    QString oldValue;
    QString newValue;
    QString description;
};

} // namespace database
} // namespace part_library 