#ifndef CATEGORYATTRIBUTELINKSMODEL_H
#define CATEGORYATTRIBUTELINKSMODEL_H

//============================================================================
/// file
/// brief      元器件分类属性数据模型
/// author     wm
/// version    1.0
/// date       2025/06/05
/// todo
//============================================================================

#include "sqlModelBase.h"
#include <memory>
#include <QUuid>

class CategoryAttributeLinksModel : public SqlModelBase
{
public:
    CategoryAttributeLinksModel() : id(QUuid::createUuid().toString()){}

public:
    // 模型属性
    QString id;                 ///< 链接uuid
    QString categoryUUID;       ///< 分类UUID
    QString attributeUUID;      ///< 属性UUID
    QString defaultValue;       ///< 默认值

public:
    QString tableName()       const override {return "Category_Attribute_Links";}
    QString primaryKeyField() const override {return "LinkUUID";}
    QVariantMap modelToDbMap() const override;
    void dbToModel(const QSqlRecord& record) override;

public:
    // 字段名称常量
    static const QString FIELD_ID;
    static const QString FIELD_CATEGORY_UUID;
    static const QString FIELD_ATTRIBUTE_UUID;
    static const QString FIELD_DEFAULT_VALUE;
    static const QString FIELD_IS_DELETED;

    static std::shared_ptr<CategoryAttributeLinksModel> createPtr()
    {
        return std::make_shared<CategoryAttributeLinksModel>();
    }
};

using CategoryAttributeLinksModelPtr = std::shared_ptr<CategoryAttributeLinksModel>;
using CategoryAttributeLinksModelList = QHash<QString, CategoryAttributeLinksModelPtr>;

#endif // CATEGORYATTRIBUTELINKSMODEL_H 