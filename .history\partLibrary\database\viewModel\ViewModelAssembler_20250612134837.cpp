#include "ViewModelAssembler.h"
#include <QString>
#include <QList>

#define LOG_CATEGORY "元器件库"
#include "3rdParty/logBusiness/logbusiness.h"

PartViewModel ViewModelAssembler::createViewModel(const PartModelPtr &partModel,
                                                      const GlobalLibraryContext &globalContext)
{
    PartViewModel viewModel;

    // 1. 直接复制基础信息
    viewModel.id = partModel->id;
    viewModel.partNumber = partModel->partNumber;
    viewModel.categoryUUID = partModel->categoryUUID;
    viewModel.manufacturerUUID = partModel->manufacturerUUID;
    viewModel.description = partModel->description;
    viewModel.lifecycleStatus = partModel->lifecycleStatus;
    viewModel.tags = partModel->tags;
    viewModel.customAttributes = partModel->customAttributes;
    viewModel.creationTimestamp = partModel->creationTimestamp;
    viewModel.createdBy = partModel->createdBy;
    viewModel.lastModifiedTimestamp = partModel->lastModifiedTimestamp;
    viewModel.lastModifiedBy = partModel->lastModifiedBy;
    viewModel.revision = partModel->revision;

    // 2. 【核心】遍历原始核心属性，并进行"丰富化"
    for (const auto& coreAttr : partModel->coreAttributes)
    {
        // 从全局属性控制器中查找属性的完整定义
        // 因为全局控制器已经将数据加载到内存，所以这里的fetch操作非常快
        AttributeDefinitionsModelPtr attrDef = globalContext.attributeDefinitionsCtrl->fetch(coreAttr.uuid);

        PartAttributeViewModel attrViewModel;
        attrViewModel.value = coreAttr.value;

        if (attrDef) // 如果找到了定义
        {
            attrViewModel.id = attrDef->id;
            attrViewModel.irdi = attrDef->irdi;
            attrViewModel.name = attrDef->name;
            attrViewModel.dataType = attrDef->dataType;
            attrViewModel.description = attrDef->description;
            attrViewModel.unit = attrDef->unit;
        }
        else // 如果由于某种原因（如数据不一致）未找到定义
        {
            attrViewModel.id = coreAttr.uuid;
            attrViewModel.name = "未知属性";
            attrViewModel.description = QString("未找到UUID为 %1 的属性定义").arg(coreAttr.uuid);
            attrViewModel.dataType = AttributeDefinitionsModel::String; // 设置一个默认值
        }

        viewModel.coreAttributes.append(attrViewModel);
    }

    return viewModel;
}



CategoriesViewModel ViewModelAssembler::createCategoriesViewModel(const QString &categoryId,
                                                                  const GlobalLibraryContext &globalContext)
{
    // 1. 获取分类模型
    CategoriesModelPtr categoriesModel = globalContext.categoriesCtrl->fetch(categoryId);
    if (!categoriesModel) {
        // 返回一个无效的视图模型
        return CategoriesViewModel();
    }

    // 2. 获取该分类的所有属性链接
    QList<CategoryAttributeLinksModelPtr> categoryAttrLinks;
    CategoryAttributeLinksModelList allLinks = globalContext.categoryAttributeLinksCtrl->fetchAll();

    // 过滤出属于当前分类的属性链接
    for (auto it = allLinks.begin(); it != allLinks.end(); ++it) {
        if (it.value()->categoryUUID == categoryId) {
            categoryAttrLinks.append(it.value());
        }
    }

    // 3. 创建视图模型
    CategoriesViewModel viewModel;

    // 4. 直接复制基础信息
    viewModel.id = categoriesModel->id;
    viewModel.name = categoriesModel->name;
    viewModel.description = categoriesModel->description;
    viewModel.parentCategoryUUID = categoriesModel->parentCategoryUUID;
    viewModel.irdi = categoriesModel->irdi;
    viewModel.usageUnits = categoriesModel->usageUnits;

    // 5. 【核心】遍历分类属性链接，并进行"丰富化"
    for (const auto& attrLink : categoryAttrLinks)
    {
        // 从全局属性控制器中查找属性的完整定义
        AttributeDefinitionsModelPtr attrDef = globalContext.attributeDefinitionsCtrl->fetch(attrLink->attributeUUID);

        CategoryAttributeViewModel attrViewModel;
        attrViewModel.linkId = attrLink->id;
        attrViewModel.defaultValue = attrLink->defaultValue;

        if (attrDef) // 如果找到了定义
        {
            attrViewModel.attributeId = attrDef->id;
            attrViewModel.irdi = attrDef->irdi;
            attrViewModel.name = attrDef->name;
            attrViewModel.dataType = attrDef->dataType;
            attrViewModel.description = attrDef->description;
            attrViewModel.unit = attrDef->unit;
            attrViewModel.options = attrDef->options;
            attrViewModel.validationRule = attrDef->validationRule;
            attrViewModel.isUserDefined = attrDef->isUserDefined;
        }
        else // 如果由于某种原因（如数据不一致）未找到定义
        {
            attrViewModel.attributeId = attrLink->attributeUUID;
            attrViewModel.name = "未知属性";
            attrViewModel.description = QString("未找到UUID为 %1 的属性定义").arg(attrLink->attributeUUID);
            attrViewModel.dataType = AttributeDefinitionsModel::String; // 设置一个默认值
            attrViewModel.isUserDefined = false;
        }

        viewModel.attributes.append(attrViewModel);
    }

    return viewModel;
}
