#include "libraryinfomodel.h"

LibraryInfoModel::LibraryInfoModel(QObject *parent) : SqlModelBase(parent)
{
    reset();
}

void LibraryInfoModel::fromJson(const QJsonObject &json)
{
    m_id = safeGetValue<QString>(json, "Id");
    m_libraryUUID = safeGetValue<QString>(json, "LibraryUUID");
    m_libraryName = safeGetValue<QString>(json, "LibraryName");
    m_version = safeGetValue<QString>(json, "Version");
    m_isOfficial = safeGetValue<bool>(json, "IsOfficial");
    m_description = safeGetValue<QString>(json, "Description");
    m_creationTimestamp = QDateTime::fromString(safeGetValue<QString>(json, "CreationTimestamp"), Qt::ISODate);
    m_createdBy = safeGetValue<QString>(json, "CreatedBy");
    m_lastModifiedTimestamp = QDateTime::fromString(safeGetValue<QString>(json, "LastModifiedTimestamp"), Qt::ISODate);
}

QJsonObject LibraryInfoModel::toJsonObject() const
{
    QJsonObject json;
    json["Id"] = m_id;
    json["LibraryUUID"] = m_libraryUUID;
    json["LibraryName"] = m_libraryName;
    json["Version"] = m_version;
    json["IsOfficial"] = m_isOfficial;
    json["Description"] = m_description;
    json["CreationTimestamp"] = m_creationTimestamp.toString(Qt::ISODate);
    json["CreatedBy"] = m_createdBy;
    json["LastModifiedTimestamp"] = m_lastModifiedTimestamp.toString(Qt::ISODate);
    return json;
}

void LibraryInfoModel::fromVariantMap(const QVariantMap &map)
{
    m_id = safeGetValueFromMap<QString>(map, "Id");
    m_libraryUUID = safeGetValueFromMap<QString>(map, "LibraryUUID");
    m_libraryName = safeGetValueFromMap<QString>(map, "LibraryName");
    m_version = safeGetValueFromMap<QString>(map, "Version");
    m_isOfficial = safeGetValueFromMap<bool>(map, "IsOfficial");
    m_description = safeGetValueFromMap<QString>(map, "Description");
    m_creationTimestamp = safeGetValueFromMap<QDateTime>(map, "CreationTimestamp");
    m_createdBy = safeGetValueFromMap<QString>(map, "CreatedBy");
    m_lastModifiedTimestamp = safeGetValueFromMap<QDateTime>(map, "LastModifiedTimestamp");
}

QVariantMap LibraryInfoModel::toVariantMap() const
{
    QVariantMap map;
    map["Id"] = m_id;
    map["LibraryUUID"] = m_libraryUUID;
    map["LibraryName"] = m_libraryName;
    map["Version"] = m_version;
    map["IsOfficial"] = m_isOfficial;
    map["Description"] = m_description;
    map["CreationTimestamp"] = m_creationTimestamp;
    map["CreatedBy"] = m_createdBy;
    map["LastModifiedTimestamp"] = m_lastModifiedTimestamp;
    return map;
}

void LibraryInfoModel::reset()
{
    m_id.clear();
    m_libraryUUID.clear();
    m_libraryName.clear();
    m_version.clear();
    m_isOfficial = false;
    m_description.clear();
    m_creationTimestamp = QDateTime();
    m_createdBy.clear();
    m_lastModifiedTimestamp = QDateTime();
}

QString LibraryInfoModel::getId() const { return m_id; }
void LibraryInfoModel::setId(const QString &id) { m_id = id; }
QString LibraryInfoModel::getLibraryUUID() const { return m_libraryUUID; }
void LibraryInfoModel::setLibraryUUID(const QString &libraryUUID) { m_libraryUUID = libraryUUID; }
QString LibraryInfoModel::getLibraryName() const { return m_libraryName; }
void LibraryInfoModel::setLibraryName(const QString &libraryName) { m_libraryName = libraryName; }
QString LibraryInfoModel::getVersion() const { return m_version; }
void LibraryInfoModel::setVersion(const QString &version) { m_version = version; }
bool LibraryInfoModel::getIsOfficial() const { return m_isOfficial; }
void LibraryInfoModel::setIsOfficial(bool isOfficial) { m_isOfficial = isOfficial; }
QString LibraryInfoModel::getDescription() const { return m_description; }
void LibraryInfoModel::setDescription(const QString &description) { m_description = description; }
QDateTime LibraryInfoModel::getCreationTimestamp() const { return m_creationTimestamp; }
void LibraryInfoModel::setCreationTimestamp(const QDateTime &creationTimestamp) { m_creationTimestamp = creationTimestamp; }
QString LibraryInfoModel::getCreatedBy() const { return m_createdBy; }
void LibraryInfoModel::setCreatedBy(const QString &createdBy) { m_createdBy = createdBy; }
QDateTime LibraryInfoModel::getLastModifiedTimestamp() const { return m_lastModifiedTimestamp; }
void LibraryInfoModel::setLastModifiedTimestamp(const QDateTime &lastModifiedTimestamp) { m_lastModifiedTimestamp = lastModifiedTimestamp; } 