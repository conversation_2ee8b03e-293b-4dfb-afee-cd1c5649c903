#include "partviewmodelassembler.h"

PartViewModel PartViewModelAssembler::createViewModel(const PartsModelPtr &partModel,
                                                      const AttributeDefinitionsCtrl &attrDefinitionsCtrl)
{
    PartViewModel viewModel;

    // 1. 直接复制基础信息
    viewModel.id = partModel->id;
    viewModel.partNumber = partModel->partNumber;
    viewModel.categoryUUID = partModel->categoryUUID;
    viewModel.manufacturerUUID = partModel->manufacturerUUID;
    viewModel.description = partModel->description;
    viewModel.lifecycleStatus = partModel->lifecycleStatus;
    viewModel.tags = partModel->tags;
    viewModel.customAttributes = partModel->customAttributes;
    viewModel.creationTimestamp = partModel->creationTimestamp;
    viewModel.createdBy = partModel->createdBy;
    viewModel.lastModifiedTimestamp = partModel->lastModifiedTimestamp;
    viewModel.lastModifiedBy = partModel->lastModifiedBy;
    viewModel.revision = partModel->revision;

    // 2. 【核心】遍历原始核心属性，并进行"丰富化"
    for (const auto& coreAttr : partModel->coreAttributes)
    {
        // 从全局属性控制器中查找属性的完整定义
        // 因为全局控制器已经将数据加载到内存，所以这里的fetch操作非常快
        AttributeDefinitionsModelPtr attrDef = attrDefinitionsCtrl.fetch(coreAttr.uuid);

        PartAttributeViewModel attrViewModel;
        attrViewModel.value = coreAttr.value;

        if (attrDef) // 如果找到了定义
        {
            attrViewModel.id = attrDef->id;
            attrViewModel.irdi = attrDef->irdi;
            attrViewModel.name = attrDef->name;
            attrViewModel.dataType = attrDef->dataType;
            attrViewModel.description = attrDef->description;
            attrViewModel.unit = attrDef->unit;
        }
        else // 如果由于某种原因（如数据不一致）未找到定义
        {
            attrViewModel.id = coreAttr.uuid;
            attrViewModel.name = "未知属性";
            attrViewModel.description = QString("未找到UUID为 %1 的属性定义").arg(coreAttr.uuid);
        }

        viewModel.coreAttributes.append(attrViewModel);
    }

    return viewModel;
} 
