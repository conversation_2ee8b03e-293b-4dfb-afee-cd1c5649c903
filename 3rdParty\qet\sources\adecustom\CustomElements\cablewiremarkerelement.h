﻿#ifndef CABLEWIREMARKERELEMENT_H
#define CABLEWIREMARKERELEMENT_H

#include "../qetgraphicsitem/element.h"
#include "layoutDiagram/cablewiretype.h"

class Conductor;
class ConductorCableData;

/**
* @file
* @brief      ADE电缆线缆类型标记元素
* <AUTHOR>
* @version    1.0
* @date       2024/07/19
* @todo
*/
class CableWireMarkerElement : public Element
{
    Q_OBJECT

public:
    explicit CableWireMarkerElement(const ElementsLocation &,
                                    int * = nullptr);
    ~CableWireMarkerElement() override;


public:
    enum { ADEType = 10 };
    int adeType() const override {return ADEType;}

    void setWireType(ADE::Diagram::CableWireType type, const QString &typeName);
    ADE::Diagram::CableWireType wireType() const {return mWireType;}

    QString wireTypeName() const;

    void setMarkedConductors(QList<Conductor *> cdts);
    QList<Conductor *> markedConductors() const;

    void firstCreatedPostProcess();

    QList<ConductorCableData> wireTypeData() const;
    void updateData(const QList<ConductorCableData>  &data);

public:
    bool        fromXml(QDomElement&, QHash<int,Terminal*>&) override;
    QDomElement toXml(QDomDocument&, QHash<Terminal*,int>&) const override;
    QString name() const override;

protected:
    void mousePressEvent(QGraphicsSceneMouseEvent* event) override;
    void mouseReleaseEvent(QGraphicsSceneMouseEvent* event) override;
    void hoverEnterEvent(QGraphicsSceneHoverEvent* event) override;
    void hoverLeaveEvent(QGraphicsSceneHoverEvent* event) override;
    QVariant itemChange(GraphicsItemChange, const QVariant&) override;
    bool sceneEventFilter(QGraphicsItem *watched, QEvent *event) override;

private:
    void initData();
    void initText();
    void highlightMarkedConductors(bool hl);

private:
    bool mFirstAddToScene;

    ADE::Diagram::CableWireType mWireType;
    QString mTypeName;

    QList<ConductorCableData> mData;  ///< 重新创建或从xml中加载、更新芯线连接图时才会被修改
    QList<Conductor *> mMarkedConductors; ///< 跟随mData初始化

    QMap<QString, int> mConductorZValue;
};

class ConductorCableData
{
public:
    ConductorCableData(Conductor *conductor, bool &valid);
    // fromXml
    ConductorCableData(const QDomElement &e, bool &valid);

    Conductor *findConductor(Diagram *diagram) const;
    QDomElement toXml(QDomDocument &doc) const;

public:
    QString ifUuid_1;       ///< 接口uuid，如果接口是多腔体则为子接口的uuid，因为只有子接口才能设置信号
    QString pointName_1;    ///< 接口上信号的点位名称
    QString ifUuid_2;       ///< 数字序号1和2并不对应导线上的terminal的序号
    QString pointName_2;
};

#endif // CABLEWIREMARKERELEMENT_H
