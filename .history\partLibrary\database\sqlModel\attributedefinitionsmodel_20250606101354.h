#ifndef ATTRIBUTEDEFINITIONSMODEL_H
#define ATTRIBUTEDEFINITIONSMODEL_H

#include "sqlModelBase.h"
#include <QString>

class AttributeDefinitionsModel : public SqlModelBase
{
    Q_OBJECT
    Q_PROPERTY(QString attributeUUID READ getAttributeUUID WRITE setAttributeUUID)
    Q_PROPERTY(QString name READ getName WRITE setName)
    Q_PROPERTY(QString dataType READ getDataType WRITE setDataType)
    Q_PROPERTY(QString description READ getDescription WRITE setDescription)
    Q_PROPERTY(QString irdi READ getIrdi WRITE setIrdi)
    Q_PROPERTY(QString unit READ getUnit WRITE setUnit)
    Q_PROPERTY(QString defaultValue READ getDefaultValue WRITE setDefaultValue)
    Q_PROPERTY(QString validationRule READ getValidationRule WRITE setValidationRule)
    Q_PROPERTY(bool isUserDefined READ getIsUserDefined WRITE setIsUserDefined)
    Q_PROPERTY(bool isDeleted READ getIsDeleted WRITE setIsDeleted)

public:
    explicit AttributeDefinitionsModel(QObject *parent = nullptr);

    void fromJson(const QJsonObject &json) override;
    QJsonObject toJsonObject() const override;
    void fromVariantMap(const QVariantMap &map) override;
    QVariantMap toVariantMap() const override;
    void reset() override;

    QString getAttributeUUID() const;
    void setAttributeUUID(const QString &attributeUUID);

    QString getName() const;
    void setName(const QString &name);

    QString getDataType() const;
    void setDataType(const QString &dataType);

    QString getDescription() const;
    void setDescription(const QString &description);

    QString getIrdi() const;
    void setIrdi(const QString &irdi);

    QString getUnit() const;
    void setUnit(const QString &unit);

    QString getDefaultValue() const;
    void setDefaultValue(const QString &defaultValue);

    QString getValidationRule() const;
    void setValidationRule(const QString &validationRule);

    bool getIsUserDefined() const;
    void setIsUserDefined(bool isUserDefined);

    bool getIsDeleted() const;
    void setIsDeleted(bool isDeleted);

private:
    QString m_attributeUUID;
    QString m_name;
    QString m_dataType;
    QString m_description;
    QString m_irdi;
    QString m_unit;
    QString m_defaultValue;
    QString m_validationRule;
    bool m_isUserDefined;
    bool m_isDeleted;
};

#endif // ATTRIBUTEDEFINITIONSMODEL_H 