CONFIG (release, release|debug) {
    win32-msvc*: {
        message("BREAKPAD_crash_handler_attached")

        DEFINES += BREAKPAD_ATTACH

        # qBreakpad中需要使用到network模块
        QT += network

        # 启用多线程、异常、RTTI、STL支持
        CONFIG += thread exceptions rtti stl

        QMAKE_CXXFLAGS_RELEASE = $$QMAKE_CFLAGS_RELEASE_WITH_DEBUGINFO
        QMAKE_LFLAGS_RELEASE = $$QMAKE_LFLAGS_RELEASE_WITH_DEBUGINFO

        # 配置头文件搜索路径和链接库路径
        win32:CONFIG(release, debug|release): {
            LIBS += -L$$PWD/lib/ -lqBreakpad
        }

        DEPENDPATH += $$PWD/include
        INCLUDEPATH += $$PWD/include
    }
}

CONFIG (debug, release|debug) {
   message( 'NOTE: QBreakpad is only valid for release builds' )
}

unix{
    message("BREAKPAD_crash_handler_attached_Linux")

    DEFINES += BREAKPAD_ATTACH

    # qBreakpad中需要使用到network模块
    QT += network

    # 启用多线程、异常、RTTI、STL支持
    CONFIG += thread exceptions rtti stl

    QMAKE_CXXFLAGS_RELEASE = $$QMAKE_CFLAGS_RELEASE_WITH_DEBUGINFO
    QMAKE_LFLAGS_RELEASE = $$QMAKE_LFLAGS_RELEASE_WITH_DEBUGINFO

    # 配置头文件搜索路径和链接库路径
    LIBS += -L$$PWD/lib/linux/ -lqBreakpad

    DEPENDPATH += $$PWD/include
    INCLUDEPATH += $$PWD/include
}