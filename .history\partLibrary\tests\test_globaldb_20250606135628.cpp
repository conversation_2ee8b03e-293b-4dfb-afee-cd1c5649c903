#include <QCoreApplication>
#include <QDebug>
#include <QUuid>
#include <QSqlDatabase>
#include <QSqlError>
#include <stdexcept>

// Assume TEST_INFO and TEST_ERROR are defined somewhere globally
#ifndef TEST_INFO
#define TEST_INFO(msg) qDebug("%s", qPrintable(msg))
#endif
#ifndef TEST_ERROR
#define TEST_ERROR(msg) qWarning("%s", qPrintable(msg))
#endif

// Include Global DB Controllers
#include "../database/globalDb/sqlCtrl/manufacturerctrl.h"
#include "../database/globalDb/sqlCtrl/categoriesctrl.h"
#include "../database/globalDb/sqlCtrl/attributedefinitionsctrl.h"
#include "../database/globalDb/sqlCtrl/categoryattributelinksctrl.h"

// Include Global DB Models
#include "../database/globalDb/sqlModel/manufacturermodel.h"
#include "../database/globalDb/sqlModel/categoriesmodel.h"
#include "../database/globalDb/sqlModel/attributedefinitionsmodel.h"
#include "../database/globalDb/sqlModel/categoryattributelinksmodel.h"

#include "SimpleDbManager.h"
#include "TestTracker.h"

void testManufacturerCtrl(QSqlDatabase& db, TestTracker& tracker) {
    qDebug() << "--- Testing ManufacturerCtrl ---";
    ManufacturerCtrl ctrl(db);
    if (!ctrl.isTableExist()) {
        QSqlError err;
        bool created = ctrl.createTable(err);
        tracker.check(created, "Create Manufacturer table. Error: " + err.text());
        if (!created) return;
    }

    // Insert
    QString newMfId;
    {
        auto mfObj = ManufacturerModel::createPtr();
        mfObj->name = "Test Manufacturer";
        mfObj->website = "www.test.com";
        mfObj->description = "A test manufacturer.";
        bool inserted = ctrl.insert(mfObj);
        tracker.check(inserted, "Insert new manufacturer");
        if (inserted) {
            newMfId = mfObj->id;
        } else {
            return; // Can't proceed
        }
    }

    // Read and Verify
    {
        auto fetchedObj = ctrl.fetch(newMfId);
        tracker.check(fetchedObj != nullptr, "Fetch inserted manufacturer by ID");
        if(fetchedObj) {
            tracker.check(fetchedObj->name == "Test Manufacturer", "Verify fetched manufacturer's name");
        }
    }

    // Modify and Verify
    {
        auto mfObj = ctrl.fetch(newMfId);
        if (mfObj) {
            mfObj->name = "Modified Manufacturer";
            bool modified = ctrl.modify(mfObj);
            tracker.check(modified, "Modify manufacturer's name");
            if(modified) {
                auto verifiedObj = ctrl.fetch(newMfId);
                tracker.check(verifiedObj && verifiedObj->name == "Modified Manufacturer", "Verify modification by fetching again");
            }
        }
    }


    // Delete and Verify
    if(!newMfId.isEmpty())
    {
        bool removed = ctrl.remove(newMfId);
        tracker.check(removed, "Delete manufacturer");
        if(removed) {
            auto fetchedObj = ctrl.fetch(newMfId);
            tracker.check(fetchedObj == nullptr, "Verify manufacturer is deleted");
        }
    }
}

void testCategoriesCtrl(QSqlDatabase& db, TestTracker& tracker) {
    qDebug() << "--- Testing CategoriesCtrl ---";
    CategoriesCtrl ctrl(db);
    if (!ctrl.isTableExist()) {
        QSqlError err;
        bool created = ctrl.createTable(err);
        tracker.check(created, "Create Categories table. Error: " + err.text());
        if (!created) return;
    }

    // Insert Parent
    QString parentCatId;
    {
        auto catObj = CategoriesModel::createPtr();
        catObj->name = "Parent Category";
        bool inserted = ctrl.insert(catObj);
        tracker.check(inserted, "Insert parent category");
        if(inserted) {
            parentCatId = catObj->id;
        } else {
            return;
        }
    }

    // Insert Child and Verify
    QString childCatId;
    {
        auto catObj = CategoriesModel::createPtr();
        catObj->name = "Child Category";
        catObj->parentCategoryUUID = parentCatId;
        bool inserted = ctrl.insert(catObj);
        tracker.check(inserted, "Insert child category");
        if (inserted) {
            childCatId = catObj->id;
            auto fetched = ctrl.fetch(childCatId);
            tracker.check(fetched && fetched->parentCategoryUUID == parentCatId, "Verify child's parent ID");
        } else {
            return;
        }
    }

    // Modify and Verify
    {
        auto catObj = ctrl.fetch(childCatId);
        if(catObj) {
            catObj->name = "Modified Child Category";
            bool modified = ctrl.modify(catObj);
            tracker.check(modified, "Modify child category name");
            if(modified){
                auto fetched = ctrl.fetch(childCatId);
                tracker.check(fetched && fetched->name == "Modified Child Category", "Verify child category modification");
            }
        }
    }

    // Delete Child and Verify
    {
        bool removed = ctrl.remove(childCatId);
        tracker.check(removed, "Delete child category");
        if(removed) {
            auto fetched = ctrl.fetch(childCatId);
            tracker.check(fetched == nullptr, "Verify child category deleted");
        }
    }
    // Delete Parent and Verify
    {
        bool removed = ctrl.remove(parentCatId);
        tracker.check(removed, "Delete parent category");
        if(removed) {
            auto fetched = ctrl.fetch(parentCatId);
            tracker.check(fetched == nullptr, "Verify parent category deleted");
        }
    }
}

void testAttributeDefinitionsCtrl(QSqlDatabase& db, TestTracker& tracker) {
    qDebug() << "--- Testing AttributeDefinitionsCtrl ---";
    AttributeDefinitionsCtrl ctrl(db);
    if (!ctrl.isTableExist()) {
        QSqlError err;
        bool created = ctrl.createTable(err);
        tracker.check(created, "Create AttributeDefinitions table. Error: " + err.text());
        if(!created) return;
    }

    // Insert
    QString newAttrId;
    {
        auto attrObj = AttributeDefinitionsModel::createPtr();
        attrObj->name = "Voltage";
        attrObj->dataType = "double";
        attrObj->unit = "V";
        bool inserted = ctrl.insert(attrObj);
        tracker.check(inserted, "Insert new attribute");
        if(inserted) {
            newAttrId = attrObj->id;
        } else {
            return;
        }
    }

    // Modify and Verify
    {
        auto attrObj = ctrl.fetch(newAttrId);
        if(attrObj) {
            attrObj->name = "Voltage (DC)";
            bool modified = ctrl.modify(attrObj);
            tracker.check(modified, "Modify attribute name");
            if(modified){
                auto fetched = ctrl.fetch(newAttrId);
                tracker.check(fetched && fetched->name == "Voltage (DC)", "Verify attribute modification");
            }
        }
    }

    // Delete and Verify
    {
        bool removed = ctrl.remove(newAttrId);
        tracker.check(removed, "Delete attribute");
        if(removed) {
            auto fetched = ctrl.fetch(newAttrId);
            tracker.check(fetched == nullptr, "Verify attribute deleted");
        }
    }
}

void testCategoryAttributeLinksCtrl(QSqlDatabase& db, TestTracker& tracker)
{
    qDebug() << "--- Testing CategoryAttributeLinksCtrl ---";

    // Setup: We need a category and an attribute to link
    CategoriesCtrl catCtrl(db);
    auto cat = CategoriesModel::createPtr();
    cat->name = "Link Test Category";
    catCtrl.insert(cat);

    AttributeDefinitionsCtrl attrCtrl(db);
    auto attr = AttributeDefinitionsModel::createPtr();
    attr->name = "Link Test Attribute";
    attrCtrl.insert(attr);

    tracker.check(!cat->id.isEmpty() && !attr->id.isEmpty(), "Setup: Create temporary category and attribute for link test");
    if (cat->id.isEmpty() || attr->id.isEmpty()) {
        catCtrl.remove(cat->id); // cleanup
        attrCtrl.remove(attr->id); // cleanup
        return;
    }


    CategoryAttributeLinksCtrl ctrl(db);
    if (!ctrl.isTableExist()) {
        QSqlError err;
        bool created = ctrl.createTable(err);
        tracker.check(created, "Create Category_Attribute_Links table. Error: " + err.text());
        if(!created) return;
    }
    
    // Insert and Verify
    QString newLinkId;
    {
        auto linkObj = CategoryAttributeLinksModel::createPtr();
        linkObj->categoryUUID = cat->id;
        linkObj->attributeUUID = attr->id;
        bool inserted = ctrl.insert(linkObj);
        tracker.check(inserted, "Insert new category-attribute link");
        if(inserted) {
            newLinkId = linkObj->id;
            auto fetched = ctrl.fetch(newLinkId);
            tracker.check(fetched && fetched->categoryUUID == cat->id && fetched->attributeUUID == attr->id, "Verify link insertion");
        } else {
            // cleanup and exit
            catCtrl.remove(cat->id);
            attrCtrl.remove(attr->id);
            return;
        }
    }
    
    // Modify and Verify
    {
        auto linkObj = ctrl.fetch(newLinkId);
        if(linkObj) {
            linkObj->defaultValue = "1.0";
            bool modified = ctrl.modify(linkObj);
            tracker.check(modified, "Modify link's default value");
            if(modified){
                auto fetched = ctrl.fetch(newLinkId);
                tracker.check(fetched && fetched->defaultValue == "1.0", "Verify link modification");
            }
        }
    }

    // Delete and Verify
    {
        bool removed = ctrl.remove(newLinkId);
        tracker.check(removed, "Delete category-attribute link");
        if(removed) {
            auto fetched = ctrl.fetch(newLinkId);
            tracker.check(fetched == nullptr, "Verify link deletion");
        }
    }

    // Teardown
    bool catRemoved = catCtrl.remove(cat->id);
    bool attrRemoved = attrCtrl.remove(attr->id);
    tracker.check(catRemoved && attrRemoved, "Teardown: Remove temporary category and attribute");
}


void run_globalDb_tests()
{
    qDebug() << "\n\n===== Starting Global DB Tests... =====";
    TestTracker tracker;

    QString err;
    QString uuid = QUuid::createUuid().toString();
    QSqlDatabase db;
    // An application instance is expected to exist, so applicationDirPath is valid.
    QString path = QString("%1/global_test.db").arg(QCoreApplication::applicationDirPath());

    if (!SimpleDbManager::connect(path, err, uuid, db))
    {
        QString errInfo = QString("Database connection failed: %1").arg(err);
        TEST_ERROR(errInfo);
        return;
    }

    qDebug() << "Database connected:" << path;

    try {
        testManufacturerCtrl(db, tracker);
        testCategoriesCtrl(db, tracker);
        testAttributeDefinitionsCtrl(db, tracker);
        testCategoryAttributeLinksCtrl(db, tracker);

    } catch (const std::runtime_error& e) {
        tracker.check(false, QString("A runtime error occurred: %1").arg(e.what()));
    }

    SimpleDbManager::close(uuid);
    qDebug() << "Database connection closed.";
    tracker.printSummary("Global DB");
} 
