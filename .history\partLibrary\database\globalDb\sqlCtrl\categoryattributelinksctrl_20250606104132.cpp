#include "categoryattributelinksctrl.h"

#include "3rdParty/logBusiness/logbusiness.h"

#include <QApplication>

CategoryAttributeLinksCtrl::CategoryAttributeLinksCtrl(const QSqlDatabase &db) :
    SqlCtrlBase(db)
{
    if(!db.isValid())
    {
        LOG_ERROR("db not valid");
    }
    else
    {
        loadTable();
    }
}

CategoryAttributeLinksCtrl::~CategoryAttributeLinksCtrl()
{
    for(auto itor = mAllObj.begin(); itor != mAllObj.end(); )
    {
        auto obj = itor.value();
        obj.reset();

        mAllObj.erase(itor++);
    }
}

CategoryAttributeLinksModelList CategoryAttributeLinksCtrl::fetchAll() const
{
    return mAllObj;
}

CategoryAttributeLinksModelPtr CategoryAttributeLinksCtrl::fetch(const QString &id) const
{
    if (modelExist(id))
    {
        return mAllObj.value(id);
    }
    else
    {
        return nullptr;
    }
}

bool CategoryAttributeLinksCtrl::modelExist(const QString &id) const
{
    return mAllObj.contains(id);
}

bool CategoryAttributeLinksCtrl::insert(const CategoryAttributeLinksModelPtr &obj)
{
    if (obj.get() == nullptr)
        return false;

    if (insertTableRecord(*obj.get()))
    {
        mAllObj.insert(obj->id, obj);
        return true;
    }
    else
    {
        return false;
    }
}

bool CategoryAttributeLinksCtrl::modify(const CategoryAttributeLinksModelPtr &newValueObj)
{
    if (newValueObj.get() == nullptr)
        return false;

    QString id = newValueObj->id;
    if (!mAllObj.contains(id))
    {
        LOG_ERROR(QString("无法修改记录：该记录不存在 %1").arg(id));
        return false;
    }

    if (updateTableRecord(*newValueObj.get()))
    {
        auto obj = mAllObj.value(id);
        obj->categoryUUID = newValueObj->categoryUUID;
        obj->attributeUUID = newValueObj->attributeUUID;
        obj->defaultValue = newValueObj->defaultValue;

        return true;
    }
    else
    {
        return false;
    }
}

bool CategoryAttributeLinksCtrl::remove(const QString &id)
{
    if (!mAllObj.contains(id))
    {
        LOG_ERROR(QString("无法删除记录：该记录不存在 %1").arg(id));
        return false;
    }

    if (deleteTableRecord(id))
    {
        auto obj = mAllObj.take(id);
        obj.reset();

        return true;
    }
    else
    {
        return false;
    }
}

bool CategoryAttributeLinksCtrl::recordExist(const QString &id) const
{
    return mAllObj.contains(id);
}

QString CategoryAttributeLinksCtrl::tableSql() const
{
    return QString(R"(
        CREATE TABLE Category_Attribute_Links (
            LinkUUID          TEXT PRIMARY KEY,       -- [主键] 关联记录自身的唯一标识符, UUID格式
            CategoryUUID      TEXT NOT NULL,          -- [外键] 指向 Categories(CategoryUUID)，说明是哪个分类
            AttributeUUID     TEXT NOT NULL,          -- [外键] 指向 AttributeDefinitions(AttributeUUID)，说明关联了哪个属性
            DefaultValue      TEXT,                 -- [字段] 在此分类下，该属性的特定默认值 (可覆盖AttributeDefinitions中的全局默认值)
            IsDeleted         INTEGER NOT NULL DEFAULT 0, -- [字段] 软删除标志
            UNIQUE (CategoryUUID, AttributeUUID) -- [约束] 确保同一个分类下，同一个属性不会被重复关联
        );
    )");
}

void CategoryAttributeLinksCtrl::loadTable()
{
    QString sql = QString("select * from %1 where IsDeleted='0'").arg(tableName());
    QSqlQuery query(getCurrentDatabase());
    if(query.exec(sql))
    {
        while (query.next())
        {
            QSqlRecord record = query.record();

            CategoryAttributeLinksModelPtr obj;
            obj.reset(new CategoryAttributeLinksModel);
            obj->dbToModel(record);

            mAllObj.insert(obj->id, obj);
        }
    }
} 