#include "adediagramstylemanager.h"

#include "3rdParty/qet/sources/qetgraphicsitem/element.h"
#include "3rdParty/qet/sources/qetgraphicsitem/conductor.h"
#include "3rdParty/qet/sources/qetgraphicsitem/independenttextitem.h"
#include "3rdParty/qet/sources/qetgraphicsitem/qetshapeitem.h"
#include "3rdParty/qet/sources/adecustom/adecompartmentitem.h"
#include "3rdParty/qet/sources/adecustom/CustomElements/equipmentelement.h"
#include "3rdParty/qet/sources/qetgraphicsitem/customconductor.h"
#include "3rdParty/qet/sources/adecustom/adelogiccable.h"
#include "3rdParty/qet/sources/adecustom/adecabletextitem.h"
#include "3rdParty/qet/sources/adecustom/adecompartmenttextitem.h"
#include "3rdParty/qet/sources/diagram.h"
#include "sql/sqlModel/projectmodelgetter.h"
#include "userLog/UserLogInstance.h"

#include "3rdParty/logBusiness/logbusiness.h"

ADEDiagramEqStyleManager::ADEDiagramEqStyleManager(Diagram *diagram) :
    mDiagram(diagram)
{
    // initSubSysytemStylesAndCompartmentStyles();
    customElementStyles.clear();

    //设备默认样式
    QPen pen;
    pen.setStyle(Qt::SolidLine);
    pen.setColor(Qt::black);
    pen.setWidthF(ADE::Diagram::EQ_BORDER_WIDTH);

    QBrush brush;
    brush.setStyle(Qt::NoBrush);

    AdeElementStyle::FontInfo fontInfo;
    fontInfo.font = QFont();
    fontInfo.fontColor = QColor(Qt::black);

    mDefaultNoStyle.setPen(pen);
    mDefaultNoStyle.setBrush(brush);
    mDefaultNoStyle.setFont(fontInfo);
    //end
}

ADEDiagramEqStyleManager::~ADEDiagramEqStyleManager()
{
}

void ADEDiagramEqStyleManager::initFromPrjUuid(const QString &prjUuid)
{
    initSubSysytemStylesAndCompartmentStyles(prjUuid);
}

void ADEDiagramEqStyleManager::switchDiagramStyle(DiagramStyle style, QList<QGraphicsItem *> qgis)
{
    QString info;
    if (style == Custom)
        info = "自定义";
    else if (style == SubSystem)
        info = "所属系统样式";
    else if (style == Compartment)
        info = "所属位置样式";
    else if (style == Empty)
        info = "空";
    USERLOG_INFO << QString("【切换设备样式风格】%1").arg(info);

    if (qgis.isEmpty())
        return;

    Diagram* d = nullptr;

    for (auto qgi : qgis)
    {
        switch (qgi->type())
        {
        case Element::Type:
        {
            Element* elmt = static_cast<Element*>(qgi);
            if (elmt->adeType() == EquipmentElement::ADEType)
            {
                EquipmentElement *eqElmt = static_cast<EquipmentElement *>(elmt);

                d = eqElmt->diagram();

                QString cptName, sysName;
                if (PRJ_MD_GETTER->getAllEqObjs(d->ade()->prjUuid()).contains(eqElmt->uuid().toString()))
                {
                    cptName = PRJ_MD_GETTER->getEquipmentObj(d->ade()->prjUuid(), eqElmt->uuid().toString())->compartment;
                    sysName = PRJ_MD_GETTER->getEquipmentObj(d->ade()->prjUuid(), eqElmt->uuid().toString())->subsystem;
                }

                eqElmt->setElementPatternLockState(false);

                AdeElementStyle pattern; pattern = mDefaultNoStyle;
                if (style == Custom)
                {
                    if (customElementStyles.keys().contains(eqElmt->uuid().toString()))
                        pattern = customElementStyles.value(eqElmt->uuid().toString());
                }
                else if (style == SubSystem)
                {
                    if (mSubSystemStyles.keys().contains(sysName))
                        pattern = mSubSystemStyles.value(sysName);

                    pattern.getPen().setWidthF(1.5);
                }
                else if (style == Compartment)
                {
                    if (mCompartmentStyles.keys().contains(cptName))
                        pattern = mCompartmentStyles.value(cptName);

                    pattern.getPen().setWidthF(1.5);
                }

                eqElmt->changeElementStyle(pattern);

                if (style != Custom)
                    eqElmt->setElementPatternLockState(true);

            }
            break;
        }
        default:
            break;
        }
    }
}

void ADEDiagramEqStyleManager::updateSubSystemStyleOrCompartmentStyleInfo(Diagram* currentDgm)
{
    // NOTE: 此函数应该在ADEDiagram初始化完成后再调用
    if (currentDgm->ade())
    {
        QString prjUuid = currentDgm->ade()->prjUuid();
        if (!prjUuid.isEmpty())
        {
            initSubSysytemStylesAndCompartmentStyles(prjUuid);
        }
        else
        {
            qDebug() << Q_FUNC_INFO <<"prjUuid获取失败";
            LOG_ERROR(QString("prjUuid获取失败"));
        }
    }
    else
    {
        LOG_ERROR(QString("ADEDiagram对象获取失败"));
    }

    if (/*mCurrentStyle != Custom && */mCurrentStyle != Empty)
        switchDiagramStyle(mCurrentStyle, currentDgm->items());
}

void ADEDiagramEqStyleManager::updateCustomStyleInfo(QString uuid, AdeElementStyle style)
{
    customElementStyles.insert(uuid, style);
}

QDomElement ADEDiagramEqStyleManager::toXml(QDomElement &root)
{
    /*
        <diagramStyle>
            <customDiagramStyle currentStyle = "0" currentCableStyle = "0">
                <elements>
                    <element uuid="{d081793b-9fea-48ad-aa64-deb728e317a9}">
                        <adeStyle>
                            <pen widthF="1" style="SolidLine" color="#000000" />
                            <brush style="SolidPattern" color="#aaffff" />
                            <fontInfo font="Sans Serif,7,-1,5,0,0,0,0,0,0,normal" fontColor="#000000">
                        </adeStyle>
                    </element>
                </elements>
    */

    //2024-5-7 只记录设备样式，布置图样式切换只设备样式切换，其他元素依然可修改样式

    QDomDocument xml_document = root.ownerDocument();

    //element
    QDomElement elements_dom = xml_document.createElement("elements");
    if (!customElementStyles.isEmpty())
    {
        for (QString uid : customElementStyles.keys())
        {
            QDomElement element_dom = xml_document.createElement("element");
            element_dom.setAttribute("uuid", uid);

            AdeElementStyle style = customElementStyles.value(uid);
            QDomElement style_dom = style.toXml(xml_document);
            element_dom.appendChild(style_dom);

            elements_dom.appendChild(element_dom);
        }
    }
    root.appendChild(elements_dom);

    return root;
}

bool ADEDiagramEqStyleManager::fromXml(const QDomElement &root)
{
    clearCustomStyleContainers();

    //root customDiagramStyle
    if (root.isNull())
        return false;

    QDomElement elements_elmt;
    for (QDomNode child = root.firstChild(); !child.isNull(); child = child.nextSibling())
    {
        QDomElement child_elmt = child.toElement();
        if (child_elmt.isNull()) continue;

        if (child_elmt.tagName() == "elements")
            elements_elmt = child_elmt;
    }

    if (!elements_elmt.isNull())
    {
        for (QDomNode child = elements_elmt.firstChild(); !child.isNull(); child = child.nextSibling())
        {
            QDomElement child_elmt = child.toElement();
            if (child_elmt.isNull()) continue;

            if (child_elmt.tagName() == "element")
            {
                QString uuid = child_elmt.attribute("uuid");
                QDomElement adeStyleEle = child_elmt.firstChildElement("adeStyle");
                AdeElementStyle style; style.fromXml(adeStyleEle);
                customElementStyles.insert(uuid, style);
            }
        }
    }

    return true;
}

void ADEDiagramEqStyleManager::setCurrentStyle(DiagramStyle style)
{
    if (style != mCurrentStyle)
    {
        mCurrentStyle = style;
    }
}

void ADEDiagramEqStyleManager::defaultCustomDiagramStyleWithoutXmlInfo(QList<QGraphicsItem *> qgis)
{
    if (mCurrentStyle != SubSystem &&
            mCurrentStyle != Compartment)
        mCurrentStyle = Custom;

    if (mCurrentStyle != Custom)
        return;

    if (qgis.isEmpty())
        return;

    clearCustomStyleContainers();

    Diagram *d = nullptr;

    for (auto qgi : qgis)
    {
        switch (qgi->type())
        {
        case Element::Type:
        {
            Element* elmt = static_cast<Element*>(qgi);
            if (elmt->adeType() == EquipmentElement::ADEType)
            {
                EquipmentElement *eqElmt = static_cast<EquipmentElement *>(elmt);
                customElementStyles.insert(eqElmt->uuid().toString(), eqElmt->getAdeElementStyle());
            }
            d = elmt->diagram();
            break;
        }
        default:
            break;
        }
    }
}

void ADEDiagramEqStyleManager::clearCustomStyleContainers()
{
    customElementStyles.clear();
}

void ADEDiagramEqStyleManager::initSubSysytemStylesAndCompartmentStyles(const QString &prjUuid)
{
    mSubSystemStyles.clear();
    mCompartmentStyles.clear();

    // ADEDiagram *ade = mDiagram->ade();
    // QString prjUuid = ade->prjUuid();

    if(prjUuid.isEmpty())
        return;
    cptCtrl = Compartment_Factory->getCtrlObj(prjUuid);
    if(cptCtrl)
    {
        compartmentModelList compartList = cptCtrl->fetchAll();
        if (!compartList.isEmpty())
        {
            for (compartmentModelPtr obj : compartList)
            {
                AdeElementStyle pattern;
                QPen pen;
                pen.setStyle(Qt::SolidLine);
                pen.setColor(Qt::black);
                pen.setWidthF(1.5);

                QBrush brush;
                brush.setStyle(Qt::NoBrush);

                AdeElementStyle::FontInfo fontInfo;
                fontInfo.font = QFont();
                fontInfo.fontColor = QColor(Qt::black);

                if (!obj->cpt_color.lineColor.isEmpty())
                    pen.setColor(QColor(obj->cpt_color.lineColor));

                if (!obj->cpt_color.fillColor.isEmpty())
                {
                    brush.setStyle(Qt::SolidPattern);
                    brush.setColor(QColor(obj->cpt_color.fillColor));
                }

                pattern.setPen(pen);
                pattern.setBrush(brush);
                pattern.setFont(fontInfo);

                mCompartmentStyles.insert(obj->name, pattern);
            }
        }
    }

    sysCtrl = Subsystem_Factory->getCtrlObj(prjUuid);
    if(sysCtrl)
    {
        subsystemModelList systemList = sysCtrl->fetchAll();
        if (!systemList.isEmpty())
        {
            for (subsystemModelPtr obj : systemList)
            {
                AdeElementStyle pattern;
                QPen pen;
                pen.setStyle(Qt::SolidLine);
                pen.setColor(Qt::black);
                pen.setWidthF(1.5);

                QBrush brush;
                brush.setStyle(Qt::NoBrush);

                AdeElementStyle::FontInfo fontInfo;
                fontInfo.font = QFont();
                fontInfo.fontColor = QColor(Qt::black);

                if (!obj->sbs_color.lineColor.isEmpty())
                    pen.setColor(QColor(obj->sbs_color.lineColor));

                if (!obj->sbs_color.fillColor.isEmpty())
                {
                    brush.setStyle(Qt::SolidPattern);
                    brush.setColor(QColor(obj->sbs_color.fillColor));
                }

                pattern.setPen(pen);
                pattern.setBrush(brush);
                pattern.setFont(fontInfo);

                mSubSystemStyles.insert(obj->name, pattern);
            }
        }
    }
}
