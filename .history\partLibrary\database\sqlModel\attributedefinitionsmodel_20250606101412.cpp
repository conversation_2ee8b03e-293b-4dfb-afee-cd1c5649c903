#include "attributedefinitionsmodel.h"

AttributeDefinitionsModel::AttributeDefinitionsModel(QObject *parent) : SqlModelBase(parent)
{
    reset();
}

void AttributeDefinitionsModel::fromJson(const QJsonObject &json)
{
    m_attributeUUID = safeGetValue<QString>(json, "AttributeUUID");
    m_name = safeGetValue<QString>(json, "Name");
    m_dataType = safeGetValue<QString>(json, "DataType");
    m_description = safeGetValue<QString>(json, "Description");
    m_irdi = safeGetValue<QString>(json, "IRDI");
    m_unit = safeGetValue<QString>(json, "Unit");
    m_defaultValue = safeGetValue<QString>(json, "DefaultValue");
    m_validationRule = safeGetValue<QString>(json, "ValidationRule");
    m_isUserDefined = safeGetValue<bool>(json, "IsUserDefined", false);
    m_isDeleted = safeGetValue<bool>(json, "IsDeleted", false);
}

QJsonObject AttributeDefinitionsModel::toJsonObject() const
{
    QJsonObject json;
    json["AttributeUUID"] = m_attributeUUID;
    json["Name"] = m_name;
    json["DataType"] = m_dataType;
    json["Description"] = m_description;
    json["IRDI"] = m_irdi;
    json["Unit"] = m_unit;
    json["DefaultValue"] = m_defaultValue;
    json["ValidationRule"] = m_validationRule;
    json["IsUserDefined"] = m_isUserDefined;
    json["IsDeleted"] = m_isDeleted;
    return json;
}

void AttributeDefinitionsModel::fromVariantMap(const QVariantMap &map)
{
    m_attributeUUID = safeGetValueFromMap<QString>(map, "AttributeUUID");
    m_name = safeGetValueFromMap<QString>(map, "Name");
    m_dataType = safeGetValueFromMap<QString>(map, "DataType");
    m_description = safeGetValueFromMap<QString>(map, "Description");
    m_irdi = safeGetValueFromMap<QString>(map, "IRDI");
    m_unit = safeGetValueFromMap<QString>(map, "Unit");
    m_defaultValue = safeGetValueFromMap<QString>(map, "DefaultValue");
    m_validationRule = safeGetValueFromMap<QString>(map, "ValidationRule");
    m_isUserDefined = safeGetValueFromMap<bool>(map, "IsUserDefined", false);
    m_isDeleted = safeGetValueFromMap<bool>(map, "IsDeleted", false);
}

QVariantMap AttributeDefinitionsModel::toVariantMap() const
{
    QVariantMap map;
    map["AttributeUUID"] = m_attributeUUID;
    map["Name"] = m_name;
    map["DataType"] = m_dataType;
    map["Description"] = m_description;
    map["IRDI"] = m_irdi;
    map["Unit"] = m_unit;
    map["DefaultValue"] = m_defaultValue;
    map["ValidationRule"] = m_validationRule;
    map["IsUserDefined"] = m_isUserDefined;
    map["IsDeleted"] = m_isDeleted;
    return map;
}

void AttributeDefinitionsModel::reset()
{
    m_attributeUUID.clear();
    m_name.clear();
    m_dataType.clear();
    m_description.clear();
    m_irdi.clear();
    m_unit.clear();
    m_defaultValue.clear();
    m_validationRule.clear();
    m_isUserDefined = false;
    m_isDeleted = false;
}

QString AttributeDefinitionsModel::getAttributeUUID() const { return m_attributeUUID; }
void AttributeDefinitionsModel::setAttributeUUID(const QString &attributeUUID) { m_attributeUUID = attributeUUID; }
QString AttributeDefinitionsModel::getName() const { return m_name; }
void AttributeDefinitionsModel::setName(const QString &name) { m_name = name; }
QString AttributeDefinitionsModel::getDataType() const { return m_dataType; }
void AttributeDefinitionsModel::setDataType(const QString &dataType) { m_dataType = dataType; }
QString AttributeDefinitionsModel::getDescription() const { return m_description; }
void AttributeDefinitionsModel::setDescription(const QString &description) { m_description = description; }
QString AttributeDefinitionsModel::getIrdi() const { return m_irdi; }
void AttributeDefinitionsModel::setIrdi(const QString &irdi) { m_irdi = irdi; }
QString AttributeDefinitionsModel::getUnit() const { return m_unit; }
void AttributeDefinitionsModel::setUnit(const QString &unit) { m_unit = unit; }
QString AttributeDefinitionsModel::getDefaultValue() const { return m_defaultValue; }
void AttributeDefinitionsModel::setDefaultValue(const QString &defaultValue) { m_defaultValue = defaultValue; }
QString AttributeDefinitionsModel::getValidationRule() const { return m_validationRule; }
void AttributeDefinitionsModel::setValidationRule(const QString &validationRule) { m_validationRule = validationRule; }
bool AttributeDefinitionsModel::getIsUserDefined() const { return m_isUserDefined; }
void AttributeDefinitionsModel::setIsUserDefined(bool isUserDefined) { m_isUserDefined = isUserDefined; }
bool AttributeDefinitionsModel::getIsDeleted() const { return m_isDeleted; }
void AttributeDefinitionsModel::setIsDeleted(bool isDeleted) { m_isDeleted = isDeleted; } 