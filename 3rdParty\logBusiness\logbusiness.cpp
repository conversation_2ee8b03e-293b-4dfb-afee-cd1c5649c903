﻿#include "logbusiness.h"


#include <sys/stat.h>
#include <fstream>
#include <functional>
#include <QFileInfo>
#include <QDebug>
#include <QDir>
#include <QCoreApplication>

INITIALIZE_EASYLOGGINGPP

std::mutex logBusiness::mMutex;

logBusiness *logBusiness::mInstance = nullptr;

logBusiness *logBusiness::getInstance()
{
    if(mInstance == nullptr)
    {
        mMutex.lock();
        if(mInstance == nullptr)
            mInstance = new logBusiness();
        mMutex.unlock();
    }

    return mInstance;
}

logBusiness::~logBusiness()
{


}

void logBusiness::setFileMaxSize(const int &size)
{
    mFileMaxSize = size;
}


logBusiness::logBusiness()
{
    // 将工作目录切换到程序根目录，否则双击工程文件启动程序会在工程目录内生成日志文件
    QString appPath = QCoreApplication::applicationDirPath();
    QDir::setCurrent(appPath);

    if(!vxFile::isExist(mConfFilePath))
    {
        //手动配置配置文件属性
        buildConf();
    }

    el::Configurations conf(mConfFilePath);

    el::Configuration *obj = conf.get(el::Level::Global, el::ConfigurationType::Filename);
    if(obj)
    {
        mCurFilePath = QString::fromStdString(obj->value());

        //创建文件夹
        QDir().mkpath(QFileInfo(mCurFilePath).absolutePath());
    }

    el::Loggers::reconfigureLogger("default", conf);

    el::Loggers::reconfigureAllLoggers(conf);

    /// 注冊回調函數
    el::Helpers::installPreRollOutCallback(std::bind(&logBusiness::logRolloutHandler, this, std::placeholders::_1, std::placeholders::_2));

    //MAX_LOG_FILE_SIZE 生效
    el::Loggers::addFlag(el::LoggingFlag::StrictLogFileSizeCheck);
}


void logBusiness::logRolloutHandler(const char* filename, std::size_t size)
{
    std::ostringstream tmpStr;
    std::ostringstream srcFile;
    std::ostringstream dstFile;
    //备份已有文件

    //如果最后一个文件存在则先删除
    tmpStr.str("");
    tmpStr << filename << "." << std::dec << mFileCount;
    if(vxFile::isExist(tmpStr.str()))
    {
        vxFile::del(tmpStr.str());
    }

    //重命名已存在
    for(int index = mFileCount - 1; index > 0; index--)
    {
        srcFile.str("");
        srcFile << filename << "." << std::dec << index;

        if(vxFile::isExist(srcFile.str()))
        {
            dstFile.str("");
            dstFile << filename << "." << std::dec << index + 1;

            vxFile::rname(srcFile.str(), dstFile.str());
        }
    }

    //最新重命名
    if(!vxFile::rname(filename, std::string(filename) + ".1"))
    {

    }
}

const QString &logBusiness::curFilePath() const
{
    return mCurFilePath;
}

void logBusiness::buildConf()
{
    std::ofstream ofs;
    ofs.open(mConfFilePath, std::ios::out);
    ofs << "* GLOBAL:\n\
FORMAT               =  \"[%datetime] [%fbase(%line)]%level: %msg\"\n\
FILENAME             =  \"./logs/";
    ofs << mLogerName << ".log\"\n";
    ofs << "ENABLED              =  true\n\
TO_FILE              =  true\n\
TO_STANDARD_OUTPUT   =  false\n\
MILLISECONDS_WIDTH   =  3\n\
PERFORMANCE_TRACKING =  false\n\
MAX_LOG_FILE_SIZE    =  ";
    ofs << std::to_string(mFileMaxSize) << "\n";
    ofs << "LOG_FLUSH_THRESHOLD  = 0\n";
    ofs.close();
}


/***********************/

#define OK  0
#define ERROR   -1
#define STATUS int


bool vxFile::isExist(const std::string &file)
{
    struct stat statbuf;
    STATUS res = stat(file.c_str(), &statbuf);

    return res == OK ? true : false;
}

bool vxFile::del(const std::string &file)
{
    STATUS res = remove(file.c_str());
    return res == OK ? true : false;
}

bool vxFile::mkFolder(const std::string &path)
{
#ifdef _WIN32
    STATUS res = mkdir(path.c_str());
#else
    STATUS res = mkdir(path.c_str(), S_IRUSR | S_IWUSR | S_IXUSR | S_IRWXG | S_IRWXO);
#endif
    return res == OK ? true : false;
}

bool vxFile::rname(const std::string &srcFile, const std::string &dstFile)
{
    STATUS res = rename(srcFile.c_str(), dstFile.c_str());
    return res == OK ? true : false;
}

unsigned long vxFile::size(const std::string &file)
{
    unsigned long size = 0;
    struct stat statbuf;
    STATUS res = stat(file.c_str(), &statbuf);
    if(res == OK)
    {
        size = statbuf.st_size;
    }
    return size;
}
