﻿#include "interfaceelement.h"

#include "../diagram.h"
#include "../qetxml.h"
#include "../qetgraphicsitem/dynamicelementtextitem.h"
#include "../qetgraphicsitem/terminal.h"
#include "../qetgraphicsitem/conductor.h"
#include "equipmentelement.h"

#include "../adeconnectrelation.h"
#include "../elementeventdockinterface.h"

#include "../undocommand/customCommand/customifmovecommand.h"

#include "sql/sqlCtrl/interfacectrl.h"
#include "../ElementsCollection/xmlelementcollection.h"

using namespace ADE::Diagram;

InterfaceElement::InterfaceElement(const ElementsLocation &location,
                                   int *state, Element *parentElement,
                                   const QString &uuid) :
      Element(location, nullptr, state, Element::Simple, uuid, parentElement)
{
    // 读取接口类型属性
    updateInterfaceType();

    // 重设大小尺寸
    adjustSize();

    // 当文本内容被改变时调整居中
    for (auto dti : dynamicTextItems())
    {
        connect(dti, &DynamicElementTextItem::plainTextChanged, [=](){ dti->adjustSize();});
    }

    // 更新ADE父对象uuid信息
    updateADEFatherUuid();
    connect(this, &InterfaceElement::parentChanged, [=](){
        updateADEFatherUuid();
    });
}

InterfaceElement::~InterfaceElement()
{

}

bool InterfaceElement::fromXml(QDomElement& e, QHash<int, Terminal*>& table_id_adr)
{
    bool res = Element::fromXml(e, table_id_adr);

    if (res)
    {
        if (e.hasAttribute("ADEFatherUuid"))
            mADEFatherUuid = e.attribute("ADEFatherUuid");

        // 设置ADE文本显示
        for (auto dti : dynamicTextItems())
        {
            ADETextProperties textProp = ADETextProperties(dti->text());
            dti->setTextProperty(textProp);

            // 文本被修改后自动重新居中(新增的文本对象需重新连接槽函数)
            // Note: In order to align HTML text in the center, the item's text width must be set.
            // Otherwise, you can call adjustSize() after setting the item's text.
            connect(dti, &DynamicElementTextItem::plainTextChanged, [=](){
                dti->adjustSize();
            });
        }
    }

    return res;
}

QDomElement InterfaceElement::toXml(QDomDocument& document,
                                    QHash<Terminal*, int>& table_adr_id) const
{
    // 先在Element基类中创建xml元素, 再补充自定义信息
    QDomElement elmtXml = Element::toXml(document, table_adr_id);

    if (!mADEFatherUuid.isEmpty())
        elmtXml.setAttribute("ADEFatherUuid", mADEFatherUuid);

    return elmtXml;
}

void InterfaceElement::paint(
        QPainter* painter,
        const QStyleOptionGraphicsItem* options,
        QWidget*w)
{
    if (m_searchResulr_highlight)
    {
        drawHighlight(painter, options, m_highLight_yellow);
    }

    // 拖动接口元素时显示颜色提示
    if (m_moving_state && m_move_on_rect)
    {
        if (m_move_valid)  drawHint(painter, options, Element::HintCorrect);
        else               drawHint(painter, options, Element::HintWrong);
    }

    Element::paint(painter, options, w);
}

void InterfaceElement::changeLocation(const ElementsLocation &newLocation)
{
    Element::changeLocation(newLocation);

    // 更新接口类型定义
    updateInterfaceType();
}

void InterfaceElement::mousePressEvent(QGraphicsSceneMouseEvent* event)
{
    //qDebug() << Q_FUNC_INFO << pos();

    if (mActiveTriggerEvent)
    {
        if (event->modifiers() != ElementEventDockInterface::redockKeyModifier())
        {
            // 点击与设备上接口对接的电缆上接口时转移event处理
            if (!parentElement()
                    && connectRelation()
                    && connectRelation()->getAnotherInterfaceElement(this)
                    && connectRelation()->getAnotherInterfaceElement(this)->parentElement())
            {
                connectRelation()->getAnotherInterfaceElement(this)->passiveTriggerMousePressEvent(event);
                QetGraphicsItem::mousePressEvent(event);
                return;
            }
        }
    }

    if (event->button() == Qt::LeftButton)
    {
        m_first_move = true;
        m_first_move_pos = qMakePair(pos(), rotation());
        m_move_valid = true;
        m_move_on_rect = false;
    }

    // 鼠标点击电缆上接口元素时触发事件管理
    if (!parentElement())
    {
        if (!mElementEvent)
            mElementEvent = new ElementEventDockInterface(this);
        mElementEvent->mousePressEvent(event);
    }

    if (mActiveTriggerEvent)
    {
        // 主动触发点击事件时才选中
        QetGraphicsItem::mousePressEvent(event);
    }
}

void InterfaceElement::mouseMoveEvent(QGraphicsSceneMouseEvent* event)
{
    if (diagram()->isContentReadOnly())
        return;

    if (!isMovable())
        return;

    // 按下Alt键时可拖动断开接口连接
    if (event->modifiers() == ElementEventDockInterface::redockKeyModifier())
    {
        if (mElementEvent && mElementEvent->redockMode())
        {
            mElementEvent->redockMouseMoveEvent(event);
            return;
        }
    }

    // 对于和设备上接口对接的电缆上接口，由设备上接口移动带动自身移动
    if (!parentElement()
            && connectRelation()
            && connectRelation()->getAnotherInterfaceElement(this)
            && connectRelation()->getAnotherInterfaceElement(this)->parentElement())
    {
        // 转移event处理
        connectRelation()->getAnotherInterfaceElement(this)->mouseMoveEvent(event);
        return;
    }

    // 只有鼠标左键才能移动元素
    if (!(event->buttons() & Qt::LeftButton))
        return;

    // 记录移动状态
    if (!m_moving_state)
        m_moving_state = true;

    if (parentElement()) // 元素为设备上的接口时限制其移动范围
    {
        m_move_on_rect = true;

        // 设置父节点高亮
        if((event->buttons() & Qt::LeftButton) && (flags() & ItemIsMovable))
        {
            if(m_first_move)
                parentElement()->setHighlighted(true);
        }

        QPointF bfmPos;
        QPointF afmPos;

        int pHeight = 0;
        int pWidth  = 0;
        int halfW = 0;
        int halfH = 0;

        bfmPos = pos();
        afmPos = mapToParent(event->pos());

        pHeight = parentElement()->size().height();
        pWidth  = parentElement()->size().width();
        halfW = (EQ_IF_WIDTH / 2);
        halfH = (EQ_IF_HEIGHT / 2);

        int pHalfW = (int)(pWidth / 2);
        int pHalfH = (int)(pHeight / 2);

        /*
                          +
                          |
                          |
                  +---------------+
                  |       |       |
                  |       |       |
            +---------------------------->
                  |       |       |      X
                  |       |       |
                  +---------------+
                          |
                        Y |
                          v
            */
        // wm: 20220916 限制子元素移动范围
        if (bfmPos != afmPos)
        {
            // 初始化位置
            QPointF calcPos = bfmPos;
            int calcRotation = this->rotation();

            // 限制横向移动
            if (abs((int)(bfmPos.y() * 2)) > pHeight)
            {
                if (afmPos.x() > pHalfW)
                {
                    if ((afmPos.y() >= 0) && (afmPos.y() < pHalfH))
                    {
                        calcPos = QPointF(pHalfW + halfH, pHalfH - halfW); // south->east
                        calcRotation = 90;
                        qDebug() << "south->east"<< parentElement()->size() << calcPos;
                    }
                    else if ((afmPos.y() <= 0) && (afmPos.y() > -pHalfH))
                    {
                        calcPos = QPointF(pHalfW + halfH, -pHalfH + halfW); // north->east
                        calcRotation = 90;
                        qDebug() <<"north->east"<< parentElement()->size() << calcPos;
                    }
                    else
                    {
                        qDebug() << "hor mov miss match" << afmPos << pHalfH;
                    }
                }
                else if (afmPos.x() < -pHalfW)
                {
                    if ((afmPos.y() >= 0) && (afmPos.y() < pHalfH))
                    {
                        calcPos = QPointF(-(pHalfW + halfH), pHalfH - halfW);   // south->west
                        calcRotation = 270;
                        qDebug() << "south->west" << parentElement()->size() << calcPos;
                    }
                    else if ((afmPos.y() <= 0) && (afmPos.y() > -pHalfH))
                    {
                        calcPos = QPointF(-(pHalfW + halfH), -(pHalfH - halfW)); // north->west
                        calcRotation = 270;
                        qDebug() << "north->west"<< parentElement()->size() << calcPos;
                    }
                }
                else
                {
                    calcPos = QPointF(afmPos.x(), bfmPos.y());
                    qDebug() << "hor move：" << "calcPos" << calcPos << parentElement()->size() <<bfmPos <<  afmPos;
                }
            }

            // 限制纵向移动
            if (abs((int)(bfmPos.x() * 2)) > pWidth)
            {
                if (afmPos.y() > pHalfH)
                {
                    if ((afmPos.x() >= 0) && (afmPos.x() <= pHalfW))
                    {
                        calcPos = QPointF(pHalfW - halfW, pHalfH + halfH);     // east->south
                        calcRotation = 180;
                        qDebug() <<     "east->south"<< parentElement()->size() << calcPos;
                    }
                    else if ((afmPos.x() <= 0) && (afmPos.x() >= -pHalfW))
                    {
                        calcPos = QPointF(-(pHalfW - halfW), pHalfH + halfH);  // west->south
                        calcRotation = 180;
                        qDebug() <<  "west->south"<< parentElement()->size() << calcPos;
                    }
                }
                else if (afmPos.y() < -pHalfH)
                {
                    if ((afmPos.x() >= 0) && (afmPos.x() <= pHalfW))
                    {
                        calcPos = QPointF(pHalfW - halfW, -(pHalfH + halfH));    // east->north
                        calcRotation = 0;
                        qDebug() <<  "east->north"<< parentElement()->size() << calcPos;
                    }
                    else if ((afmPos.x() <= 0) && (afmPos.x() >= -pHalfW))
                    {
                        calcPos = QPointF(-(pHalfW - halfW), -(pHalfH + halfH));  // west->north
                        calcRotation = 0;
                        qDebug() <<  "west->north"<< parentElement()->size() << calcPos;
                    }
                }
                else
                {
                    calcPos = QPointF(bfmPos.x(), afmPos.y());
                    qDebug() << "ver move：" << "calcPos" << calcPos << parentElement()->size() <<bfmPos <<  afmPos;
                }
            }      

            // 设置位置
            this->setRotation(calcRotation);

            // 将计算后的坐标吸附到网格
            {
                // 根据接口方向决定吸附网格的轴
                Qet::Orientation relOri = this->relativeOrientation();

                // 当接口位于边缘时不吸附网格，防止出现错位问题
                bool nearEdge = false;
                qreal safeDist = Diagram::xGrid + halfW;
                if (relOri == Qet::North || relOri == Qet::South)
                {
                    if ( std::abs( std::abs(calcPos.x()) - pHalfW ) < safeDist)
                    {
                        nearEdge = true;
                    }
                }
                else if (relOri == Qet::West || relOri == Qet::East)
                {
                    if ( std::abs( std::abs(calcPos.y()) - pHalfH ) < safeDist)
                    {
                        nearEdge = true;
                    }
                }

                if (!nearEdge)
                {
                    Qet::Orientation sceOri = this->sceneOrientation();

                    QPointF sceneCalcPos = parentElement()->mapToScene(calcPos);
                    if (sceOri == Qet::North || sceOri == Qet::South)
                        sceneCalcPos = diagram() ? diagram()->xPosSnapToGrid(sceneCalcPos) : sceneCalcPos;
                    else if (sceOri == Qet::West || sceOri == Qet::East)
                        sceneCalcPos = diagram() ? diagram()->yPosSnapToGrid(sceneCalcPos) : sceneCalcPos;
                    calcPos = parentElement()->mapFromScene(sceneCalcPos);
                }
            }

            this->setPos(QPointF(calcPos.x() , calcPos.y()), false);

            // 显示提示
            m_move_valid = isElementMoveValid(calcPos);
            update();
        }
    }
    else                 // 电缆上的接口移动时自动与其它接口对接
    {
        QetGraphicsItem::mouseMoveEvent(event);

        if (mElementEvent)
            mElementEvent->mouseMoveEvent(event);
    }

    // 更新导线路径
    for (Conductor* c : conductors())
        c->updatePath();

    foreach (Terminal* t, terminals())
    {
        t -> drawHelpLine(true);
    }

    // 记录鼠标拖动元素的状态
    if (m_first_move)
        m_first_move = false;
}

void InterfaceElement::mouseReleaseEvent(QGraphicsSceneMouseEvent* event)
{
    // 转移事件
    if (!parentElement()
            && connectRelation()
            && connectRelation()->getAnotherInterfaceElement(this)
            && connectRelation()->getAnotherInterfaceElement(this)->parentElement())
    {
        // 转移event处理
        connectRelation()->getAnotherInterfaceElement(this)->mouseReleaseEvent(event);
        return;
    }
    //--------

    if (mElementEvent)
    {
        diagram()->elementsMover().setMovment(mElementEvent->validMovment());
    }


    QetGraphicsItem::mouseReleaseEvent(event);

    // 鼠标左键使元素移动时才执行后续操作
    if (event->button() != Qt::LeftButton)
        return;

    foreach (Terminal* t, terminals())
    {
        t -> drawHelpLine(false);
    }

    if (parentElement())
    {
        parentElement()->setHighlighted(false);
    }

    bool releaseIsOk = false;

    // 判断移动后的接口元素位置是否合法
    if (m_move_on_rect)
    {
        if (!isElementMoveValid(pos().toPoint()))
        {
            setPos(m_first_move_pos.first, false); // 恢复之前位置，不对齐网格
            setRotation(m_first_move_pos.second);

            for (Conductor* c : this->conductors())
            {
                c->updatePath();
            }
        }
        else
        {
            releaseIsOk = true;
        }
    }

    m_moving_state = false;
    update();

    if (releaseIsOk)
    {
        //add xqx 2023-5-17 undo功能，首次 redo不执行
        this->diagram()->undoStack().push(
            new customIfMoveCommand(
                this->diagram(),
                this,
                m_first_move_pos.first,
                pos(),
                m_first_move_pos.second,
                rotation()
            )
        );
    }

    if (mElementEvent)
    {
        mElementEvent->mouseReleaseEvent(event);
        delete mElementEvent;
        mElementEvent = nullptr;
    }
}

void InterfaceElement::hoverEnterEvent(QGraphicsSceneHoverEvent *event)
{
    //鼠标悬浮+ctrl 显示接口类型
    if ( QApplication::keyboardModifiers() == Qt::ControlModifier)
    {
        interfaceCtrl *ctrl = Interface_Factory->getCtrlObj(this->diagram()->ade()->prjUuid());
        if (!ctrl)
            return;

        interfaceList ifList = ctrl->fetchAll();

        QStringList categorys;
        if (ifList.isEmpty())
            return;

        for (QString uuid : ifList.keys())
        {
            if (uuid == this->uuid().toString())
                categorys = ifList.value(uuid)->getCategoryFromJson();
        }

        QString text;
        for (int index = 0; index < categorys.size(); index++)
        {
            text.append(categorys.at(index));
            if (index != (categorys.size() - 1))
                text.append("\n");
        }
        setToolTip(text);
    }

    Element::hoverEnterEvent(event);
}

void InterfaceElement::updateInterfaceType()
{
    if (m_location.xml().hasAttribute("ADEInterfaceType"))
    {
        m_interface_type = (ADE::Diagram::InterfaceElementType)m_location.xml().attribute("ADEInterfaceType").toInt();
    }
}

void InterfaceElement::adjustSize()
{
    int smallSizeThreshold = 30;
    int wid = size().width();
    int hei = size().height();
    if (wid < smallSizeThreshold)
    {
        while (wid % 10)
            ++ wid;
    }
    if (hei < smallSizeThreshold)
    {
        while (hei % 10)
            ++ hei;
    }
    setSize(wid, hei);
    // 调整尺寸后更新热点
    setHotspot(hotspot());
}

QString InterfaceElement::adeMark() const
{
    if (dynamicTextItems().count())
    {
        ADETextProperties prop = dynamicTextItems().first()->getTextProp();
        return prop.getContentByTextName("代号");
    }
    return QString();
}

QString InterfaceElement::adeName() const
{
    if (dynamicTextItems().count())
    {
        ADETextProperties prop = dynamicTextItems().first()->getTextProp();
        return prop.getContentByTextName("名称");
    }
    return QString();
}

bool InterfaceElement::isCitedInterface()
{
    return diagram()->ade()->isInterfaceElementCited(this);
}

bool InterfaceElement::isDirectConnectInterface() const
{
    return parentElement() ? true : false;
}

void InterfaceElement::setADEFatherUuid(const QString &uuid)
{
    mADEFatherUuid = uuid;
}

void InterfaceElement::setCable(ADELogicCable *cable)
{
    mCable = cable;
}

void InterfaceElement::setConnectRelation(ADEConnectRelation *cr)
{
    mConnectRelation = cr;
}

bool InterfaceElement::canRotate() const
{
    if (parentElement())
        return false;

    // 当对接的接口有父元素时禁止旋转
    if (connectRelation()
            && connectRelation()->getAnotherInterfaceElement(this)
            && connectRelation()->getAnotherInterfaceElement(this)->parentElement())
        return false;

    return true;
}

void InterfaceElement::passiveTriggerMousePressEvent(QGraphicsSceneMouseEvent* event)
{
    mActiveTriggerEvent = false;
    mousePressEvent(event);
    mActiveTriggerEvent = true;
}

void InterfaceElement::triggerQETMouseMoveEvent(QGraphicsSceneMouseEvent *event)
{
    QetGraphicsItem::mouseMoveEvent(event);
}

void InterfaceElement::adjustTextPosition()
{
    // 调整接口文本位置，防止与接口元素重合
    if (m_dynamic_text_list.isEmpty())
        return;

    if (!diagram())
        return;

    DynamicElementTextItem *textItem = m_dynamic_text_list.first();

    // 根据接口旋转角度调整文本位置
//    {
//        QSizeF textSize = textItem->document()->size();
//        int offset = EQ_IF_HEIGHT / 2;

//        Qet::Orientation ifElmtOri = relativeOrientation();
//        QPointF offsetPos;
//        if (parentElement())  // 设备上的接口
//        {
//            if      (ifElmtOri == Qet::North) {offsetPos = QPointF(-(textSize.width()  / 2),  -(textSize.height() + offset));}
//            else if (ifElmtOri == Qet::East)  {offsetPos = QPointF(-(textSize.width()  / 2), 0);}
//            else if (ifElmtOri == Qet::South) {offsetPos = QPointF( (textSize.width()  / 2), 0);}
//            else if (ifElmtOri == Qet::West)  {offsetPos = QPointF( (textSize.height() / 2),  -(textSize.width() + offset));}
//        }
//        else                  // 电缆上的接口
//        {
//            offsetPos = QPointF(-(textSize.width() / 2),  offset);

//            if      (ifElmtOri == Qet::North) {offsetPos = QPointF(-(textSize.width()  / 2), 0);}
//            else if (ifElmtOri == Qet::East)  {offsetPos = QPointF(-(textSize.height() / 2), -offset);}
//            else if (ifElmtOri == Qet::South) {offsetPos = QPointF( (textSize.width()  / 2), textSize.height() + offset);}
//            else if (ifElmtOri == Qet::West)  {offsetPos = QPointF( (textSize.height() / 2),  0);}
//        }

//        textItem->setPos(offsetPos);
//    }

    // 当文本与其它元素碰撞时继续调整
    {
        if (diagram()->collidingItems(textItem).isEmpty())
            return;

        QPointF pos = textItem->scenePos();
        int offset = EQ_IF_WIDTH;

        Qet::Orientation ori = relativeOrientation();
        if      (ori == Qet::North) pos = QPointF(pos.x(), pos.y() - offset);
        else if (ori == Qet::West)  pos = QPointF(pos.x() + offset, pos.y());
        else if (ori == Qet::South) pos = QPointF(pos.x() , pos.y() + offset);
        else if (ori == Qet::East)  pos = QPointF(pos.x() - offset , pos.y());

        QPointF itemPos = textItem->mapFromScene(pos);
        itemPos = textItem->mapToParent(itemPos);

        textItem->setPos(itemPos);
    }
}

bool InterfaceElement::isElementMoveValid(const QPointF &pos)
{
    Element *selfParent  = parentElement();

    QPointF relativePos;
    int pHeight = 0;
    int pWidth  = 0;

    if (selfParent)
    {
        relativePos = pos;
        pHeight = parentElement()->size().height();
        pWidth  = parentElement()->size().width();
    }

    int pHalfW = (int)(pWidth / 2);
    int pHalfH = (int)(pHeight / 2);

    // 获取边角点位
    QList<QPointF> calcPoints;
    calcPoints << QPointF(-pHalfW, -pHalfH) << QPointF(pHalfW, -pHalfH)
               << QPointF(pHalfW, pHalfH) << QPointF(-pHalfW, pHalfH);

    // 判断与设备边角的距离是否合法
    for (QPointF point : calcPoints)
    {
        qreal distance = sqrt( pow(relativePos.x() - point.x(), 2) + pow(relativePos.y() - point.y(), 2) );
        if (distance < ((EQ_IF_WIDTH / 2) + EQ_IF_SPACING))
            return false;
    }

    // 获取其它接口元素点位
    QList<QPointF> ifPoints;
    if (EquipmentElement *pElmt = element_cast<EquipmentElement*>(parentElement()))
    {
        for (Element* siblingEle : pElmt->childElements())
        {
            if (siblingEle == this)
                continue;

            ifPoints << siblingEle->pos();
        }
    }

    // 判断与点位的距离是否合法
    QPointF interfecePos = relativePos;

    for (QPointF point : ifPoints)
    {
        qreal distance = sqrt( pow(interfecePos.x() - point.x(), 2) + pow(interfecePos.y() - point.y(), 2) );
        if (distance < (EQ_IF_WIDTH + EQ_IF_SPACING))
            return false;
    }

    return true;
}

void InterfaceElement::updateADEFatherUuid()
{
    if (parentElement())
    {
        setADEFatherUuid(parentElement()->uuid().toString());
    }
}

void InterfaceElement::setSearchResultHighLight(bool hl)
{
    m_searchResulr_highlight = hl;
    update();
}

void InterfaceElement::updateDefinition(const QString &definition)
{
    if (definition.isEmpty())
        return;
    QDomDocument xml_document;
    xml_document.setContent(definition);
    QDomElement root = xml_document.documentElement();

    // 替换元素和端子uuid
    QDomElement oldElementDefinitionXml = this->location().xml();
    QString oldUuid = oldElementDefinitionXml.firstChildElement("uuid").attribute("uuid");
    QDomElement oldDescriptionXml = oldElementDefinitionXml.firstChildElement("description");
    QList<QDomElement> oldTerminalDomElmts = QET::findInDomElement(oldDescriptionXml, "terminal");

    root.firstChildElement("uuid").setAttribute("uuid", oldUuid);
    QList<QDomElement> newTerminalDomElmts = QET::findInDomElement(root.firstChildElement("description"), "terminal");
    for (auto dom : newTerminalDomElmts)
        root.firstChildElement("description").removeChild(dom);
    for (auto dom : oldTerminalDomElmts)
        root.firstChildElement("description").appendChild(dom);

    QDomDocument qdoc;
    QDomElement tmpEle = qdoc.createElement("element");
    tmpEle.setAttribute("name", QString("%1").arg(this->location().fileName()));
    tmpEle.appendChild(root);

    // 替换Element xml描述信息
    ElementsLocation newLo = this->location().project()->embeddedElementCollection()->replaceElementDefinition(this->location(), tmpEle);
    this->changeLocation(newLo);
}
