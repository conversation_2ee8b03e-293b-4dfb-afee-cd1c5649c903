#ifndef CATEGORIESVIEWMODEL_H
#define CATEGORIESVIEWMODEL_H

#include "../globalDb/sqlModel/categoriesmodel.h"
// #include "../globalDb/sqlModel/categoryattributelinksmodel.h"
#include "../globalDb/sqlModel/attributedefinitionsmodel.h"

#include <QList>
#include <QString>
#include <QVariant>
#include <QStringList>

/**
 * @brief 用于UI展示的、被"丰富化"的单个分类属性的模型
 */
struct CategoryAttributeViewModel
{
    // 来自 CategoryAttributeLinksModel 的数据
    QString linkId;             ///< 链接ID
    QString defaultValue;       ///< 默认值

    // 来自 AttributeDefinitionsModel 的数据
    QString attributeId;        ///< 属性ID
    QString irdi;               ///< IRDI
    QString name;               ///< 属性名称
    AttributeDefinitionsModel::AttrDataType dataType;  ///< 数据类型
    QString description;        ///< 属性描述
    QString unit;               ///< 单位
    QStringList options;        ///< 枚举选项（当dataType为Enumeration时）
    QString validationRule;     ///< 验证规则
    bool isUserDefined;         ///< 是否用户定义

    /**
     * @brief 模板化的Getter，用于安全、便捷地获取具体类型的默认值。
     * @tparam T            期望获取的数据类型 (如 QString, int, bool, double)。
     * @return              转换后的值。如果无法转换，则返回T类型的默认值 (如0, ""空字符串等)。
     * @note                该函数的核心优势在于提供了更高级的抽象，而非简单地替代 toDouble() 等函数：
     * 1.  **封装性**: 向调用者隐藏了内部使用QString存储默认值的实现细节。
     * 2.  **健壮性**: 内部统一处理了类型转换的安全性检查，避免在多处重复编写错误处理逻辑。
     * 3.  **一致性**: 提供了统一的API入口(as<T>)来获取所有类型的值，而非多个不同的toType()函数。
     * 4.  **可维护性**: 如果未来更换内部存储方式，只需修改此函数实现，所有调用方代码无需变动。
     */
    template<typename T>
    T getDefaultValueAs() const
    {
        QVariant variant(defaultValue);
        if (variant.canConvert<T>()) {
            return variant.value<T>();
        }
        return T();
    }

    /**
     * @brief [新增] 为方便UI显示，提供一个统一转换为字符串的接口
     * @return 默认值的字符串表示形式
     */
    QString toDisplayString() const
    {
        return defaultValue;
    }
};

/**
 * @brief 最终提供给UI使用的、完整的分类视图模型
 */
struct CategoriesViewModel
{
    // 直接从 CategoriesModel 复制过来的基础信息
    QString id;                     ///< 分类UUID
    QString name;                   ///< 分类名称
    QString description;            ///< 分类描述
    QString parentCategoryUUID;     ///< 父分类UUID
    QString irdi;                   ///< IRDI
    QList<CategoriesModel::UsageUnitInfo> usageUnits;

    // 使用被丰富化的属性模型列表
    QList<CategoryAttributeViewModel> attributes;

    /**
     * @brief 判断当前的viewModel是否有效
     * @return true表示有效，false表示无效（id为空）
     */
    bool valid() const { return !id.isEmpty(); }

    /**
     * @brief 根据属性名称查找属性视图模型
     * @param attributeName 属性名称
     * @return 找到的属性视图模型指针，如果未找到则返回nullptr
     */
    const CategoryAttributeViewModel* findAttributeByName(const QString& attributeName) const
    {
        for (const auto& attr : attributes) {
            if (attr.name == attributeName) {
                return &attr;
            }
        }
        return nullptr;
    }

    /**
     * @brief 根据属性ID查找属性视图模型
     * @param attributeId 属性ID
     * @return 找到的属性视图模型指针，如果未找到则返回nullptr
     */
    const CategoryAttributeViewModel* findAttributeById(const QString& attributeId) const
    {
        for (const auto& attr : attributes) {
            if (attr.attributeId == attributeId) {
                return &attr;
            }
        }
        return nullptr;
    }
};

#endif // CATEGORIESVIEWMODEL_H
