#ifndef CONTACTORMOVEEVENTINTERFACE_H
#define CONTACTORMOVEEVENTINTERFACE_H

#include "CustomElements/contactorelement.h"
#include "portsnappointitem.h"
#include "3rdParty/qet/sources/CustomExtend/regionrectitem.h"

#include <QObject>
#include <QPointF>

class ContactorElement;
class ConductorVertexItem;

/**
 * @brief The ContactorMoveEventInterface class    点位元素移动事件，限制在父元素上移动
 */
class ContactorMoveEventInterface : public QObject
{
    Q_OBJECT

public:
    explicit ContactorMoveEventInterface(ContactorElement* ctEle);
    ~ContactorMoveEventInterface();

public:
    void mousePressEvent       (QGraphicsSceneMouseEvent *event);
    void mouseMoveEvent        (QGraphicsSceneMouseEvent *event);
    void mouseReleaseEvent     (QGraphicsSceneMouseEvent *event);

private:
    void swapContactorElmtPos(Element *e1, Element *e2, QPointF e2Pos, bool lastSwitch);

    ContactorElement *getContactorElementByPos(const QPointF &pos) const;

    void updateExistElementPos();
    void recordPointPosToNameBeforeSwitch();

    void hideContactorMovedSnapItem();

    // 复制被移动接触件的初始导线走线
    void copyMoveContactorEleConductorsPath(CustomConductor* copy, CustomConductor* previewCdt);

private:
    ContactorElement* mContactorEle;
    QPointF mCurrentPos;
    qreal mSnapRange;
    QList<PortSnapPointItem *> mSnapPointItems;
    QList<QPointF> mSnapPointsPos;

    QList<QPair<QPointF, ContactorElement*>> mAllExistPointItems;

    QMap<Element*, qreal> mDistInfo;
    QMap<qreal, QPointF> mDistEmptyPoints;

    bool endSwap = true;

    // 开始交换前记录，交换后刷新，
    // 芯线连接图包含空点位，不包含接壳
    QMap<QPointF, QString> mPointPosRecord;   ///< <scenePos, pointName>
    QPointF mLastSwitchPointPos;             ///< 最后被交换的点位坐标
    Element* mLastSwitchPointElement = nullptr;

    RegionRectItem *mHighLightRect = nullptr;          ///< 最后被交换的点位高亮

    QRectF mUsefulRange;    ///< 可吸附有效范围，用于限制点位元素在父元素上的移动范围， 模型外框宽度*2
    ContactorElement* mDisableContactorEle = nullptr;
    RegionRectItem *mDisableContactorRect = nullptr;
    QList<QGraphicsLineItem*> mPreviewLinkLines;
    QList<CustomConductor*> mPreviewLinkConductors;

    QMap<ConductorVertexItem*, QPointF> mVertexInitialPos;
};

inline bool operator<(const QPointF &a, const QPointF &b) {
    if (a.x() != b.x()) {
        return a.x() < b.x(); // 比较 x 坐标
    }
    return a.y() < b.y(); // 如果 x 坐标相等，则比较 y 坐标
}
#endif // CONTACTORMOVEEVENTINTERFACE_H
