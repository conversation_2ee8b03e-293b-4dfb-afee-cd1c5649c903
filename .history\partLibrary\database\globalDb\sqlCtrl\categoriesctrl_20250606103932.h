#ifndef CATEGORIESCTRL_H
#define CATEGORIESCTRL_H

#include "sqlCtrlBase.h"
#include "../sqlModel/categoriesmodel.h"

//============================================================================
/// file
/// brief      元器件分类表控制器
/// author     wm
/// version    1.0
/// date       2025/06/05
/// todo
//============================================================================

class CategoriesCtrl : public SqlCtrlBase
{
public:
    CategoriesCtrl(const QSqlDatabase& db);
    ~CategoriesCtrl();

    // 针对数据模型的操作
    CategoriesModelList fetchAll() const;
    CategoriesModelPtr  fetch(const QString &id) const;
    bool modelExist(const QString &id) const;
    // end

    // 针对数据库表的操作
    bool insert(const CategoriesModelPtr &obj);
    bool modify(const CategoriesModelPtr &newValueObj);
    bool remove(const QString &id);
    // end

public:
    QString tableName()       const override {return "Categories";}
    QString primaryKeyField() const override {return "CategoryUUID";}
    bool    recordExist(const QString &id) const override;

private:
    QString tableSql()        const override;
    void loadTable();

private:
    CategoriesModelList mAllObj;
};

#define Categories_Factory objFactory<QString, CategoriesCtrl>::getInstance()

#endif // CATEGORIESCTRL_H 