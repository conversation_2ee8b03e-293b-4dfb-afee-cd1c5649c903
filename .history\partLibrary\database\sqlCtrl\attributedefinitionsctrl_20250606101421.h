#ifndef ATTRIBUTEDEFINITIONSCTRL_H
#define ATTRIBUTEDEFINITIONSCTRL_H

#include "sqlCtrlBase.h"
#include "../sqlModel/attributedefinitionsmodel.h"
#include <QSharedPointer>
#include <QList>

class AttributeDefinitionsCtrl : public SqlCtrlBase
{
    Q_OBJECT
public:
    explicit AttributeDefinitionsCtrl(const QString &dbPath, QObject *parent = nullptr);

    bool checkAndCreateTable() override;

    bool insert(const AttributeDefinitionsModel &model);
    bool update(const AttributeDefinitionsModel &model);
    bool softDelete(const QString &uuid);
    QSharedPointer<AttributeDefinitionsModel> getByUUID(const QString &uuid);
    QList<QSharedPointer<AttributeDefinitionsModel>> getAll();
    QList<QSharedPointer<AttributeDefinitionsModel>> getAllActive();
};

#endif // ATTRIBUTEDEFINITIONSCTRL_H 