#ifndef NOTIFYMSGWRAP_H
#define NOTIFYMSGWRAP_H

#include <QString>
#include <QMutex>

class NotifyManager;

#define NotifyMsg notifyMsgWrap::getInstance()

/**
 * @brief The notifyMsgWrap class   通知消息包装
 */

class notifyMsgWrap
{
public:
    /** 消息类型 **/
    enum MsgType{
        ErrMsg,     ///< 错误消息
        RightMsg,   ///< 正确消息
        WaringMsg   ///< 提醒消息
    };
public:
    static notifyMsgWrap *getInstance()
    {
        if(mInstance == nullptr)
        {
            mInstance = new notifyMsgWrap();
        }
        return mInstance;
    }

private:
    notifyMsgWrap();
    ~notifyMsgWrap();
public:
    /**
     * @brief showMsg   显示消息
     * @param msg       消息体
     * @param msgType      类型
     */
    void showMsg(const QString &msg, const MsgType &msgType = MsgType::RightMsg);
private:
    NotifyManager *mNotifyManager;
    QMutex mMsgMutex;
    static notifyMsgWrap *mInstance;
};



#endif // NOTIFYMSGWRAP_H
