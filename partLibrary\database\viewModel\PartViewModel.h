#ifndef PARTVIEWMODEL_H
#define PARTVIEWMODEL_H

#include "../partDb/sqlModel/partmodel.h"
#include "../globalDb/sqlModel/attributedefinitionsmodel.h"

#include <QList>
#include <QString>
#include <QVariant>
#include <QStringList>
#include <QDateTime>


/**
 * @brief 用于UI展示的、被"丰富化"的单个核心属性的模型
 */
struct PartAttributeViewModel
{
    // 来自 PartModel::CoreAttrInfo 的数据
    QVariant value;

    // 来自 AttributeDefinitionsModel 的数据
    QString id;
    QString irdi;
    QString name;
    AttributeDefinitionsModel::AttrDataType dataType;
    QString description;
    QString unit;

    /**
     * @brief 模板化的Getter，用于安全、便捷地获取具体类型的值。
     * * @tparam T            期望获取的数据类型 (如 QString, int, bool, double)。
     * @return              转换后的值。如果无法转换，则返回T类型的默认值 (如0, ""空字符串等)。
     * * @note                该函数的核心优势在于提供了更高级的抽象，而非简单地替代 toDouble() 等函数：
     * 1.  **封装性**: 向调用者隐藏了内部使用QVariant存储值的实现细节。
     * 2.  **健壮性**: 内部统一处理了类型转换的安全性检查，避免在多处重复编写错误处理逻辑。
     * 3.  **一致性**: 提供了统一的API入口(as<T>)来获取所有类型的值，而非多个不同的toType()函数。
     * 4.  **可维护性**: 如果未来更换内部存储方式(如std::any)，只需修改此函数实现，所有调用方代码无需变动。
     */
    template<typename T>
    T as() const
    {
        if (value.canConvert<T>()) {
            return value.value<T>();
        }
        return T();
    }

    /**
     * @brief [新增] 为方便UI显示，提供一个统一转换为字符串的接口
     * @return 值的字符串表示形式
     */
    QString toDisplayString() const
    {
        return value.toString();
    }
};

/**
 * @brief 最终提供给UI使用的、完整的元器件视图模型
 */
struct PartViewModel
{
    // 直接从 PartModel 复制过来的基础信息
    QString id;
    QString partNumber;
    QString categoryUUID;
    QString manufacturerUUID;
    QString description;
    QString lifecycleStatus;
    QStringList tags;
    QDateTime creationTimestamp;    ///< 创建时间
    QString createdBy;          ///< 创建者
    QDateTime lastModifiedTimestamp;///< 最后修改时间
    QString lastModifiedBy;     ///< 最后修改者
    int     revision;           ///< 修订版本号

    QList<PartModel::CustomAttrInfo> customAttributes;

    // 使用被丰富化的属性模型列表
    QList<PartAttributeViewModel> coreAttributes;

    // 判断当前的viewModel是否有效
    bool valid() const {return id.isEmpty();}
};


#endif // PARTVIEWMODEL_H 
