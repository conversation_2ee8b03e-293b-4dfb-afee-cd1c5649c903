#ifndef PARTIMAGEMODEL_H
#define PARTIMAGEMODEL_H

#include "../../sqlModelBase.h"
#include <QUuid>
#include <QByteArray>

class PartImageModel : public SqlModelBase
{
public:
    PartImageModel() : id(QUuid::createUuid().toString()), displayOrder(0), isDeleted(0) {}

public:
    // 模型属性
    QString   id;               ///< 图片记录自身的唯一标识符，UUID格式
    QString   partUuid;         ///< 指向本库中 `Parts` 表的元器件UUID
    QByteArray pixmap;          ///< 图片二进制数据
    QString   imageType;        ///< 图片类型，用于UI区分显示，如 'Thumbnail', 'PrimaryPhoto'
    int       displayOrder;     ///< 多张图片时的显示顺序
    QString   description;      ///< 图片的简短描述或标题
    int       isDeleted;        ///< 软删除标志，0表示未删除，1表示已删除

public:
    QString tableName() const override { return "PartImages"; }
    QString primaryKeyField() const override { return "ImageUUID"; }
    QVariantMap modelToDbMap() const override;
    void dbToModel(const QSqlRecord& record) override;

public:
    // 字段名称常量
    static const QString FIELD_IMAGE_UUID;
    static const QString FIELD_PART_UUID;
    static const QString FIELD_PIXMAP;
    static const QString FIELD_IMAGE_TYPE;
    static const QString FIELD_DISPLAY_ORDER;
    static const QString FIELD_DESCRIPTION;
    static const QString FIELD_IS_DELETED;
};

#endif // PARTIMAGEMODEL_H 