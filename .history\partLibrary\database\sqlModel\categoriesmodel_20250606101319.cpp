#include "categoriesmodel.h"

CategoriesModel::CategoriesModel(QObject *parent) : SqlModelBase(parent)
{
    reset();
}

void CategoriesModel::fromJson(const QJsonObject &json)
{
    m_categoryUUID = safeGetValue<QString>(json, "CategoryUUID");
    m_name = safeGetValue<QString>(json, "Name");
    m_description = safeGetValue<QString>(json, "Description");
    m_parentCategoryUUID = safeGetValue<QString>(json, "ParentCategoryUUID");
    m_irdi = safeGetValue<QString>(json, "IRDI");
    m_usageUnitJson = safeGetValue<QString>(json, "UsageUnit_JSON");
    m_isDeleted = safeGetValue<bool>(json, "IsDeleted", false);
}

QJsonObject CategoriesModel::toJsonObject() const
{
    QJsonObject json;
    json["CategoryUUID"] = m_categoryUUID;
    json["Name"] = m_name;
    json["Description"] = m_description;
    json["ParentCategoryUUID"] = m_parentCategoryUUID;
    json["IRDI"] = m_irdi;
    json["UsageUnit_JSON"] = m_usageUnitJson;
    json["IsDeleted"] = m_isDeleted;
    return json;
}

void CategoriesModel::fromVariantMap(const QVariantMap &map)
{
    m_categoryUUID = safeGetValueFromMap<QString>(map, "CategoryUUID");
    m_name = safeGetValueFromMap<QString>(map, "Name");
    m_description = safeGetValueFromMap<QString>(map, "Description");
    m_parentCategoryUUID = safeGetValueFromMap<QString>(map, "ParentCategoryUUID");
    m_irdi = safeGetValueFromMap<QString>(map, "IRDI");
    m_usageUnitJson = safeGetValueFromMap<QString>(map, "UsageUnit_JSON");
    m_isDeleted = safeGetValueFromMap<bool>(map, "IsDeleted", false);
}

QVariantMap CategoriesModel::toVariantMap() const
{
    QVariantMap map;
    map["CategoryUUID"] = m_categoryUUID;
    map["Name"] = m_name;
    map["Description"] = m_description;
    map["ParentCategoryUUID"] = m_parentCategoryUUID;
    map["IRDI"] = m_irdi;
    map["UsageUnit_JSON"] = m_usageUnitJson;
    map["IsDeleted"] = m_isDeleted;
    return map;
}

void CategoriesModel::reset()
{
    m_categoryUUID.clear();
    m_name.clear();
    m_description.clear();
    m_parentCategoryUUID.clear();
    m_irdi.clear();
    m_usageUnitJson.clear();
    m_isDeleted = false;
}

QString CategoriesModel::getCategoryUUID() const { return m_categoryUUID; }
void CategoriesModel::setCategoryUUID(const QString &uuid) { m_categoryUUID = uuid; }
QString CategoriesModel::getName() const { return m_name; }
void CategoriesModel::setName(const QString &name) { m_name = name; }
QString CategoriesModel::getDescription() const { return m_description; }
void CategoriesModel::setDescription(const QString &description) { m_description = description; }
QString CategoriesModel::getParentCategoryUUID() const { return m_parentCategoryUUID; }
void CategoriesModel::setParentCategoryUUID(const QString &uuid) { m_parentCategoryUUID = uuid; }
QString CategoriesModel::getIrdi() const { return m_irdi; }
void CategoriesModel::setIrdi(const QString &irdi) { m_irdi = irdi; }
QString CategoriesModel::getUsageUnitJson() const { return m_usageUnitJson; }
void CategoriesModel::setUsageUnitJson(const QString &json) { m_usageUnitJson = json; }
bool CategoriesModel::getIsDeleted() const { return m_isDeleted; }
void CategoriesModel::setIsDeleted(bool isDeleted) { m_isDeleted = isDeleted; } 