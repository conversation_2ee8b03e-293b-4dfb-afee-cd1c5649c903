#ifndef PARTLIBRARYMANAGER_H
#define PARTLIBRARYMANAGER_H

#include <QObject>
#include <QHash>
#include <QString>
#include <QSharedPointer> // 关键：包含智能指针头文件
#include <QSqlDatabase>

#include "database/globalDb/sqlModel/manufacturermodel.h"
#include "database/globalDb/sqlModel/categoriesmodel.h"
#include "database/partDb/sqlModel/libraryinfomodel.h"
#include "database/partDb/sqlModel/partmodel.h"
#include "database/partDb/viewModel/PartViewModel.h"

#define PLM PartLibraryManager::instance()

// --- 前向声明所有 Ctrl 类 ---
class ManufacturerCtrl;
class CategoriesCtrl;
class AttributeDefinitionsCtrl;
class CategoryAttributeLinksCtrl;
class LibraryInfoCtrl;
class PartCtrl;
class DatasheetsCtrl;
class PartDatasheetLinksCtrl;
class PartImagesCtrl;
class ChangeLogCtrl;
class SqlCtrlBase;

/**
 * @brief 全局库上下文，封装全局库的数据库连接和所有控制器
 */
struct GlobalLibraryContext {
    QSqlDatabase db;
    QSharedPointer<ManufacturerCtrl> manufacturerCtrl;
    QSharedPointer<CategoriesCtrl> categoriesCtrl;
    QSharedPointer<AttributeDefinitionsCtrl> attributeDefinitionsCtrl;
    QSharedPointer<CategoryAttributeLinksCtrl> categoryAttributeLinksCtrl;

    // QSharedPointer 自动处理拷贝和赋值
    GlobalLibraryContext() = default;
};

/**
 * @brief 单个零件库上下文，封装其数据库连接和所有控制器
 */
struct PartLibraryContext {
    QString libraryId; // 库的UUID，来自LibraryInfo表
    QString filePath;  // .ptlib 文件的完整路径

    // 优化：缓存从数据库加载的元信息，避免重复IO
    LibraryInfoModelPtr cachedInfo;

    QSqlDatabase db;
    QSharedPointer<LibraryInfoCtrl> libraryInfoCtrl;
    QSharedPointer<PartCtrl> partCtrl;
    QSharedPointer<DatasheetsCtrl> datasheetsCtrl;
    QSharedPointer<PartDatasheetLinksCtrl> partDatasheetLinksCtrl;
    QSharedPointer<PartImagesCtrl> partImagesCtrl;
    QSharedPointer<ChangeLogCtrl> changeLogCtrl;

    // QSharedPointer 自动处理拷贝和赋值
    PartLibraryContext() = default;

    QString dataSheetsFolder() const;
};

using GlobalLibraryPtr = QSharedPointer<GlobalLibraryContext>;
using PartLibraryPtr   = QSharedPointer<PartLibraryContext>;

class PartLibraryManager : public QObject
{
    Q_OBJECT
private:
    explicit PartLibraryManager(QObject *parent = nullptr);
    ~PartLibraryManager();

public:
    // 单例访问接口
    static PartLibraryManager* instance();

    // 禁用拷贝和赋值
    PartLibraryManager(const PartLibraryManager&) = delete;
    PartLibraryManager& operator=(const PartLibraryManager&) = delete;

public:
    /**
     * @brief 初始化管理器，加载所有元器件库
     * @return true-初始化成功, false-失败
     */
    bool initialize(QString &err);

    /**
     * @brief 关闭所有数据库连接，释放资源
     */
    void shutdown();

    /**
     * @brief 创建一个新的空白用户库
     * @param libraryName 库的显示名称, 不能包含系统文件夹名称中禁止的符号
     * @param[out] errorInfo 错误信息
     * @return 成功则返回新库的UUID, 否则返回空QString
     */
    QString createNewPartLibrary(const QString& libraryName,  QString& errorInfo);

    /**
     * @brief 删除一个指定的用户库
     * @param libraryId 要删除库的UUID
     * @return true-删除成功, false-失败
     */
    bool deletePartLibrary(const QString& libraryId);

    GlobalLibraryPtr globalLibrary() const { return m_globalContext; }
    PartLibraryPtr   getPartLibrary(const QString &id) const;
    PartLibraryPtr   officialPartLibrary() const;
    QList<PartLibraryPtr> getAllPartLibraries() const;

public:
    /**
     * @brief 获取一个零件的完整视图模型 (聚合了全局库信息)
     * @param libraryId 零件所在的库UUID
     * @param partId    零件自身的UUID
     * @return 完整的 PartViewModel
     */
    PartViewModel getPartViewModel(const QString& libraryId, const QString& partId);

    /**
     * @brief 获取指定库中所有零件的基础模型
     * @param libraryId 目标库的UUID
     * @return PartModel 列表
     */
    PartModelList getPartsInLibrary(const QString& libraryId) const;

    /**
     * @brief 获取全局唯一的制造商列表
     */
    QList<ManufacturerModelPtr> getManufacturers() const;

    /**
     * @brief 获取全局唯一的分类列表
     */
    QList<CategoriesModelPtr> getCategories() const;

private:
    bool loadGlobalLibrary(const QString& globalDbPath, QString &err);
    bool loadPartLibrary(const QString& ptlibPath, QString &err);

    // 检查数据库表是否存在，不存在时创建
    bool checkAndCreateTable(SqlCtrlBase *sqlCtrl, QString &err);
    // 元器件库的根目录
    QString rootPath();
    // 元器件数据库文件后缀
    QString partLibSuffix();

private:
    bool mInitialized;

    // 使用 QSharedPointer 管理全局库上下文
    QSharedPointer<GlobalLibraryContext> m_globalContext;

    // 使用 QSharedPointer 管理所有零件库上下文
    QHash<QString, QSharedPointer<PartLibraryContext>> m_partLibraries;

private:
    // 静态成员
    static QString GlobalDbFolder;
    static QString GlobalDbName;
    static QString TrashFolder;

    static QString LOGCAT;
};

#endif // PARTLIBRARYMANAGER_H
