#include "partImageModel.h"

const QString PartImageModel::FIELD_IMAGE_UUID = "ImageUUID";
const QString PartImageModel::FIELD_PART_UUID = "PartUUID";
const QString PartImageModel::FIELD_PIXMAP = "Pixmap";
const QString PartImageModel::FIELD_IMAGE_TYPE = "ImageType";
const QString PartImageModel::FIELD_DISPLAY_ORDER = "DisplayOrder";
const QString PartImageModel::FIELD_DESCRIPTION = "Description";
const QString PartImageModel::FIELD_IS_DELETED = "IsDeleted";

QVariantMap PartImageModel::modelToDbMap() const
{
    QVariantMap map;
    map.insert(FIELD_IMAGE_UUID, id);
    map.insert(FIELD_PART_UUID, partUuid);
    map.insert(FIELD_PIXMAP, pixmap);
    map.insert(FIELD_IMAGE_TYPE, imageType);
    map.insert(FIELD_DISPLAY_ORDER, displayOrder);
    map.insert(FIELD_DESCRIPTION, description);
    map.insert(FIELD_IS_DELETED, isDeleted);
    return map;
}

void PartImageModel::dbToModel(const QSqlRecord &record)
{
    QMap<QString, QVariant> infos = getRecordValues(record);

    if (infos.contains(FIELD_IMAGE_UUID)) this->id = infos[FIELD_IMAGE_UUID].toString();
    if (infos.contains(FIELD_PART_UUID)) this->partUuid = infos[FIELD_PART_UUID].toString();
    if (infos.contains(FIELD_PIXMAP)) this->pixmap = infos[FIELD_PIXMAP].toByteArray();
    if (infos.contains(FIELD_IMAGE_TYPE)) this->imageType = infos[FIELD_IMAGE_TYPE].toString();
    if (infos.contains(FIELD_DISPLAY_ORDER)) this->displayOrder = infos[FIELD_DISPLAY_ORDER].toInt();
    if (infos.contains(FIELD_DESCRIPTION)) this->description = infos[FIELD_DESCRIPTION].toString();
    if (infos.contains(FIELD_IS_DELETED)) this->isDeleted = infos[FIELD_IS_DELETED].toInt();
} 