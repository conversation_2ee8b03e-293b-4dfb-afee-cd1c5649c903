#include "adediagramcablestylemanager.h"

#include "3rdParty/qet/sources/qetgraphicsitem/customconductor.h"
#include "3rdParty/qet/sources/qetgraphicsitem/conductor.h"
#include "3rdParty/qet/sources/adecustom/adecabletextitem.h"
#include "3rdParty/qet/sources/adecustom/adelogiccable.h"
#include "../diagram.h"
#include "3rdParty/qet/sources/adecustom/adecablemanager.h"
#include "CustomElements/interfaceelement.h"
#include "layoutDiagram/diagramToProject/diagramtoprojecthandler.h"
#include "layoutDiagram/diagramToProject/diagraminterfacemgr.h"
#include "layoutDiagram/diagramToProject/diagramsignalmgr.h"
#include "sql/sqlCtrl/interfacectrl.h"
#include "sql/sqlModel/interface.h"
#include "sql/sqlModel/signalinfo.h"
#include "userLog/UserLogInstance.h"

#include "sql/sqlModel/projectmodelgetter.h"
#include "sql/sqlModel/projectinfo.h"
#include "layoutDiagram/highlightSignal/cablesignalhighlighter.h"

ADEDiagramCableStyleManager::ADEDiagramCableStyleManager(Diagram *diagram) :
    mDiagram(diagram)
{
    //默认样式
    QPen pen;
    pen.setStyle(Qt::SolidLine);
    pen.setColor(Qt::black);
    pen.setWidthF(ADE::Diagram::CB_BORDER_WIDTH);

    QBrush brush;
    brush.setStyle(Qt::NoBrush);

    AdeElementStyle::FontInfo fontInfo;
    fontInfo.font = QFont();
    fontInfo.fontColor = QColor(Qt::black);

    mDefaultNoStyle.setPen(pen);
    mDefaultNoStyle.setBrush(brush);
    mDefaultNoStyle.setFont(fontInfo);
    //end

    customConductorStyles.clear();
}

ADEDiagramCableStyleManager::~ADEDiagramCableStyleManager()
{

}

QDomElement ADEDiagramCableStyleManager::toXml(QDomElement &root)
{
    /*
        <diagramStyle>
            <customDiagramStyle currentStyle = "0" currentCableStyle = "0">
                <conductors>
                    <conductor uuid="{d081793b-9fea-48ad-aa64-deb728e317a9}">
                        <adeStyle>
                            <pen widthF="1" style="SolidLine" color="#000000" />
                        </adeStyle>
                    </conductor>
                </conductors>
    */

    QDomDocument xml_document = root.ownerDocument();

    //conductors
    QDomElement elements_dom = xml_document.createElement("conductors");
    if (!customConductorStyles.isEmpty())
    {
        for (QString uid : customConductorStyles.keys())
        {
            // 检验uuid元素是否存在于布置图,不存在的不保存xml
            if (!mDiagram->conductors().isEmpty())
            {
                bool isExist = false;
                for (auto cdt : mDiagram->conductors())
                {
                    if (cdt->uuid() == uid)
                    {
                        isExist = true;
                        break;
                    }
                }

                if (!isExist)
                    continue;
            }

            QDomElement element_dom = xml_document.createElement("conductor");
            element_dom.setAttribute("uuid", uid);

            AdeElementStyle style = customConductorStyles.value(uid);
            QDomElement style_dom = style.toXml(xml_document);
            element_dom.appendChild(style_dom);

            elements_dom.appendChild(element_dom);
        }
    }
    root.appendChild(elements_dom);

    return root;
}

bool ADEDiagramCableStyleManager::fromXml(const QDomElement &root)
{
    customConductorStyles.clear();

    //root customDiagramStyle
    if (root.isNull())
        return false;

    QDomElement conductors_elmt;

    for (QDomNode child = root.firstChild(); !child.isNull(); child = child.nextSibling())
    {
        QDomElement child_elmt = child.toElement();
        if (child_elmt.isNull()) continue;

        if (child_elmt.tagName() == "conductors")
            conductors_elmt = child_elmt;
    }

    if (!conductors_elmt.isNull())
    {
        for (QDomNode child = conductors_elmt.firstChild(); !child.isNull(); child = child.nextSibling())
        {
            QDomElement child_elmt = child.toElement();
            if (child_elmt.isNull()) continue;

            if (child_elmt.tagName() == "conductor")
            {
                QString uuid = child_elmt.attribute("uuid");
                QDomElement adeStyleEle = child_elmt.firstChildElement("adeStyle");
                AdeElementStyle style; style.fromXml(adeStyleEle);
                customConductorStyles.insert(uuid, style);
            }
        }
    }
    else
    {
        defaultCustomStyleWithoutXnlInfo(mDiagram->items());
    }

    return true;
}

void ADEDiagramCableStyleManager::switchDiagramCableStyle(DiagramCableStyle style)
{
    if (style == SpecifiedSignalCategoryStyle)
        return;

    QString info;
    if (style == DefaultStyle)
        info = "默认";
    else if (style == Custom)
        info = "自定义";
    else if (style == SignalCategoryStyle)
        info = "按信号类型";
    else if (style == Empty)
        info = "空";
    USERLOG_INFO << QString("【切换电缆样式风格】%1").arg(info);

    if (!mDiagram)
        return;

    if (mDiagram->items().isEmpty())
        return;

    updateSgCategoryStyles();

    // qDebug() << mSgCategoryCableStyle;

    for (auto qgi : mDiagram->items())
    {
        switch (qgi->type())
        {
        case Conductor::Type:
        {
            CustomConductor *ctElmt = qgraphicsitem_cast<CustomConductor *>(qgi);
            changeConductorStyle(ctElmt, style);
            break;
        }
        default:
            break;
        }
    }

    // 按分支显示信号类型颜色
    for (auto qgi : mDiagram->items())
    {
        switch (qgi->type())
        {
        case Element::Type:
        {
            Element *ctElmt = qgraphicsitem_cast<Element *>(qgi);

            if (ctElmt->adeType() == InterfaceElement::ADEType)
            {
                InterfaceElement* ifEle = qgraphicsitem_cast<InterfaceElement*>(ctElmt);

                if (!ifEle->conductors().isEmpty())
                {
                    CustomConductor *ctElmt = qgraphicsitem_cast<CustomConductor *>(ifEle->conductors().first());
                    if(ctElmt && ctElmt->parentCable())
                    {
                        if (ctElmt->parentCable()->allConductors().count() != 1)
                            changeConductorStyleByInterfaceBranch(ifEle, style);
                    }
                }
            }
            break;
        }
        default:
            break;
        }
    }

}

void ADEDiagramCableStyleManager::switchDiagramCableStyleHighLightSpecifiedSignalCategory(int index, QList<SignalCategoryInfo> infos)
{
    if ((DiagramCableStyle)index != SpecifiedSignalCategoryStyle)
        return;

    mCurrentStyle = (DiagramCableStyle)index;

    USERLOG_INFO << QString("【切换电缆样式风格】高亮指定信号类型");

    if (!mDiagram)
        return;

    if (mDiagram->items().isEmpty())
        return;

    // 全部显示灰色虚线
    for (auto qgi : mDiagram->items())
    {
        switch (qgi->type())
        {
        case Conductor::Type:
        {
            CustomConductor *ctElmt = qgraphicsitem_cast<CustomConductor *>(qgi);

            if (!ctElmt)
                break;

            ctElmt->setCdtPatternLockState(false);

            QPen pen;
            pen.setStyle(Qt::DashLine);
            // pen.setColor(QColor("#d1d1d1"));
            pen.setColor(QColor("#cccccc"));


            ctElmt->unuseConductorDashPattern();
            ctElmt->setPen(pen);

            ctElmt->setCdtPatternLockState(true);

            break;
        }
        default:
            break;
        }
    }

    // 高亮指定导线
    DiagramSignalConnections dsc(mDiagram, mDiagram->ade()->prjUuid());
    QMap<CustomConductor*, QList<QColor>> hlConductors =
            CableSignalHighlighter::highlightSignals(mDiagram, dsc.cableSignalConnections().values(), infos);

    if (!hlConductors.isEmpty())
    {
        for (CustomConductor* cdt : hlConductors.keys())
        {
            if (!cdt)
                continue;

            if (!cdt->parentCable())
                continue;

            QStringList colors;
            for (QColor color : hlConductors.value(cdt))
                colors << color.name();

            colors.removeDuplicates();

            cdt->setCdtPatternLockState(false);
            cdt->useConductorDashPattern(colors);
            cdt->setCdtPatternLockState(true);
        }
    }
}

void ADEDiagramCableStyleManager::defaultCustomStyleWithoutXnlInfo(QList<QGraphicsItem *> qgis)
{
    // 避免非初始化时 使用diagram::fromXml函数 重置布置图当前样式类型
    if (mCurrentStyle != SignalCategoryStyle &&
            mCurrentStyle != SpecifiedSignalCategoryStyle &&
            mCurrentStyle != DefaultStyle)
        mCurrentStyle = Custom;

    // 当前非自定义样式时，不从当前布置图读取自定义样式
    if (mCurrentStyle != Custom)
        return;

    if (qgis.isEmpty())
        return;

    customConductorStyles.clear();

    for (auto qgi : qgis)
    {
        switch (qgi->type())
        {
        case Conductor::Type:
        {
            CustomConductor *ctElmt = static_cast<CustomConductor *>(qgi);
            AdeElementStyle style = mDefaultNoStyle;
            style.setPen(ctElmt->pen());
            customConductorStyles.insert(ctElmt->uuid(), style);
            break;
        }
        default:
            break;
        }
    }
}

void ADEDiagramCableStyleManager::setCurrentStyle(DiagramCableStyle style)
{
    if (style != mCurrentStyle)
    {
        mCurrentStyle = style;
    }
}

void ADEDiagramCableStyleManager::updateCustomConductorStyles(QString uuid, AdeElementStyle style)
{
    customConductorStyles.insert(uuid, style);
}

void ADEDiagramCableStyleManager::updateSgCategoryStyles()
{
    mSgCategoryCableStyle.clear();
    mIfToSignalCategoryColors.clear();

    //获取布置图上所有电缆-接口信号类型颜色，去重
    if (mDiagram == nullptr)
        return;

    if (!mDiagram->ade()->cableManager()->allCables().isEmpty())
    {
        for (auto cable : mDiagram->ade()->cableManager()->allCables().values())
        {
            QString cableUuid = cable->uuid();
            QStringList ifSgCategoryColors;

            QMap<QString, InterfaceElement*> ifs = cable->interfaceElements();
            // QList<InterfaceElement *> ifs = cable->cableSelfInterfaceElements();
            if (!ifs.values().isEmpty())
            {
                for (auto ifElmt : ifs.values())
                {
                    QString ifUuid = ifElmt->uuid().toString();

                    interfacePtr obj = mDiagram->ade()->adeProjectHandler()->interfaceManager()->getInterfaceObj(ifUuid);

                    if (!obj || obj->customSlCategorys.isEmpty())
                        continue;

                    QMap<QString, QString> colors = getSignalCategolyColors(obj->customSlCategorys);
                    // colors.removeDuplicates();

                    //删选接口未使用的信号类型
                    signalList sgObjs = PRJ_MD_GETTER->getAllSgObjs(mDiagram->ade()->prjUuid()).value(ifUuid);

                    if (!sgObjs.isEmpty())
                    {
                        QStringList sgCategorys;
                        for (signalPtr obj : sgObjs)
                        {
                            if (obj->slCategory == "无")
                                continue;
                            else
                                sgCategorys.append(obj->slCategory);
                        }

                        if (sgCategorys.isEmpty())
                            continue;

                        sgCategorys.removeDuplicates();
                        QStringList cgUsedColors;
                        for (QString category : sgCategorys)
                        {
                            if (colors.keys().contains(category))
                                cgUsedColors.append(colors.value(category));
                        }
                        ifSgCategoryColors.append(cgUsedColors);
                        mIfToSignalCategoryColors.insert(ifUuid, cgUsedColors);
                    }
                    else
                        continue;
                }
            }

            ifSgCategoryColors.removeDuplicates();
            mSgCategoryCableStyle.insert(cableUuid, ifSgCategoryColors);

            // qDebug() << Q_FUNC_INFO << cableUuid << ifSgCategoryColors.count();
        }
    }
}

void ADEDiagramCableStyleManager::refreshCableStyle(const QString &cableUuid, DiagramCableStyle style)
{
    if (style == SpecifiedSignalCategoryStyle)
        return;

    if (!mDiagram)
        return;

    if (mDiagram->ade()->cableManager()->allCables().isEmpty())
        return;

    ADELogicCable* editedCable = mDiagram->ade()->cableManager()->getCableByUuid(cableUuid);

    if (!editedCable)
        return;

    updateSgCategoryStyles();
    if (editedCable->allConductors().isEmpty())
        return;

    for (auto cdt : editedCable->allConductors())
    {
        changeConductorStyle(cdt, style);
    }

    if (editedCable->allConductors().count() != 1)
    {
        for (auto ifEle : editedCable->interfaceElements().values())
        {
            changeConductorStyleByInterfaceBranch(ifEle, style);
        }
    }
}

AdeElementStyle ADEDiagramCableStyleManager::getCustomConductorStyle(const QString &uuid)
{
   return customConductorStyles.value(uuid, mDefaultNoStyle);
}

QMap<QString, QString> ADEDiagramCableStyleManager::getSignalCategolyColors(const QString &jsonInfo)
{
    QMap<QString, QString> colors;

    if (jsonInfo.isEmpty())
        return colors;

    QJsonParseError jsonError;
    QJsonDocument jsonDoc(QJsonDocument::fromJson(jsonInfo.toUtf8(), &jsonError));

    if (jsonError.error == QJsonParseError::NoError)
    {
        QJsonArray rootArray = jsonDoc.array();

        for (int index = 0; index < rootArray.size(); index++)
        {
            QJsonObject obj = rootArray.at(index).toObject();
            QString customCgName = obj.keys().first();
            QJsonObject valueObj = obj.value(obj.keys().first()).toObject();
            QString color = valueObj.value("color").toString();

            colors.insert(customCgName, color);
        }
    }
    else
    {
        LOG_ERROR(QString("json str:%1,parse faild:%2").arg(jsonInfo).arg(jsonError.errorString()));
    }

    return colors;
}

void ADEDiagramCableStyleManager::changeConductorStyle(CustomConductor *cdt, DiagramCableStyle style)
{
    AdeElementStyle pattern = mDefaultNoStyle;
    if (style == DefaultStyle)
    {
        cdt->unuseConductorDashPattern();
        cdt->setCdtPatternLockState(false);
        cdt->setPen(pattern.getPen());
    }
    else if (style == Custom)
    {
        if (customConductorStyles.keys().contains(cdt->uuid()))
            pattern = customConductorStyles.value(cdt->uuid());

        cdt->unuseConductorDashPattern();
        cdt->setCdtPatternLockState(false);
        cdt->setPen(pattern.getPen());
    }
    else if (style == SignalCategoryStyle)
    {
        if (!cdt->parentCable())
            return;

        cdt->setCdtPatternLockState(false);

        if (!mSgCategoryCableStyle.keys().contains(cdt->parentCable()->uuid()))
        {
            cdt->unuseConductorDashPattern();
            cdt->setPen(pattern.getPen());
        }
        else
        {
            if (!mSgCategoryCableStyle.value(cdt->parentCable()->uuid()).isEmpty())
                cdt->useConductorDashPattern(mSgCategoryCableStyle.value(cdt->parentCable()->uuid()));
            else
            {
                cdt->unuseConductorDashPattern();
                cdt->setPen(pattern.getPen());
            }
        }

        cdt->setCdtPatternLockState(true);
    }
}

void ADEDiagramCableStyleManager::changeConductorStyleByInterfaceBranch(InterfaceElement *ifEle, DiagramCableStyle style)
{
    if (!ifEle)
        return;

    AdeElementStyle pattern = mDefaultNoStyle;

    if (style == Custom)
    {
    }
    else if (style == SignalCategoryStyle)
    {
        if (ifEle->conductors().isEmpty())
            return;

        // if (ifEle->textItem())
        //     qDebug() << Q_FUNC_INFO << ifEle->textItem()->text().toStdString().c_str() << ifEle->conductors().count()
        //              << ifEle->cable()->allConductors().count();


        for (auto cdt : ifEle->conductors())
        {
            CustomConductor *ctElmt = qgraphicsitem_cast<CustomConductor *>(cdt);
            if (!ctElmt)
                return;

            ctElmt->setCdtPatternLockState(false);

            if (mIfToSignalCategoryColors.value(ifEle->uuid().toString()).isEmpty())
            {
                ctElmt->unuseConductorDashPattern();
                ctElmt->setPen(pattern.getPen());
            }
            else
            {
                QStringList branchCdtColors = mIfToSignalCategoryColors.value(ifEle->uuid().toString());
                branchCdtColors.removeDuplicates();
                ctElmt->useConductorDashPattern(branchCdtColors);
            }

            ctElmt->setCdtPatternLockState(true);
        }
    }
}
