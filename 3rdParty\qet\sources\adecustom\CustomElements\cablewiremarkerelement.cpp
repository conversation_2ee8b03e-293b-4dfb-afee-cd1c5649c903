﻿#include "cablewiremarkerelement.h"

#include <QDomDocument>

#include "../diagram.h"
#include "../qetxml.h"
#include "../qetgraphicsitem/terminal.h"
#include "../qetgraphicsitem/simpleconductor.h"
#include "../qetgraphicsitem/dynamicelementtextitem.h"

#include "connectorwiringelement.h"

#include "3rdParty/logBusiness/logbusiness.h"

CableWireMarkerElement::CableWireMarkerElement(const ElementsLocation &location,
                                               int *state) :
      Element(location, nullptr, state, Element::Simple),
    mFirstAddToScene(true)
{
    // 提高层级，显示在导线上方
    setZValue(99999);

    // 存在文本时进行初始化
    initText();

    // 不显示端子
    for (const auto &t : terminals())
    {
        t->setDrawTermianl(false);
    }
}

CableWireMarkerElement::~CableWireMarkerElement()
{

}


/**
 * @brief CableWireMarkerElement::setWireType 设置线缆类型
 * @param type
 * @note  !!应且只应在元素构建（不包括从fromXml构建）后设置一次类型!!
 */
void CableWireMarkerElement::setWireType(ADE::Diagram::CableWireType type, const QString &typeName)
{
    mWireType = type;
    mTypeName = typeName;
}

QString CableWireMarkerElement::wireTypeName() const
{
    return mTypeName;
}

/**
 * @brief CableWireMarkerElement::setMarkedConductors 从导线中读取当前电缆上的点位信息
 * @param cdts
 * @note !!应且只应在元素构建（不包括从fromXml构建）后调用此函数!!
 */
void CableWireMarkerElement::setMarkedConductors(QList<Conductor *> cdts)
{
    for (const auto &c : cdts)
    {
        bool valid = true;
        ConductorCableData data(c, valid);
        if (valid)
            mData << data;
    }
}

QList<Conductor *> CableWireMarkerElement::markedConductors() const
{
    return mMarkedConductors;
}

/**
 * @brief CableWireMarkerElement::firstCreatedPostProcess 元素第一次被创建后（不包含fromXml）且添加到场景上时的后处理
 * @note ！！应且只应在元素创建且添加到场景上后调用！！
 */
void CableWireMarkerElement::firstCreatedPostProcess()
{
    if (!diagram())
    {
        LOG_ERROR("元素没有添加到场景");
        return;
    }

    // 针对不同的类型进行特殊处理
    if (mWireType == ADE::Diagram::Ground)
    {
        // 对于接地类型，将元素的端子与导线上的端子连接
        for (const auto &c : markedConductors())
        {
            SimpleConductor *newConductor = new SimpleConductor(c->terminal1, this->terminal());
            if (newConductor)
            {
                diagram()->addItem(newConductor);
                // 手动隐藏导线文本, 考虑设置导线属性 m_show_text 实现
                newConductor->setTextVisible(false);
            }
        }
    }
}

QList<ConductorCableData> CableWireMarkerElement::wireTypeData() const
{
    return mData;
}

void CableWireMarkerElement::updateData(const QList<ConductorCableData> &data)
{
    mData = data;
    initData();
}

bool CableWireMarkerElement::fromXml(QDomElement& e, QHash<int, Terminal*>& table_id_adr)
{
    bool res = Element::fromXml(e, table_id_adr);
    if (res)
    {
        mWireType = (ADE::Diagram::CableWireType)e.attribute("CableWireType").toInt();
        mTypeName = e.attribute("WireTypeName");
        mData.clear();

        QDomElement dataXml = e.firstChildElement("Data");
        for (const auto &node : QET::findInDomElement(dataXml, "ConductorCableData"))
        {
            bool valid = true;
            ConductorCableData data(node, valid);
            if (valid)
            {
                mData << data;
            }
        }

        initText();
    }

    return res;
}

QDomElement CableWireMarkerElement::toXml(QDomDocument& document,
                                    QHash<Terminal*, int>& table_adr_id) const
{
    // 先在Element基类中创建xml元素, 再补充自定义信息
    QDomElement elmtXml = Element::toXml(document, table_adr_id);
    elmtXml.setAttribute("CableWireType", mWireType);
    elmtXml.setAttribute("WireTypeName", mTypeName);

    // 记录电缆数据
    QDomElement dataXml = document.createElement("Data");
    elmtXml.appendChild(dataXml);
    for (const auto &data : mData)
    {
        dataXml.appendChild(data.toXml(document));
    }

    return elmtXml;
}

QString CableWireMarkerElement::name() const
{
    return mTypeName;
}

void CableWireMarkerElement::mousePressEvent(QGraphicsSceneMouseEvent *event)
{
    Element::mousePressEvent(event);

    highlightMarkedConductors(true);
}

void CableWireMarkerElement::mouseReleaseEvent(QGraphicsSceneMouseEvent *event)
{
    Element::mouseReleaseEvent(event);

    highlightMarkedConductors(false);
}

void CableWireMarkerElement::hoverEnterEvent(QGraphicsSceneHoverEvent *event)
{
    Element::hoverEnterEvent(event);

    highlightMarkedConductors(true);
}

void CableWireMarkerElement::hoverLeaveEvent(QGraphicsSceneHoverEvent *event)
{
    Element::hoverLeaveEvent(event);

    highlightMarkedConductors(false);
}

QVariant CableWireMarkerElement::itemChange(GraphicsItemChange change, const QVariant& value)
{
    if (QGraphicsItem::ItemSceneHasChanged == change)
    {
        // 添加到场景上时计算标记的导线
        if (auto d = diagram())
        {
            // 添加到场景上时才能获取场景的导线信息
            initData();

            // Diagram从xml中加载的情景中，此函数运行时导线还没有初始化，
            // 因此当xml解析完毕后再读取导线信息
            if (mFirstAddToScene)
            {
                connect(d, &Diagram::xmlParseCompleted, this, [this](){
                    initData();
                });
            }

            mFirstAddToScene = false;
        }
    }

    return Element::itemChange(change, value);
}

bool CableWireMarkerElement::sceneEventFilter(QGraphicsItem *watched, QEvent *event)
{
    QGraphicsSceneMouseEvent * sceneEvent = static_cast<QGraphicsSceneMouseEvent *>(event);
    return false;
}

void CableWireMarkerElement::initData()
{
    mMarkedConductors.clear();
    Diagram *d = this->diagram();
    QList<Conductor *> cdts;
    for (const auto &data : mData)
    {
        Conductor *c = data.findConductor(d);
        if (c) mMarkedConductors << c;
    }
}

void CableWireMarkerElement::initText()
{
    if (auto text = textItem())
    {
        // 调整文本位置到中心
        QRectF nameRect = text->boundingRect();

        text->setPos( - (nameRect.width() / 2), - (nameRect.height() / 2));

        // text->setEnabled(false);
        // 设置不接收鼠标事件
        text->setFlag(QGraphicsItem::ItemIsSelectable, false);
        text->setFlag(QGraphicsItem::ItemIsMovable, false);
        text->setFlag(QGraphicsItem::ItemSendsGeometryChanges, false);
    }
}

void CableWireMarkerElement::highlightMarkedConductors(bool hl)
{
    if (hl)
    {
        for (const auto &c : markedConductors())
        {
            c->setHighlighted(Conductor::Normal);
            c->setHighVisibility(true);

            // 将高亮导线显示在最上方
            mConductorZValue.insert(c->uuid(), c->zValue());
            c->setZValue(9999);
        }

        for (const auto &c : conductors())
        {
            c->setHighlighted(Conductor::Normal);
            c->setHighVisibility(true);

            // 将高亮导线显示在最上方
            mConductorZValue.insert(c->uuid(), c->zValue());
            c->setZValue(9999);
        }
    }
    else
    {
        for (const auto &c : markedConductors())
        {
            c->setHighlighted(Conductor::None);
            c->setHighVisibility(false);
            c->setZValue(mConductorZValue.value(c->uuid(), Conductor::defaultZValue));
        }

        for (const auto &c : conductors())
        {
            c->setHighlighted(Conductor::None);
            c->setHighVisibility(false);
            c->setZValue(mConductorZValue.value(c->uuid(), Conductor::defaultZValue));
        }
    }
}

///------------------------------------------------------------------
/// ConductorCableData
///------------------------------------------------------------------
ConductorCableData::ConductorCableData(Conductor *conductor, bool &valid)
{
    Element *elmt1 = conductor->terminal1->parentElement();
    if (auto cttElmt = qobject_cast<ContactorElement *>(elmt1))
    {
        ifUuid_1 = cttElmt->adeFatherUuid();
        pointName_1 = cttElmt->pointName();
    }
    else
    {
        valid = false;
        LOG_ERROR(QString("元素%1无法转为接触件元素").arg(elmt1->uuid().toString()));
        return;
    }

    Element *elmt2 = conductor->terminal2->parentElement();
    if (auto cttElmt = qobject_cast<ContactorElement *>(elmt2))
    {
        ifUuid_2 = cttElmt->adeFatherUuid();
        pointName_2 = cttElmt->pointName();
    }
    else
    {
        valid = false;
        LOG_ERROR(QString("元素%1无法转为接触件元素").arg(elmt2->uuid().toString()));
        return;
    }

    valid = true;
}

ConductorCableData::ConductorCableData(const QDomElement &e, bool &valid)
{
    if (e.tagName() != "ConductorCableData")
    {
        valid = false;
        return;
    }

    ifUuid_1 = e.attribute("ifUuid_1");
    pointName_1 = e.attribute("pointName_1");
    ifUuid_2 = e.attribute("ifUuid_2");
    pointName_2 = e.attribute("pointName_2");
}


Conductor *ConductorCableData::findConductor(Diagram *diagram) const
{
    for (const auto &cdt : diagram->conductors())
    {
        Element *elmt1 = cdt->terminal1->parentElement();
        Element *elmt2 = cdt->terminal2->parentElement();

        auto cttElmt1 = qobject_cast<ContactorElement *>(elmt1);
        auto cttElmt2 = qobject_cast<ContactorElement *>(elmt2);

        if (!cttElmt1 || !cttElmt2)
        {
            continue;
        }

        if ( ((cttElmt1->adeFatherUuid() == ifUuid_1) && (cttElmt1->pointName() == pointName_1)) &&
             ((cttElmt2->adeFatherUuid() == ifUuid_2) && (cttElmt2->pointName() == pointName_2)) )
        {
            return cdt;
        }

        if ( ((cttElmt1->adeFatherUuid() == ifUuid_2) && (cttElmt1->pointName() == pointName_2)) &&
            ((cttElmt2->adeFatherUuid() == ifUuid_1) && (cttElmt2->pointName() == pointName_1)) )
        {
            return cdt;
        }
    }

    return nullptr;
}

QDomElement ConductorCableData::toXml(QDomDocument &doc) const
{
    QDomElement node = doc.createElement("ConductorCableData");
    node.setAttribute("ifUuid_1", ifUuid_1);
    node.setAttribute("pointName_1", pointName_1);

    node.setAttribute("ifUuid_2", ifUuid_2);
    node.setAttribute("pointName_2", pointName_2);

    return node;
}
