#ifndef LIBRARYINFOCTRL_H
#define LIBRARYINFOCTRL_H

#include "sqlCtrlBase.h"
#include "../sqlModel/libraryinfomodel.h"
#include <QSharedPointer>

class LibraryInfoCtrl : public SqlCtrlBase
{
    Q_OBJECT
public:
    explicit LibraryInfoCtrl(const QString &dbPath, QObject *parent = nullptr);

    bool checkAndCreateTable() override;

    bool setLibraryInfo(const LibraryInfoModel &model);
    QSharedPointer<LibraryInfoModel> getLibraryInfo();
};

#endif // LIBRARYINFOCTRL_H 