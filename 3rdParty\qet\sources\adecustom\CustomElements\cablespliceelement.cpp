﻿#include "cablespliceelement.h"

#include "../diagram.h"
#include "../qetxml.h"
#include "../qetgraphicsitem/conductor.h"
#include "../CustomElements/interfaceelement.h"
#include "../CustomExtend/geometricsolver.h"
#include "../UndoCommand/customCommand/mergecablesplicecommand.h"

int CableSpliceElement::MergeSearchRadius = 10;

CableSpliceElement::CableSpliceElement(const ElementsLocation &location,
                                   int *state) :
      Element(location, nullptr, state, Element::Simple)
    , mMergeMode(false)
    , mMergeTargetInterfaceElement(nullptr)
{
    mHalfLength = 10;

    // 端点元素的层级最高
    setZValue(99999);
}


CableSpliceElement::~CableSpliceElement()
{

}

QRectF CableSpliceElement::boundingRect() const
{
    return ( QRectF( QPointF(-mHalfLength, -mHalfLength) ,
                     QSizeF(mHalfLength * 2, mHalfLength * 2) ) );
}


bool CableSpliceElement::fromXml(QDomElement& e, QHash<int, Terminal*>& table_id_adr)
{
    bool res = Element::fromXml(e, table_id_adr);

    return res;
}

QDomElement CableSpliceElement::toXml(QDomDocument& document,
                                    QHash<Terminal*, int>& table_adr_id) const
{
    // 先在Element基类中创建xml元素, 再补充自定义信息
    QDomElement elmtXml = Element::toXml(document, table_adr_id);

    return elmtXml;
}

void CableSpliceElement::mousePressEvent(QGraphicsSceneMouseEvent *event)
{
    if (event->modifiers() == Qt::ControlModifier)
    {
        // 如果只连接了一个元素，不允许合并
        if (connectedElements().size() > 1)
        {
            mInitialMergePos = this->scenePos();
            mMergeMode = true;
        }
    }

    Element::mousePressEvent(event);
}

void CableSpliceElement::mouseMoveEvent(QGraphicsSceneMouseEvent *event)
{
    if (mMergeMode)
    {
        event->ignore();

        QList<QGraphicsItem*> qgis = scene()->items(QET::circlePath(event->scenePos(), MergeSearchRadius));
        auto ifElmts = connectedInterfaceElements();

        // 过滤掉电缆直连接口
        QList<InterfaceElement *> filteredIfElmts;
        for (auto e : ifElmts)
        {
            if (!e->isDirectConnectInterface())
                filteredIfElmts << e;
        }

        // 寻找距离最近的接口元素
        QList<InterfaceElement *> nearbyIfElmts;
        for (auto qgi : qgis)
        {
            if (qgi->type() != Element::Type)
                continue;

            auto elmt = qgraphicsitem_cast<Element *>(qgi);
            if (elmt->adeType() != InterfaceElement::ADEType)
                continue;

            auto ifElmt = element_cast<InterfaceElement *>(elmt);
            if (filteredIfElmts.contains(ifElmt))
                nearbyIfElmts << ifElmt;
        }

        InterfaceElement *closestIfElmt = nullptr;
        if (!nearbyIfElmts.isEmpty())
        {
            QMap<InterfaceElement*, QPointF> distInfo;
            for (auto e : nearbyIfElmts)
                distInfo.insert(e, e->scenePos());
            QPointF closestP = GeometricSolver::closestPointToPoint(this->scenePos(), distInfo.values());
            if (distInfo.values().contains(closestP))
                closestIfElmt = distInfo.key(closestP);
        }

        if (closestIfElmt)
        {
            if (mMergeTargetInterfaceElement)
            {
                if (mMergeTargetInterfaceElement == closestIfElmt)
                {
                    // do nothing
                }
                else
                {
                    mMergeTargetInterfaceElement->setHighlighted(false);
                    closestIfElmt->setHighlighted(true);
                }
            }
            else
                closestIfElmt->setHighlighted(true);

            mMergeTargetInterfaceElement = closestIfElmt;

            // 吸附到最近的接口上
            this->setPos(closestIfElmt->scenePos());
        }
        else
        {
            if (mMergeTargetInterfaceElement)
                mMergeTargetInterfaceElement->setHighlighted(false);
            mMergeTargetInterfaceElement = nullptr;

            this->setPos(event->scenePos());
        }
    }
    else
    {
        Element::mouseMoveEvent(event);
    }
}

void CableSpliceElement::mouseReleaseEvent(QGraphicsSceneMouseEvent *event)
{
    if (mMergeMode)
    {
        event->ignore();

        if (mMergeTargetInterfaceElement)
        {
            // 创建合并交点的命令
            auto cmd = new MergeCableSpliceCommand(this, mMergeTargetInterfaceElement, mInitialMergePos);
            diagram()->undoStack().push(cmd);
        }
        else
        {
            // 没有可合并的接口时还原位置
            setPos(mInitialMergePos, false);
        }

        // 重置状态
        mMergeMode = false;
        if (mMergeTargetInterfaceElement)
            mMergeTargetInterfaceElement->setHighlighted(false);
        mMergeTargetInterfaceElement = nullptr;
    }
    else
    {
        Element::mouseReleaseEvent(event);
    }
}

void CableSpliceElement::setCable(ADELogicCable *cable)
{
    mCable = cable;
}

/**
 * @brief CableSpliceElement::interfaceElements 获取端点元素连接的所有接口元素
 * @return
 */
QList<InterfaceElement *> CableSpliceElement::connectedInterfaceElements() const
{
    QList<InterfaceElement *> ifElmts;
    for (auto c : conductors())
    {
        auto e = c->otherSideElement(this);
        if (auto ifElmt = qobject_cast<InterfaceElement *>(e))
            ifElmts << ifElmt;
    }

    return ifElmts;
}

QList<CableSpliceElement *> CableSpliceElement::connectedSpliceElements() const
{
    QList<CableSpliceElement *> spElmts;
    for (auto c : conductors())
    {
        auto e = c->otherSideElement(this);
        if (auto spElmt = qobject_cast<CableSpliceElement *>(e))
            spElmts << spElmt;
    }

    return spElmts;
}

