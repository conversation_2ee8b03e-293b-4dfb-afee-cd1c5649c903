#ifndef PARTDATASHEETLINKSCTRL_H
#define PARTDATASHEETLINKSCTRL_H

#include "../../sqlCtrlBase.h"
#include "../sqlModel/partdatasheetlinksmodel.h"

//============================================================================
/// file
/// brief      元器件与Datasheet关联表控制器
/// author     wm
/// version    1.0
/// date       2025/06/05
/// todo
//============================================================================

class PartDatasheetLinksCtrl : public SqlCtrlBase
{
public:
    PartDatasheetLinksCtrl(const QSqlDatabase& db);
    ~PartDatasheetLinksCtrl();

    // 针对数据模型的操作
    PartDatasheetLinksModelList fetchAll() const;
    PartDatasheetLinksModelPtr  fetch(const QString &id) const;
    bool modelExist(const QString &id) const;
    // end

    // 针对数据库表的操作
    bool insert(const PartDatasheetLinksModelPtr &obj);
    bool modify(const PartDatasheetLinksModelPtr &newValueObj);
    bool remove(const QString &id);
    // end

public:
    QString tableName()       const override {return "Part_Datasheet_Links";}
    QString primaryKeyField() const override {return "LinkUUID";}
    bool    recordExist(const QString &id) const override;
    virtual bool softDelete() const override { return true; }

private:
    QString tableSql()        const override;
    void loadTable();

private:
    PartDatasheetLinksModelList mAllObj;
};

#endif // PARTDATASHEETLINKSCTRL_H