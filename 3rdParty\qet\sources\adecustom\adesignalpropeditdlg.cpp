﻿#include "adesignalpropeditdlg.h"
#include "ui_adesignalpropeditdlg.h"

#include <QMessageBox>

#include "sql/sqlModel/signalinfo.h"
#include "customDialogStyle/dialogstylemodifier.h"

ADESignalPropEditDlg::ADESignalPropEditDlg(const QStringList &pinOutNums,
                                           const ADEPinoutSignalProperty &prop,
                                           QWidget *parent) :
    QDialog(parent),
    ui(new Ui::ADESignalPropEditDlg)
{
    ui->setupUi(this);

    this->setWindowTitle(QString("编辑点位信号属性"));
    this->resize(250, 300);

    ui->lineEdit_pinOutNum->setDisabled(true);
    ui->comboBox_signalProperty->addItems(signalTypeStr);

    QString pinRange;
    for (QString pinOutNum : pinOutNums)
        pinRange += pinOutNum + ",";
    pinRange.chop(1);

    ui->lineEdit_pinOutNum->setText(pinRange);
    ui->lineEdit_signalName->setText(prop.mSignalName);
    ui->comboBox_signalProperty->setCurrentText(prop.mType);
    ui->lineEdit_current->setText(prop.mCurrent);
    ui->lineEdit_voltage->setText(prop.mVoltage);
    ui->lineEdit_frequency->setText(prop.mFrequency);

    connect(ui->pushButton_confirm, &QPushButton::clicked, this, &ADESignalPropEditDlg::onConfirmButtonClicked);
    connect(ui->pushButton_cancel, &QPushButton::clicked, this, [=](){
        done(QDialog::Rejected);
        close();
    });
    connect(ui->comboBox_signalProperty, static_cast<void (QComboBox::*)(const QString &)>(&QComboBox::currentTextChanged),
            this, [=](QString text){
        if (text == "接壳")
            ui->lineEdit_signalName->setText("接壳");
    });

    DialogStyleModifier::getInstance()->installStyle(this, QSize(250, 300));
}

ADESignalPropEditDlg::~ADESignalPropEditDlg()
{
    delete ui;
}

void ADESignalPropEditDlg::onConfirmButtonClicked()
{
//    if (ui->lineEdit_signalName->text().isEmpty())
//    {
//        QMessageBox::information(this, "信息", "请输入信号名称");
//        return;
//    }

    done(QDialog::Accepted);
    close();
}

ADEPinoutSignalProperty ADESignalPropEditDlg::getSignalProperty() const
{
    // 信号类型为"接壳"时，信号名称也为"接壳"
    QString signalName = ui->lineEdit_signalName->text();
    if (ui->comboBox_signalProperty->currentText() == "接壳")
        signalName = "接壳";

    ADEPinoutSignalProperty prop;
    prop.mSignalName = signalName;
    prop.mType = ui->comboBox_signalProperty->currentText();
    prop.mCurrent = ui->lineEdit_current->text();
    prop.mVoltage = ui->lineEdit_voltage->text();
    prop.mFrequency = ui->lineEdit_frequency->text();
    prop.mUsedState = true;

    return prop;
}

bool ADESignalPropEditDlg::isSignalNameChanged()
{
    bool isChanged = ui->lineEdit_signalName->isModified();
    return isChanged;
}
