﻿#include "pinoutelement.h"

#include "../diagram.h"
#include "../qetxml.h"
#include "../qetgraphicsitem/dynamicelementtextitem.h"

#include "../qetgraphicsitem/terminal.h"
#include "../adecustom/adesignalpropeditdlg.h"
#include "../ElementsCollection/xmlelementcollection.h"

PinOutElement::PinOutElement(const ElementsLocation &location,
                                   int *state) :
      Element(location, nullptr, state, Element::Simple)
{
    for (auto t : terminals())
    {
        t->setDrawTermianl(false);
        t->setDrawHover(false);
    }

    // 连接器模型引脚元素不可移动
    setMovable(false);

    // 当元素已经被添加到场景上时, 才能进行事件过滤器的安装
    if (scene() && !mTextEventFilterInstalled)
    {
        for (auto dti :dynamicTextItems())
        {
            dti->setFlag(QGraphicsItem::ItemIsMovable, false);
            dti->setFlag(QGraphicsItem::ItemIsSelectable, false);

            dti->installSceneEventFilter(this);
        }
        mTextEventFilterInstalled = true;
    }
}


PinOutElement::~PinOutElement()
{

}

bool PinOutElement::fromXml(QDomElement& e, QHash<int, Terminal*>& table_id_adr)
{
    bool res = Element::fromXml(e, table_id_adr);

    if (res)
    {
        // 读取信号属性
        QList<QDomElement> domList = QET::findInDomElement(e, "signalProperty");
        if (!domList.isEmpty())
        {
            m_pin_signal_prop.init(domList.first());
        }

        // 读取点位编号
        if (m_dynamic_text_list.size() == 1)
        {
            m_pinout_num = m_dynamic_text_list.first()->text(); // 点位编号使用元素文本进行保存
        }

        // 引脚元素文本不可编辑
        m_text_editable = false;
        for (auto dti :dynamicTextItems())
        {
            // dti->setFlag(QGraphicsItem::ItemStacksBehindParent, true);
            dti->setFlag(QGraphicsItem::ItemIsMovable, false);
            dti->setFlag(QGraphicsItem::ItemIsSelectable, false);

            dti->installSceneEventFilter(this);
        }
    }

    return res;
}

QDomElement PinOutElement::toXml(QDomDocument& document,
                                    QHash<Terminal*, int>& table_adr_id) const
{
    // 先在Element基类中创建xml元素, 再补充自定义信息
    QDomElement elmtXml = Element::toXml(document, table_adr_id);

    elmtXml.appendChild(m_pin_signal_prop.toXmlElement(document));

    return elmtXml;
}

void PinOutElement::editProperty()
{

}

void PinOutElement::paint(
    QPainter* painter,
    const QStyleOptionGraphicsItem* options,
    QWidget*w)
{
    painter->save();
    QPen pen;
    QBrush brush;

    painter->setPen(pen);
    painter->setBrush(brush);

    if (options && options -> levelOfDetail < 1.0)
    {
        painter->drawPicture(0, 0, m_low_zoom_picture);
    }
    else
    {
        painter->drawPicture(0, 0, m_picture);
    }

    painter->restore(); //Restor the QPainter after use drawPicture

    //Draw the selection rectangle
    if ( isSelected() || m_mouse_over )
    {
        drawSelection(painter, options);
    }

    // 连接器模型引脚元素
    if ( m_edit_signal_prop_mode)
    {
        // if (m_pin_signal_prop.mSignalName.isEmpty())
        // {
        //     drawHighlight(painter, options, m_unused_state_color);
        //     m_must_highlight = false;
        // }
        if (!m_pin_signal_prop.mUsedState)
        {
            drawHighlight(painter, options, m_unused_state_color);
            m_must_highlight = false;
            m_show_category_color = false;
        }
    }

    if (m_must_highlight)
    {
        // drawHighlight(painter, options, m_pinout_highLight);
        drawSelection(painter, options);   // 高亮修改为绿色实线
    }

    if (m_show_category_color)
    {
        drawHighlight(painter, options, QColor(m_pin_signal_prop.mCustomCategory.second));
    }
}

void PinOutElement::mousePressEvent(QGraphicsSceneMouseEvent *event)
{
    clickPinoutHl();
    Element::mousePressEvent(event);
}

void PinOutElement::hoverEnterEvent(QGraphicsSceneHoverEvent* e)
{
    Q_UNUSED(e)
    hoverPinoutEvent();
}

void PinOutElement::mouseDoubleClickEvent(QGraphicsSceneMouseEvent *event)
{
    editPinoutPropDlg(event->scenePos());
    event->accept();
}

QVariant PinOutElement::itemChange(GraphicsItemChange change, const QVariant& value)
{
    if (change == QGraphicsItem::ItemSelectedChange)
    {
        // 连接器端面模型引脚元素选中状态改变时设置同信号名称的元素高亮状态
        // if (m_edit_signal_prop_mode
        //      && !m_pin_signal_prop.mSignalName.isEmpty())
        if (m_edit_signal_prop_mode
                && m_pin_signal_prop.mUsedState)
        {
            onPinOutSelectionChange(value.toBool());
        }
    }

    return (Element::itemChange(change, value));
}

bool PinOutElement::sceneEventFilter(QGraphicsItem *watched, QEvent *event)
{
    if (!mTextEventFilterInstalled)
        return false;

    if (!m_edit_signal_prop_mode)
        return false;

    if (watched->type() == DynamicElementTextItem::Type)
    {
        DynamicElementTextItem *qghi = qgraphicsitem_cast<DynamicElementTextItem *>(watched);

        if (qghi == nullptr)
            return false;

        if (event->type() == QEvent::GraphicsSceneMousePress)
        {
            clickPinoutHl();
            return true;
        }
        else if (event->type() == QEvent::GraphicsSceneMouseDoubleClick)
        {
            QGraphicsSceneMouseEvent* e = static_cast<QGraphicsSceneMouseEvent *>(event);
            if (e->button() == Qt::LeftButton)
            {
                editPinoutPropDlg(e->scenePos());
                return true;
            }
        }
    }

    return false;
}

void PinOutElement::setEditSignalPropModeEnabled(bool enabled)
{
    m_edit_signal_prop_mode = enabled;
}

QString PinOutElement::contactorType() const
{
    return location().realName();
}

void PinOutElement::setPinoutSignalProp(const ADEPinoutSignalProperty &prop)
{
    m_pin_signal_prop = prop;
    update();
}

void PinOutElement::changePinoutSignalProp(const ADEPinoutSignalProperty& prop, bool isChanged)
{
    setPinoutSignalProp(prop);

    emit pinOutSignalPropChanged(m_pinout_num, m_pin_signal_prop, isChanged);
}

void PinOutElement::clickPinoutHl()
{
    // 连接器模型引脚点位设置为高亮
    // if (m_edit_signal_prop_mode &&
    //         !m_pin_signal_prop.mSignalName.isEmpty())
    // {
    //     onPinOutSelectionChange(true);
    // }

    if (m_edit_signal_prop_mode &&
            m_pin_signal_prop.mUsedState)
    {
        onPinOutSelectionChange(true);
    }
}

void PinOutElement::hoverPinoutEvent()
{
    if (!m_can_move)
        return;

    if (m_edit_signal_prop_mode)
    {
        QString tipText = "<html>"
                          "<body>"
                          "<h3>信号信息</h3>"
                          "<b>点位：</b> " + m_pinout_num + "<br>"
                          "<b>信号名称：</b> " + m_pin_signal_prop.mSignalName + "<br>"
                          "<b>信号属性：</b> " + m_pin_signal_prop.mType + "<br>"
                          "<b>电流：</b> " + m_pin_signal_prop.mCurrent + "<br>"
                          "<b>电压：</b> " + m_pin_signal_prop.mVoltage + "<br>"
                          "<b>频率：</b> " + m_pin_signal_prop.mFrequency + "</p>"
                          "</body>"
                          "</html>";

        QString ctModelUuid = diagram()->ade()->ctModelUuid();
        if (!ctModelUuid.isEmpty())
        {
            bool state = true;
            ConnectorDbCtrl *dbCtrl = new ConnectorDbCtrl(state);
            if (state)
            {
                contactPtr ctObj = dbCtrl->getContact(ctModelUuid, this->contactorType());
                if (ctObj.get() != nullptr)
                {
                    QString type = ctObj->joiningTech;
                    QString dia = QString::number(ctObj->dia);
                    QString resistance = QString::number(ctObj->resistance);
                    QString current = QString::number(ctObj->current);

                    tipText += "<html>"
                               "<body>"
                               "<h3>接触件规格</h3>"
                               "<b>接触件名称：</b> " + this->contactorType() + "<br>"
                               "<b>类  型：</b> " + type + "<br>"
                               "<b>工作直径：</b> " + dia + "<br>"
                               "<b>接触电阻：</b> " + resistance + "<br>"
                               "<b>额定电流：</b> " + current + "</p>"
                               "</body>"
                               "</html>";
                }
            }
        }

        setToolTip(tipText);

        // setToolTip(m_pin_signal_prop.propToString(m_pinout_num));
    }
    else
        setToolTip(QString());

    m_mouse_over = true;
    update();
}

void PinOutElement::setPinOutHighlightBySignalName(const QString& pinName,
        bool hl)
{
    // if (m_pin_signal_prop.mSignalName.isEmpty()
    //         || signalName.isEmpty())
    // {
    //     return;
    // }

    // if (m_pin_signal_prop.mSignalName == signalName)
    // {
    //     setHighlighted(hl);
    // }
    // else
    // {
    //     //setSelected(false);
    // }

    if (pinName.isEmpty())
        return;

    if (pinName == this->textItem()->text())
        setHighlighted(hl);

}

void PinOutElement::resetPinOutHighlight(bool hl)
{
    setHighlighted(hl);
}

void PinOutElement::updatePinOutSignalProperty(const QStringList& pinOutNums,
        const ADEPinoutSignalProperty& prop)
{
    if (m_pinout_num.isEmpty()
            || !pinOutNums.contains(m_pinout_num))
    {
        return;
    }

    m_pin_signal_prop = prop;
    // setHighlighted(true);

    // 刷新模型控件信号名称tooptip
    emit changePointSignalNameToolTip();

    update();
}

void PinOutElement::updatePinOutUseState(const QStringList& pinOutNums)
{
    if (m_pinout_num.isEmpty())
    {
        return;
    }
    // 当前引脚元素点号不在已经使用的点号范围内时重置引脚信号属性
    if (!pinOutNums.contains(m_pinout_num))
    {
        m_pin_signal_prop = ADEPinoutSignalProperty();

        //取消高亮
        setHighlighted(false);

        update();
    }
}

void PinOutElement::setDefaultPinoutEleEnabled(bool displayEnable)
{
    pinoutEleEnabled = !displayEnable;
    update();
}

void PinOutElement::showSignalCategoryColor(bool show)
{
    m_show_category_color = show;
    update();
}

void PinOutElement::setFocusable(const bool& movable)
{
    m_can_move = movable;
    this->setMovable(movable);
    this->setFlag(QGraphicsItem::ItemIsSelectable, movable);
    this->setFlag(QGraphicsItem::ItemIsFocusable, movable);
}

void PinOutElement::onPinOutSelectionChange(bool selected)
{
    emit pinOutSelected(m_pin_signal_prop.mSignalName, m_pinout_num, selected);

    QList<PinOutElement*> otherElmts;
    QList<PinOutElement*> elmts = sameSingalNameElements(otherElmts);
    otherElmts.removeOne(this);
    for (PinOutElement* elmt : elmts)
    {
        elmt->setHighlighted(selected);
    }
    for (PinOutElement* elmt : otherElmts)
    {
        elmt->setHighlighted(false);
    }
}

QList <PinOutElement*> PinOutElement::sameSingalNameElements(QList<PinOutElement*>& otherElmts)
{
    QList<PinOutElement*> list;

    for (Element* elmt : diagram()->elements())
    {
        if (PinOutElement *poElmt = element_cast<PinOutElement*>(elmt))
        {
            if (!poElmt->m_pin_signal_prop.mUsedState)
                continue;

            if (!poElmt->m_pin_signal_prop.mSignalName.isEmpty()
                    && poElmt->m_pin_signal_prop.mSignalName == m_pin_signal_prop.mSignalName)
            {
                list << poElmt;
            }
            else
            {
                otherElmts << poElmt;
            }
        }
    }

    return list;
}

void PinOutElement::drawSelection(
        QPainter* painter,
        const QStyleOptionGraphicsItem* options)
{
    Q_UNUSED(options)
    painter -> save();
    // Annulation des renderhints
    painter -> setRenderHint(QPainter::Antialiasing,          false);
    painter -> setRenderHint(QPainter::TextAntialiasing,      false);
    painter -> setRenderHint(QPainter::SmoothPixmapTransform, false);
    // Dessin du cadre de selection en gris
    // QColor hoverRectColor = is_movable_ ? Qt::gray : Qt::red;
    QColor hoverRectColor = m_must_highlight ? Qt::green : Qt::gray; // 选择效果改为绿色实线
    QPen t;
    t.setColor(hoverRectColor);

    if (m_must_highlight)
    {
        t.setStyle(Qt::SolidLine);
        t.setWidthF(2.0);
    }
    else
        t.setStyle(Qt::DashDotLine);

    t.setCosmetic(true);
    painter -> setPen(t);

    //hyb:重设hotspot,避免选择框偏离元素
    setHotspot(QPoint(size().width() / 2, size().height() / 2));

    painter -> drawRoundedRect(boundingRect().adjusted(0, 0, 0, 0),
                               10, 10);

    painter -> restore();
}

void PinOutElement::editPinoutPropDlg(const QPointF &point)
{
    // 双击连接器模型引脚元素
    if (m_edit_signal_prop_mode)
    {
        // 编辑引脚元素信号属性
        ADESignalPropEditDlg dlg({m_pinout_num}, m_pin_signal_prop);

        // 将窗口移动到鼠标点击位置显示
        if (diagram() && diagram()->views().count())
        {
            QGraphicsView *view = diagram()->views().first();
            QPoint viewPos = view->mapFromScene(point);
            QPoint globalPos = view->mapToGlobal(viewPos);

            dlg.move(globalPos.x() - (dlg.size().width() / 2),
                     globalPos.y() - (dlg.size().height() / 2));
        }

        if (dlg.exec() == QDialog::Accepted)
        {
            ADEPinoutSignalProperty prop = dlg.getSignalProperty();
            // bool isChanged = dlg.isSignalNameChanged();

            // 保留信号类型及颜色
            prop.mCustomCategory = m_pin_signal_prop.mCustomCategory;

            if (m_pin_signal_prop.mCustomCategory.first == "" ||
                    m_pin_signal_prop.mCustomCategory.first == "无" )
                prop.mCustomCategory.second == "#FFFFFF";

            bool isChanged = false;
            if (m_pin_signal_prop.mSignalName != prop.mSignalName)
                isChanged = true;

            if (m_pin_signal_prop.mType != prop.mType)
                isChanged = true;

            if (m_pin_signal_prop.mCurrent != prop.mCurrent)
                isChanged = true;

            if (m_pin_signal_prop.mFrequency != prop.mFrequency)
                isChanged = true;

            if (m_pin_signal_prop.mVoltage != prop.mVoltage)
                isChanged = true;

            if (!isChanged && prop.mSignalName.isEmpty())
                prop.mUsedState = false;

            if (isChanged && prop.mSignalName.isEmpty() && !m_pin_signal_prop.mSignalName.isEmpty())
                prop.mUsedState = false;

            // 取消使用使重置信号类型及颜色
            if (!prop.mUsedState)
            {
                prop.mCustomCategory.first = "无";
                prop.mCustomCategory.second ="#FFFFFF";
            }

            if (isChanged)
                changePinoutSignalProp(prop, isChanged);
        }
    }
}
