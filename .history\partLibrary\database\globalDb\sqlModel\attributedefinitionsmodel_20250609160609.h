#ifndef ATTRIBUTEDEFINITIONSMODEL_H
#define ATTRIBUTEDEFINITIONSMODEL_H

//============================================================================
/// file
/// brief      属性定义数据模型
/// author     wm
/// version    1.0
/// date       2025/06/05
/// todo
//============================================================================

#include "../../sqlModelBase.h"
#include <memory>
#include <QUuid>
#include <QStringList>

class AttributeDefinitionsModel : public SqlModelBase
{
public:
    /**
     * @brief The DataType enum 属性的数据类型
     */
    enum AttrDataType
    {
        String,     ///< 字符串
        Integer,    ///< 整数
        Float,      ///< 浮点数
        Bool,       ///< 布尔值
        Enumeration ///< 枚举值（从options中选择，数据类型为 String）
    };

    AttributeDefinitionsModel() : id(QUuid::createUuid().toString()), dataType(String), isUserDefined(0){}

public:
    // 模型属性
    QString id;                 ///< 属性uuid
    QString name;               ///< 属性名称
    AttrDataType dataType;      ///< 数据类型
    QStringList options;        ///< 数据类型为枚举时的可选值列表
    QString description;        ///< 属性描述
    QString irdi;               ///< IRDI
    QString unit;               ///< 单位
    QString defaultValue;       ///< 默认值
    QString validationRule;     ///< 验证规则
    int     isUserDefined;      ///< 是否用户定义

public:
    QString tableName()       const override {return "AttributeDefinitions";}
    QString primaryKeyField() const override {return "AttributeUUID";}
    QVariantMap modelToDbMap() const override;
    void dbToModel(const QSqlRecord& record) override;

public:
    // Helper functions for enum conversion
    static QString dataTypeToString(AttrDataType type);
    static AttrDataType stringToDataType(const QString& typeStr);

public:
    // 字段名称常量
    static const QString FIELD_ID;
    static const QString FIELD_NAME;
    static const QString FIELD_DATA_TYPE;
    static const QString FIELD_OPTIONS_JSON;
    static const QString FIELD_DESCRIPTION;
    static const QString FIELD_IRDI;
    static const QString FIELD_UNIT;
    static const QString FIELD_DEFAULT_VALUE;
    static const QString FIELD_VALIDATION_RULE;
    static const QString FIELD_IS_USER_DEFINED;
    static const QString FIELD_IS_DELETED;

    static std::shared_ptr<AttributeDefinitionsModel> createPtr()
    {
        return std::make_shared<AttributeDefinitionsModel>();
    }

private:
    void optionsFromJson(const QString& json);
    QString optionsToJson() const;
};

using AttributeDefinitionsModelPtr = std::shared_ptr<AttributeDefinitionsModel>;
using AttributeDefinitionsModelList = QHash<QString, AttributeDefinitionsModelPtr>;

#endif // ATTRIBUTEDEFINITIONSMODEL_H 
