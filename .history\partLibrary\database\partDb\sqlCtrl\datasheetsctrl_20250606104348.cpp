#include "datasheetsctrl.h"

#include "3rdParty/logBusiness/logbusiness.h"

#include <QApplication>

DatasheetsCtrl::DatasheetsCtrl(const QSqlDatabase &db) :
    SqlCtrlBase(db)
{
    if(!db.isValid())
    {
        LOG_ERROR("db not valid");
    }
    else
    {
        loadTable();
    }
}

DatasheetsCtrl::~DatasheetsCtrl()
{
    for(auto itor = mAllObj.begin(); itor != mAllObj.end(); )
    {
        auto obj = itor.value();
        obj.reset();

        mAllObj.erase(itor++);
    }
}

DatasheetsModelList DatasheetsCtrl::fetchAll() const
{
    return mAllObj;
}

DatasheetsModelPtr DatasheetsCtrl::fetch(const QString &id) const
{
    if (modelExist(id))
    {
        return mAllObj.value(id);
    }
    else
    {
        return nullptr;
    }
}

bool DatasheetsCtrl::modelExist(const QString &id) const
{
    return mAllObj.contains(id);
}

bool DatasheetsCtrl::insert(const DatasheetsModelPtr &obj)
{
    if (obj.get() == nullptr)
        return false;

    if (insertTableRecord(*obj.get()))
    {
        mAllObj.insert(obj->id, obj);
        return true;
    }
    else
    {
        return false;
    }
}

bool DatasheetsCtrl::modify(const DatasheetsModelPtr &newValueObj)
{
    if (newValueObj.get() == nullptr)
        return false;

    QString id = newValueObj->id;
    if (!mAllObj.contains(id))
    {
        LOG_ERROR(QString("无法修改记录：该记录不存在 %1").arg(id));
        return false;
    }

    if (updateTableRecord(*newValueObj.get()))
    {
        auto obj = mAllObj.value(id);
        obj->title = newValueObj->title;
        obj->filePath = newValueObj->filePath;
        obj->md5Hash = newValueObj->md5Hash;
        obj->revision = newValueObj->revision;
        obj->uploadTimestamp = newValueObj->uploadTimestamp;

        return true;
    }
    else
    {
        return false;
    }
}

bool DatasheetsCtrl::remove(const QString &id)
{
    if (!mAllObj.contains(id))
    {
        LOG_ERROR(QString("无法删除记录：该记录不存在 %1").arg(id));
        return false;
    }

    if (deleteTableRecord(id))
    {
        auto obj = mAllObj.take(id);
        obj.reset();

        return true;
    }
    else
    {
        return false;
    }
}

bool DatasheetsCtrl::recordExist(const QString &id) const
{
    return mAllObj.contains(id);
}

QString DatasheetsCtrl::tableSql() const
{
    return QString(R"(
        CREATE TABLE Datasheets (
            DatasheetUUID   TEXT PRIMARY KEY, -- [主键] Datasheet在本库文件中的唯一标识符，UUID格式
            Title           TEXT NOT NULL,    -- [字段] Datasheet的文档标题
            FilePath        TEXT NOT NULL,    -- [字段] Datasheet的文件路径，是相对于库文件的安全相对路径
            MD5_Hash        TEXT NOT NULL,    -- [新增] 文件的MD5哈希值，用于完整性校验和去重
            Revision        TEXT,             -- [字段] Datasheet文档本身的版本号，如 "Rev. B"
            UploadTimestamp DATETIME DEFAULT CURRENT_TIMESTAMP, -- [字段] 该Datasheet记录被添加到本库的时间
            IsDeleted       INTEGER NOT NULL DEFAULT 0, -- [字段] 软删除标志，0表示未删除，1表示已删除
            UNIQUE (FilePath),                -- [约束] 同一个库文件内，文件路径应唯一
            UNIQUE (MD5_Hash)                 -- [约束] 同一个库文件内，文件MD5值应唯一，防止内容重复
        );
    )");
}

void DatasheetsCtrl::loadTable()
{
    QString sql = QString("select * from %1 where IsDeleted='0'").arg(tableName());
    QSqlQuery query(getCurrentDatabase());
    if(query.exec(sql))
    {
        while (query.next())
        {
            QSqlRecord record = query.record();

            DatasheetsModelPtr obj;
            obj.reset(new DatasheetsModel);
            obj->dbToModel(record);

            mAllObj.insert(obj->id, obj);
        }
    }
} 