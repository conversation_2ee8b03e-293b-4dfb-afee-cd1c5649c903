﻿/*
	Copyright 2006-2021 The QElectroTech Team
	This file is part of QElectroTech.

	QElectroTech is free software: you can redistribute it and/or modify
	it under the terms of the GNU General Public License as published by
	the Free Software Foundation, either version 2 of the License, or
	(at your option) any later version.

	QElectroTech is distributed in the hope that it will be useful,
	but WITHOUT ANY WARRANTY; without even the implied warranty of
	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
	GNU General Public License for more details.

	You should have received a copy of the GNU General Public License
	along with QElectroTech.  If not, see <http://www.gnu.org/licenses/>.
*/
#ifndef CONTACTORELEMENT_H
#define CONTACTORELEMENT_H

#include "../qetgraphicsitem/element.h"
#include "../contactormoveeventinterface.h"
#include "layoutDiagram/diagramToProject/dtp.h"
#include "sql/sqlCtrl/signalctrl.h"

class ContactorMoveEventInterface;
class ConnectorWiringElement;
class ContactorPusher;
class RegionRectItem;

/**
* @file
* @brief      ADE电缆映射图/芯线连接图连接器模型接触件元素
* <AUTHOR>
* @version    1.0
* @date       2023/11/30
* @todo
*/
class ContactorElement : public Element
{
    Q_OBJECT

public:
    /**
     * @brief The ContactorType enum 接触件针孔类型
     */
    enum ContactorType
    {
        Unknown = 0,   ///< 未知
        Pin     = 1,   ///< 针
        Hole    = 2    ///< 孔
    };

    /**
     * @brief The FunctionType enum 元素所表征的点位作用类型
     */
    enum FunctionType
    {
        Contactor = 1,  ///< 接触件
        Enclosure = 2  ///< 接壳
    };

    // 点位映射图记录信号名称和信号类型
    struct SignalInfo
    {
        QString SignalName = "";
        QString SignalCategory = "无";
    };


public:
    explicit ContactorElement(const ElementsLocation &,
                              int * = nullptr,
                              Element* parentElmt = nullptr,
                              const QString &uuid = QString(),
                              FunctionType fType = Contactor);
    ~ContactorElement() override;

private:
    ContactorElement(const Element&);

public:
    enum { ADEType = 8 };
    int adeType() const override {return ADEType;}

public:
    QRectF      boundingRect() const override;
    bool        fromXml(QDomElement&, QHash<int,Terminal*>&) override;
    QDomElement toXml(QDomDocument&, QHash<Terminal*,int>&)  const override;
    void changeLocation(const ElementsLocation &newLocation) override;
    bool canRotate() const override;

    // 属性
    /// contactor由元素的定义得到, 不能手动设置
    ContactorElement::ContactorType contactorType() const;

    FunctionType functionType() const {return mFunctionType;}
    void setFunctionType(FunctionType fType);

	QString adeFatherUuid() const {return mADEFatherUuid;}
    void    setADEFatherUuid(const QString &uuid);

    QString pointName();
    void    setPointName(const QString &name);

    SignalInfo signalInfo();
    void setSignalInfo(SignalInfo info);

    /// 调整文本位置
    void adjustTextToVisualPosition();
    void adjustTextPos();
    void setUserMovable(bool movable);

    void onSwitchPointKeyRelease(QKeyEvent *event);
    void onSwitchPointKeyPressed(QKeyEvent* event);
    void setDrawTerminal(bool draw);

    void pressEvent(QGraphicsSceneMouseEvent* event);
    void moveEvent(QGraphicsSceneMouseEvent* event);
    void releaseEvent(QGraphicsSceneMouseEvent* event);

    bool isMoving();

protected:
    QVariant itemChange(GraphicsItemChange change, const QVariant &value) override;

    void mousePressEvent(QGraphicsSceneMouseEvent* event) override;
    void mouseMoveEvent(QGraphicsSceneMouseEvent* event) override;
    void mouseReleaseEvent(QGraphicsSceneMouseEvent* event) override;
    void hoverEnterEvent(QGraphicsSceneHoverEvent*event) override;
    void hoverLeaveEvent(QGraphicsSceneHoverEvent*event) override;
    void mouseDoubleClickEvent(QGraphicsSceneMouseEvent *event) override;
    bool sceneEventFilter(QGraphicsItem *watched, QEvent *event) override ;

    void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget) override;

private:
    void doubleClickEvent(QGraphicsSceneMouseEvent* event);

    // 点位 交换高亮状态时，禁用导线加粗高亮
    void disableConductorHighLight(bool disable);


private:
    int mHalfLength = 10;
    int mTextOffset;  ///< 文本位置的偏移量

    FunctionType mFunctionType; ///< 元素所表征的点位作用类型

    QString mADEFatherUuid;
    ContactorMoveEventInterface *mMoveEvInterface = nullptr;

    bool mUserMovable;  ///< 是否能由用户拖动
    bool mTextEventFilterInstalled;

    ContactorPusher *mPusher = nullptr;
    bool mParentHover = false;

    bool addRegionRect = false;  ///< 起始点位显示高亮矩形（可进行点位交换）
    int mOriginalConductorZValue;

    bool mIsMoving = false;

    SignalInfo mSignalInfo;
};

#endif // CONTACTORELEMENT_H
