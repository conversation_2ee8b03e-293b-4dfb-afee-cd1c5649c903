﻿#include "agimanager.h"

#include "../diagram.h"

AGIManager::AGIManager(Diagram *diagram) :
    mDiagram(diagram)
{

}

AGIManager::~AGIManager()
{
    // BUG: 析构时记录的对象已被删除
//    qDebug() << Q_FUNC_INFO << mItems;
//    for (QGraphicsItem *qgi : mItems)
//    {
//        if (!mDiagram->items().contains(qgi) && qgi)
//        {
//            // 调试, 暂时不在此处析构
//            delete qgi;
//            qgi = nullptr;
//        }
//    }
}

void AGIManager::manage(QGraphicsItem *item)
{
    if (!mItems.contains(item))
        mItems.append(item);
}

void AGIManager::release(QGraphicsItem *item)
{
    if (mItems.contains(item))
        mItems.removeAll(item);
}

void AGIManager::deleteItem(QGraphicsItem *item)
{
    if (mItems.contains(item))
    {
        mItems.removeAll(item);
        delete item;
        item = nullptr;
    }
}
