#ifndef CATEGORIESMODEL_H
#define CATEGORIESMODEL_H

#include "sqlModelBase.h"
#include <QString>

class CategoriesModel : public SqlModelBase
{
    Q_OBJECT
    Q_PROPERTY(QString categoryUUID READ getCategoryUUID WRITE setCategoryUUID)
    Q_PROPERTY(QString name READ getName WRITE setName)
    Q_PROPERTY(QString description READ getDescription WRITE setDescription)
    Q_PROPERTY(QString parentCategoryUUID READ getParentCategoryUUID WRITE setParentCategoryUUID)
    Q_PROPERTY(QString irdi READ getIrdi WRITE setIrdi)
    Q_PROPERTY(QString usageUnitJson READ getUsageUnitJson WRITE setUsageUnitJson)
    Q_PROPERTY(bool isDeleted READ getIsDeleted WRITE setIsDeleted)

public:
    explicit CategoriesModel(QObject *parent = nullptr);

    void fromJson(const QJsonObject &json) override;
    QJsonObject toJsonObject() const override;
    void fromVariantMap(const QVariantMap &map) override;
    QVariantMap toVariantMap() const override;
    void reset() override;

    QString getCategoryUUID() const;
    void setCategoryUUID(const QString &uuid);

    QString getName() const;
    void setName(const QString &name);

    QString getDescription() const;
    void setDescription(const QString &description);

    QString getParentCategoryUUID() const;
    void setParentCategoryUUID(const QString &uuid);

    QString getIrdi() const;
    void setIrdi(const QString &irdi);

    QString getUsageUnitJson() const;
    void setUsageUnitJson(const QString &json);

    bool getIsDeleted() const;
    void setIsDeleted(bool isDeleted);

private:
    QString m_categoryUUID;
    QString m_name;
    QString m_description;
    QString m_parentCategoryUUID;
    QString m_irdi;
    QString m_usageUnitJson;
    bool m_isDeleted;
};

#endif // CATEGORIESMODEL_H 