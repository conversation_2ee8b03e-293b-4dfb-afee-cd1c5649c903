# -------------------------------------------------
# --- Project file for the Global DB Test ---
# -------------------------------------------------

QT      += core sql
CONFIG  += console c++11
TEMPLATE = app

# The name of the test executable
TARGET = test_globaldb

# Add the root of the partLibrary as an include path to find headers
INCLUDEPATH += $$PWD/..

# The source file for the test itself
SOURCES += $$PWD/test_globaldb.cpp

# Add the required source files from the library that this test depends on.
# These are relative to this .pro file's location (partLibrary/tests/)
SOURCES += \
    $$PWD/../database/sqlCtrlBase.cpp \
    $$PWD/../database/sqlModelBase.cpp \
    $$PWD/../database/globalDb/sqlCtrl/attributedefinitionsctrl.cpp \
    $$PWD/../database/globalDb/sqlCtrl/categoriesctrl.cpp \
    $$PWD/../database/globalDb/sqlCtrl/categoryattributelinksctrl.cpp \
    $$PWD/../database/globalDb/sqlCtrl/manufacturerctrl.cpp \
    $$PWD/../database/globalDb/sqlModel/attributedefinitionsmodel.cpp \
    $$PWD/../database/globalDb/sqlModel/categoriesmodel.cpp \
    $$PWD/../database/globalDb/sqlModel/categoryattributelinksmodel.cpp \
    $$PWD/../database/globalDb/sqlModel/manufacturermodel.cpp

# Note: The test executable will be placed in the build directory. 