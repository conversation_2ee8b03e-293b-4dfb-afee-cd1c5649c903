#include "categoryattributelinksctrl.h"
#include <QSqlQuery>
#include <QSqlError>
#include <QDebug>
#include <QVariant>

CategoryAttributeLinksCtrl::CategoryAttributeLinksCtrl(const QString &dbPath, QObject *parent)
    : SqlCtrlBase(dbPath, "Category_Attribute_Links", parent)
{
    m_uuidKeyName = "LinkUUID";
}

bool CategoryAttributeLinksCtrl::checkAndCreateTable()
{
    if (!m_db.isOpen()) {
        qCritical() << "Database not open!";
        return false;
    }

    QSqlQuery query(m_db);
    QString createTableQuery = R"(
        CREATE TABLE IF NOT EXISTS Category_Attribute_Links (
            LinkUUID          TEXT PRIMARY KEY,
            CategoryUUID      TEXT NOT NULL,
            AttributeUUID     TEXT NOT NULL,
            DefaultValue      TEXT,
            UNIQUE (CategoryUUID, AttributeUUID)
        )
    )";

    if (!query.exec(createTableQuery)) {
        qCritical() << "Failed to create Category_Attribute_Links table:" << query.lastError();
        return false;
    }
    return true;
}

bool CategoryAttributeLinksCtrl::insert(const CategoryAttributeLinksModel &model)
{
    return SqlCtrlBase::insert(model.toVariantMap());
}

bool CategoryAttributeLinksCtrl::update(const CategoryAttributeLinksModel &model)
{
    return SqlCtrlBase::update(model.getLinkUUID(), model.toVariantMap());
}

QSharedPointer<CategoryAttributeLinksModel> CategoryAttributeLinksCtrl::getByUUID(const QString &uuid)
{
    QVariantMap map = SqlCtrlBase::getByUUID(uuid);
    if (map.isEmpty()) {
        return nullptr;
    }
    auto model = QSharedPointer<CategoryAttributeLinksModel>::create();
    model->fromVariantMap(map);
    return model;
}

QList<QSharedPointer<CategoryAttributeLinksModel>> CategoryAttributeLinksCtrl::getAll()
{
    QList<QVariantMap> maps = SqlCtrlBase::getAll();
    QList<QSharedPointer<CategoryAttributeLinksModel>> models;
    for (const auto &map : maps) {
        auto model = QSharedPointer<CategoryAttributeLinksModel>::create();
        model->fromVariantMap(map);
        models.append(model);
    }
    return models;
}

QList<QSharedPointer<CategoryAttributeLinksModel>> CategoryAttributeLinksCtrl::getLinksForCategory(const QString &categoryUUID)
{
    QList<QSharedPointer<CategoryAttributeLinksModel>> models;
    if (!m_db.isOpen()) {
        qCritical() << "Database not open!";
        return models;
    }

    QSqlQuery query(m_db);
    query.prepare(QString("SELECT * FROM %1 WHERE CategoryUUID = :CategoryUUID").arg(m_tableName));
    query.bindValue(":CategoryUUID", categoryUUID);

    if (!query.exec()) {
        qCritical() << "Failed to query links for category:" << query.lastError();
        return models;
    }

    while (query.next()) {
        QVariantMap map;
        for (int i = 0; i < query.record().count(); ++i) {
            map.insert(query.record().fieldName(i), query.value(i));
        }
        auto model = QSharedPointer<CategoryAttributeLinksModel>::create();
        model->fromVariantMap(map);
        models.append(model);
    }
    return models;
}

bool CategoryAttributeLinksCtrl::deleteByCategoryAndAttribute(const QString &categoryUUID, const QString &attributeUUID)
{
    if (!m_db.isOpen()) {
        qCritical() << "Database not open!";
        return false;
    }

    QSqlQuery query(m_db);
    query.prepare(QString("DELETE FROM %1 WHERE CategoryUUID = :CategoryUUID AND AttributeUUID = :AttributeUUID").arg(m_tableName));
    query.bindValue(":CategoryUUID", categoryUUID);
    query.bindValue(":AttributeUUID", attributeUUID);

    if (!query.exec()) {
        qCritical() << "Failed to delete link by category and attribute:" << query.lastError();
        return false;
    }
    return true;
} 