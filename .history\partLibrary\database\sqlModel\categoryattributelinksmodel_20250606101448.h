#ifndef CATEGORYATTRIBUTELINKSMODEL_H
#define CATEGORYATTRIBUTELINKSMODEL_H

#include "sqlModelBase.h"
#include <QString>

class CategoryAttributeLinksModel : public SqlModelBase
{
    Q_OBJECT
    Q_PROPERTY(QString linkUUID READ getLinkUUID WRITE setLinkUUID)
    Q_PROPERTY(QString categoryUUID READ getCategoryUUID WRITE setCategoryUUID)
    Q_PROPERTY(QString attributeUUID READ getAttributeUUID WRITE setAttributeUUID)
    Q_PROPERTY(QString defaultValue READ getDefaultValue WRITE setDefaultValue)

public:
    explicit CategoryAttributeLinksModel(QObject *parent = nullptr);

    void fromJson(const QJsonObject &json) override;
    QJsonObject toJsonObject() const override;
    void fromVariantMap(const QVariantMap &map) override;
    QVariantMap toVariantMap() const override;
    void reset() override;

    QString getLinkUUID() const;
    void setLinkUUID(const QString &linkUUID);

    QString getCategoryUUID() const;
    void setCategoryUUID(const QString &categoryUUID);

    QString getAttributeUUID() const;
    void setAttributeUUID(const QString &attributeUUID);

    QString getDefaultValue() const;
    void setDefaultValue(const QString &defaultValue);

private:
    QString m_linkUUID;
    QString m_categoryUUID;
    QString m_attributeUUID;
    QString m_defaultValue;
};

#endif // CATEGORYATTRIBUTELINKSMODEL_H 