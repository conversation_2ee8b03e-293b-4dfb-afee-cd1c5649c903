﻿#ifndef ADERETROCMOPATIBILITY_H
#define ADERETROCMOPATIBILITY_H

#include <QString>
#include <QDomElement>
#include <QDomDocument>

/**
* @file
* @brief      在QET工程中获取ADE相关信息, 对ADE版本升级进行兼容性处理
* <AUTHOR>
* @version    1.0
* @date       2023/09/21
* @todo
*/
class ADEProject
{
public:
    ADEProject();
    ~ADEProject();

public:
    bool mIfElmtCollectionUpdated;///< （在读取工程时）接口元素定义是否被更新

public:
    /// ADE版本信息
    static QString ADEVersion();
    static QString ADEVersionTagName() {return "ADEVersion";}

    /// 检查并更新接工程xml信息
    void checkAndUpdateProjectDocument(QDomDocument &xml_project);

private:


};

#endif // ADERETROCMOPATIBILITY_H
