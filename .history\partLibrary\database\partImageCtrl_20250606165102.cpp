#include "partImageCtrl.h"
#include <QSqlQuery>
#include <QSqlRecord>
#include <QSqlError>
#include <QDebug>

namespace part_library
{
namespace database
{

bool PartImageCtrl::addImage(QSqlDatabase &db, const PartImage &image)
{
    QSqlQuery query(db);
    query.prepare(image.getInsertSqlTemplate());
    query.bindValue(":ImageUUID", image.imageUUID);
    query.bindValue(":PartUUID", image.partUUID);
    query.bindValue(":Pixmap", image.pixmap);
    query.bindValue(":ImageType", image.imageType);
    query.bindValue(":DisplayOrder", image.displayOrder);
    query.bindValue(":Description", image.description);
    query.bindValue(":IsDeleted", image.isDeleted);

    if (!query.exec()) {
        qWarning() << "PartImageCtrl::addImage failed:" << query.lastError().text();
        return false;
    }
    return true;
}

bool PartImageCtrl::updateImage(QSqlDatabase &db, const PartImage &image)
{
    QSqlQuery query(db);
    query.prepare(image.getUpdateSqlTemplate());
    query.bindValue(":PartUUID", image.partUUID);
    query.bindValue(":Pixmap", image.pixmap);
    query.bindValue(":ImageType", image.imageType);
    query.bindValue(":DisplayOrder", image.displayOrder);
    query.bindValue(":Description", image.description);
    query.bindValue(":IsDeleted", image.isDeleted);
    query.bindValue(":ImageUUID", image.imageUUID);

    if (!query.exec()) {
        qWarning() << "PartImageCtrl::updateImage failed:" << query.lastError().text();
        return false;
    }
    return true;
}

bool PartImageCtrl::removeImage(QSqlDatabase &db, const QString &imageUUID)
{
    QSqlQuery query(db);
    query.prepare("UPDATE PartImages SET IsDeleted = 1 WHERE ImageUUID = :ImageUUID");
    query.bindValue(":ImageUUID", imageUUID);

    if (!query.exec()) {
        qWarning() << "PartImageCtrl::removeImage failed:" << query.lastError().text();
        return false;
    }
    return true;
}

QList<PartImage> PartImageCtrl::getImagesForPart(QSqlDatabase &db, const QString &partUUID)
{
    QList<PartImage> images;
    QSqlQuery query(db);
    query.prepare("SELECT * FROM PartImages WHERE PartUUID = :PartUUID AND IsDeleted = 0 ORDER BY DisplayOrder");
    query.bindValue(":PartUUID", partUUID);

    if (!query.exec()) {
        qWarning() << "PartImageCtrl::getImagesForPart failed:" << query.lastError().text();
        return images;
    }

    while (query.next()) {
        PartImage image;
        image.dbToModel(query.record());
        images.append(image);
    }

    return images;
}

} // namespace database
} // namespace part_library 