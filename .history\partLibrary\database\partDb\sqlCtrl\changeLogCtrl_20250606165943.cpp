#include "changeLogCtrl.h"
#include <QSqlQuery>
#include <QVariant>

ChangeLogCtrl::ChangeLogCtrl(const QSqlDatabase &db) : SqlCtrlBase(db)
{
}

bool ChangeLogCtrl::add(const ChangeLogModel &log)
{
    return insertTableRecord(log);
}

QList<ChangeLogModel> ChangeLogCtrl::getLogsForRecord(const QString &recordUuid) const
{
    QList<ChangeLogModel> logs;
    QSqlQuery query(mDatabase);
    QString sql = QString("SELECT * FROM %1 WHERE %2 = :record_uuid ORDER BY %3 DESC")
                      .arg(tableName(), ChangeLogModel::FIELD_RECORD_UUID, ChangeLogModel::FIELD_TIMESTAMP);
    query.prepare(sql);
    query.bindValue(":record_uuid", recordUuid);

    if (query.exec())
    {
        while (query.next())
        {
            ChangeLogModel log;
            log.dbToModel(query.record());
            logs.append(log);
        }
    }
    return logs;
}

QList<ChangeLogModel> ChangeLogCtrl::getAllLogs() const
{
    QList<ChangeLogModel> logs;
    QSqlQuery query(mDatabase);
    QString sql = QString("SELECT * FROM %1 ORDER BY %2 DESC")
                      .arg(tableName(), ChangeLogModel::FIELD_TIMESTAMP);

    if (query.exec())
    {
        while (query.next())
        {
            ChangeLogModel log;
            log.dbToModel(query.record());
            logs.append(log);
        }
    }
    return logs;
}

bool ChangeLogCtrl::recordExist(const QString &id) const
{
    QSqlQuery query(mDatabase);
    query.prepare(QString("SELECT COUNT(*) FROM %1 WHERE %2 = :id").arg(tableName(), primaryKeyField()));
    query.bindValue(":id", id);
    if (query.exec() && query.next())
    {
        return query.value(0).toInt() > 0;
    }
    return false;
}

QString ChangeLogCtrl::tableSql() const
{
    return R"(
        CREATE TABLE ChangeLog (
            ChangeLogUUID   TEXT PRIMARY KEY,
            Timestamp       DATETIME DEFAULT CURRENT_TIMESTAMP,
            UserName        TEXT,
            Action          TEXT NOT NULL CHECK(Action IN ('CREATE', 'UPDATE', 'DELETE')),
            RecordUUID      TEXT NOT NULL,
            FieldName       TEXT,
            OldValue        TEXT,
            NewValue        TEXT,
            Description     TEXT
        );
    )";
} 