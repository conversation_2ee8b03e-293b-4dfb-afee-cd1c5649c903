﻿#ifndef ELEMENTEVENTDOCKINTERFACE_H
#define ELEMENTEVENTDOCKINTERFACE_H

#include <QObject>
#include <QGraphicsLineItem>

#include "sql/sqlModel/connectormodel.h"

class QGraphicsSceneMouseEvent;
class Element;
class Diagram;
class InterfaceElement;
class ADELogicCable;

/**
* @file
* @brief      鼠标拖动电缆上接口元素时管理接口对接行为
* <AUTHOR>
* @version    0.1
* @date       2023/7/12
* @todo
*/
class ElementEventDockInterface : public QObject
{
    Q_OBJECT
public:
    explicit ElementEventDockInterface(InterfaceElement *element, QObject *parent = nullptr);
    ~ElementEventDockInterface();

    void mousePressEvent       (QGraphicsSceneMouseEvent *event);
    void mouseMoveEvent(QGraphicsSceneMouseEvent *event);
    void mouseReleaseEvent     (QGraphicsSceneMouseEvent *event);

    void redockMouseMoveEvent  (QGraphicsSceneMouseEvent *event);

    bool redockMode() const {return mReDockMode;}

    QPointF validMovment() const;

public:
    /**
     * @brief getInterfaceDockPos 获取接口（trigger）对接后的位置信息
     * @param other               对接的接口元素
     * @return                    QPair<position, rotation>
     */
     static QPair<QPointF, int> getInterfaceDockPos(InterfaceElement *trigger, InterfaceElement *other);

     static QPointF getCableInterfaceUndockPos(InterfaceElement *interfaceElmt);

     static Qt::KeyboardModifier redockKeyModifier() {return Qt::AltModifier;}

     static bool cableCanConnect(ADELogicCable *cable1, ADELogicCable* cable2, QString &reason);

private:
    /**
     * @brief setDiagramMouseInteractionEnabled 设置布置图鼠标交互动作是否使能
     * @param enabled                           是否使能
     */
    void setDiagramMouseInteractionEnabled(bool enabled);

    Diagram *diagram() const;

private:
    /**
     * @brief drawAnchorLine 在拖动元素与目标元素间画引导线
     * @param target         目标元素
     */
    void drawAnchorLine(InterfaceElement *target);

    /**
     * @brief getElementAnchorLinePoint 获取接口元素引导线起点
     * @param elmt
     * @return
     */
    QPointF getElementAnchorLinePoint(InterfaceElement *elmt);

    /**
     * @brief hideAnchorLine 隐藏辅助线
     */
    void hideAnchorLine();

    /**
     * @brief connectorCanDock 通过连接器uuid校验两个连接器是否能对接
     * @param uuid1
     * @param uuid2
     * @return
     */
    bool connectorCanDock(const QString &uuid1, const QString &uuid2);

    /**
     * @brief connectorCanDock 布置图接口对接连接器兼容性限制
     * @return
     */
    bool connectorDockLimited();

signals:

private:
    const int mDockSearchRadius = 30;   ///< 拖动接口元素移动时搜索可对接接口元素的半径
    const double mDockDistance = 20.0;  ///< 对接距离阈值

private:
    QPointF mInitialPos;

    InterfaceElement *mElement = nullptr;        ///< 触发事件的接口元素
    bool mCanDock = true;

    QList<InterfaceElement *> mLastMoveElements; ///< 上次鼠标移动时搜索范围内的全部接口元素
    int mLastMoveRoatation = 0;         ///< 上次移动接口的旋转角度
    bool mLastMoveDocked = false;       ///< 上次移动接口是否触发了对接行为
    QGraphicsLineItem *mDockAnchorLine = nullptr; ///< 拖动接口元素时与附近最近接口元素对接引导线

    InterfaceElement *mDockedElement = nullptr;
    QPointF mFirstMovePos;          ///< 第一次移动时接口的位置
    int mFirstMoveRotation = 0;     ///< 第一次移动时接口的旋转角度

    bool mReDockMode = false;
    QGraphicsEllipseItem *mUnDockRangeCircle = nullptr;
    InterfaceElement *mUnDockedElemnet = nullptr;
    bool mUnDocked = false;

    QPointF mLastMovePos;
};

#endif // ELEMENTEVENTDOCKINTERFACE_H
