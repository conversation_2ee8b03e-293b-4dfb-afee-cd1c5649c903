#include "attributedefinitionsmodel.h"
#include <QJsonDocument>
#include <QJsonArray>
#include <QJsonObject>

// 字段名称定义
const QString AttributeDefinitionsModel::FIELD_ID = "AttributeUUID";
const QString AttributeDefinitionsModel::FIELD_NAME = "Name";
const QString AttributeDefinitionsModel::FIELD_DATA_TYPE = "DataType";
const QString AttributeDefinitionsModel::FIELD_OPTIONS_JSON = "Options_JSON";
const QString AttributeDefinitionsModel::FIELD_DESCRIPTION = "Description";
const QString AttributeDefinitionsModel::FIELD_IRDI = "IRDI";
const QString AttributeDefinitionsModel::FIELD_UNIT = "Unit";
const QString AttributeDefinitionsModel::FIELD_DEFAULT_VALUE = "DefaultValue";
const QString AttributeDefinitionsModel::FIELD_VALIDATION_RULE = "ValidationRule";
const QString AttributeDefinitionsModel::FIELD_IS_USER_DEFINED = "IsUserDefined";
const QString AttributeDefinitionsModel::FIELD_IS_DELETED = "IsDeleted";

QVariantMap AttributeDefinitionsModel::modelToDbMap() const
{
    QVariantMap map;
    map.insert(FIELD_ID, id);
    map.insert(FIELD_NAME, name);
    map.insert(FIELD_DATA_TYPE, dataType);
    map.insert(FIELD_OPTIONS_JSON, optionsToJson());
    map.insert(FIELD_DESCRIPTION, description);
    map.insert(FIELD_IRDI, irdi);
    map.insert(FIELD_UNIT, unit);
    map.insert(FIELD_DEFAULT_VALUE, defaultValue);
    map.insert(FIELD_VALIDATION_RULE, validationRule);
    map.insert(FIELD_IS_USER_DEFINED, isUserDefined);
    return map;
}

void AttributeDefinitionsModel::dbToModel(const QSqlRecord &record)
{
    QMap<QString, QVariant> infos = getRecordValues(record);

    if (infos.contains(FIELD_ID)) this->id = infos[FIELD_ID].toString();
    if (infos.contains(FIELD_NAME)) this->name = infos[FIELD_NAME].toString();
    if (infos.contains(FIELD_DATA_TYPE)) this->dataType = infos[FIELD_DATA_TYPE].toString();
    if (infos.contains(FIELD_OPTIONS_JSON)) this->optionsFromJson(infos[FIELD_OPTIONS_JSON].toString());
    if (infos.contains(FIELD_DESCRIPTION)) this->description = infos[FIELD_DESCRIPTION].toString();
    if (infos.contains(FIELD_IRDI)) this->irdi = infos[FIELD_IRDI].toString();
    if (infos.contains(FIELD_UNIT)) this->unit = infos[FIELD_UNIT].toString();
    if (infos.contains(FIELD_DEFAULT_VALUE)) this->defaultValue = infos[FIELD_DEFAULT_VALUE].toString();
    if (infos.contains(FIELD_VALIDATION_RULE)) this->validationRule = infos[FIELD_VALIDATION_RULE].toString();
    if (infos.contains(FIELD_IS_USER_DEFINED)) this->isUserDefined = infos[FIELD_IS_USER_DEFINED].toInt();
}

void AttributeDefinitionsModel::optionsFromJson(const QString &json)
{
    options.clear();
    QJsonDocument doc = QJsonDocument::fromJson(json.toUtf8());
    if (doc.isArray()) {
        QJsonArray jsonArray = doc.array();
        for (const QJsonValue &value : jsonArray) {
            options.append(value.toString());
        }
    }
}

QString AttributeDefinitionsModel::optionsToJson() const
{
    QJsonArray jsonArray = QJsonArray::fromStringList(options);
    return QJsonDocument(jsonArray).toJson(QJsonDocument::Compact);
} 