﻿#include "portsnappointitem.h"

#include <QPainter>
#include <QCursor>

#include "3rdParty/qet/sources/qetgraphicsitem/element.h"

PortSnapPointItem::PortSnapPointItem(Element *parentElmt) :
    mParentElmt(parentElmt)
{
    setParentItem(mParentElmt);

    init();
}

PortSnapPointItem::~PortSnapPointItem()
{

}

void PortSnapPointItem::init()
{
    setAcceptHoverEvents(true);
    setFlag(QGraphicsItem::ItemIgnoresTransformations);
    setFlags(QGraphicsItem::ItemIsMovable| QGraphicsItem::ItemIsSelectable);
    setZValue(100000);
    setSize(6);
    // m_color = QColor(127, 255, 127);
    // m_color.setAlpha(80);
}

QRectF PortSnapPointItem::boundingRect() const
{
    return mBr;
}

void PortSnapPointItem::setSize(qreal size)
{
    prepareGeometryChange();
    m_current_size = size;
    m_handler_rect.setRect(0-m_current_size/2, 0-m_current_size/2, m_current_size, m_current_size);
    mBr.setRect(-1-m_current_size/2, -1-m_current_size/2, m_current_size+2, m_current_size+2);
    update();
}

void PortSnapPointItem::paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget)
{
    Q_UNUSED(option)
    Q_UNUSED(widget)

    painter->save();
    painter->setBrush(QBrush(m_color));
    QPen pen(QBrush(m_color),
         2,
         Qt::SolidLine,
         Qt::SquareCap,
         Qt::MiterJoin);
    pen.setCosmetic(true);
    painter->setPen(pen);
    painter->setRenderHint(QPainter::Antialiasing, true);
    painter->drawEllipse(m_handler_rect);
    painter->restore();
}

ContactorPortSnapPointItem::ContactorPortSnapPointItem(Element *parent) :
    PortSnapPointItem(nullptr)
{
    setParentItem(parent);

    setAcceptHoverEvents(true);
    setFlag(QGraphicsItem::ItemIgnoresTransformations);
    setFlags(QGraphicsItem::ItemIsMovable| QGraphicsItem::ItemIsSelectable);
    setZValue(100000);
    setSize(12);
    // m_color = QColor(24, 153, 69);
    // m_color.setAlpha(100);
}

ContactorPortSnapPointItem::~ContactorPortSnapPointItem()
{

}

QRectF ContactorPortSnapPointItem::boundingRect() const
{
    return mBr;
}

void ContactorPortSnapPointItem::paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget)
{
    // PortSnapPointItem::paint(painter, option, widget);
    painter -> save();

    QColor color = QColor(69, 137, 255);      ///< 元素高亮颜色

    qreal gradient_radius = qMin(boundingRect().width(),
                                 boundingRect().height()) / 2.0;
    QRadialGradient gradient(
        boundingRect().center(),
        gradient_radius,
        boundingRect().center()
    );

    gradient.setColorAt(0.0, QColor::fromRgb(color.red(), color.green(), color.blue(), 255));
    gradient.setColorAt(1.0, QColor::fromRgb(color.red(), color.green(), color.blue(), 0));
    QBrush brush(gradient);

    painter -> setPen(Qt::NoPen);
    painter -> setBrush(brush);
    // Le dessin se fait a partir du rectangle delimitant
    painter -> drawRoundedRect(boundingRect().adjusted(-5, -5, 5, 5),
                               boundingRect().width() / 2,
                               boundingRect().height() / 2);
    painter -> restore();
}
