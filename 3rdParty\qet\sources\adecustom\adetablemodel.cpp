﻿#include "adetablemodel.h"

#include "../../dataBase/projectdatabase.h"
#include "../../qetapp.h"
#include "../../qetinformation.h"
#include "../../qetproject.h"
#include "../../qetxml.h"

#include <QSqlError>
#include <QSqlRecord>

/**
	@brief ADETableModel::ADETableModel
	@param project :project of this nomenclature
	@param parent : parent QObject
*/
ADETableModel::ADETableModel(const QString &uuid, QObject *parent) :
    QAbstractTableModel(parent),
    m_uuid(uuid),
    m_row_separator("|")
{

}


/**
	@brief ADETableModel::rowCount
	Reimplemented for QAbstractTableModel
	@param parent
	@return
*/
int ADETableModel::rowCount(const QModelIndex &parent) const
{
	if (parent.isValid())
		return 0;
	
    return  m_content.count();
}

/**
	@brief ADETableModel::columnCount
	Reimplemented for QAbstractTableModel
	@param parent
	@return
*/
int ADETableModel::columnCount(const QModelIndex &parent) const
{
	if (parent.isValid())
		return 0;
	
    if (m_content.count()) {
        return m_content.first().count();
	}
	
	return 0;
}

/**
	@brief ADETableModel::setHeaderData
	Reimplemented from QAbstractTableModel.
	Only horizontal orientation is accepted.
	@param section
	@param orientation
	@param value
	@param role
	@return
*/
bool ADETableModel::setHeaderData(int section, Qt::Orientation orientation, const QVariant &value, int role)
{
	if (orientation == Qt::Vertical) {
		return false;
	}
	auto hash_ = m_header_data.value(section);
	hash_.insert(role, value);
	m_header_data.insert(section, hash_);
	emit headerDataChanged(orientation, section, section);
	return true;
}

/**
	@brief ADETableModel::headerData
	Reimplemented from QAbstractTableModel.
	@param section
	@param orientation
	@param role
	@return
*/
QVariant ADETableModel::headerData(int section, Qt::Orientation orientation, int role) const
{
	if (orientation == Qt::Vertical) {
		return QVariant();
	}
	
	if (m_header_data.contains(section))
	{
		auto hash_ = m_header_data.value(section);
		if (role == Qt::DisplayRole && !hash_.contains(Qt::DisplayRole)) { //special case to have the same behavior as Qt
			return hash_.value(Qt::EditRole);
		}
		return m_header_data.value(section).value(role);
	}
	return QVariant();
}

/**
	@brief ADETableModel::setData
	Only store the data for the index 0.0
	@param index
	@param value
	@param role
	@return
*/
bool ADETableModel::setData(const QModelIndex &index, const QVariant &value, int role)
{
    // 只修改单元格属性，不修改内容
    if (!index.isValid() || index.row() != 0 || index.column() != 0) {
        // BUG: 寻找新的判断方法
        //return false;
	}
	m_index_0_0_data.insert(role, value);
	emit dataChanged(index, index, QVector<int>(role));
	return true;
}

/**
	@brief ADETableModel::data
	Reimplemented for QAbstractTableModel
	@param index
	@param role
	@return
*/
QVariant ADETableModel::data(const QModelIndex &index, int role) const
{
	if (!index.isValid())
		return QVariant();
	
	if (index.row() == 0 &&
		index.column() == 0 &&
		role != Qt::DisplayRole) {
		return m_index_0_0_data.value(role);
	}
	
	if (role == Qt::DisplayRole) {
        if (m_content.count() > index.row())
        {
            QStringList rowTexts = m_content.at(index.row());
            if (rowTexts.size() > index.column())
                return QVariant(rowTexts.at(index.column()));
//                return QVariant("123");
        }
//        QVariant v(m_content.at(index.row()).at(index.column()));
//		return v;
	}
	
    return QVariant();
}

void ADETableModel::setContent(const QStringList &headers,
                               const QVector<QStringList> &wholeContent)
{
    emit beginResetModel();

    setHeaderString(headers);
    m_content = wholeContent;
    //qDebug() << Q_FUNC_INFO << headers << m_content;
    emit endResetModel();
}


/**
	@brief ADETableModel::toXml
	Save the model to xml,since model can have unlimited data we only save few data (only these used by qelectrotech).
	The query, all header data. and some data of index::(0,0). All other data are not saved.
	@param document
	@return
*/
QDomElement ADETableModel::toXml(QDomDocument &document) const
{
	auto dom_element = document.createElement(xmlTagName());
    dom_element.setAttribute("uuid", m_uuid);
	
	//Identifier
	auto dom_identifier = document.createElement("identifier");
	auto dom_identifier_text = document.createTextNode(m_identifier);
	dom_identifier.appendChild(dom_identifier_text);
	dom_element.appendChild(dom_identifier);
	
	//Add index 0,0 data
	auto index_00 = document.createElement("index00");
	index_00.setAttribute("font", m_index_0_0_data.value(Qt::FontRole).toString());
	auto me = QMetaEnum::fromType<Qt::Alignment>();
	index_00.setAttribute("alignment", me.valueToKey(m_index_0_0_data.value(Qt::TextAlignmentRole).toInt()));
	dom_element.appendChild(index_00);
	index_00.setAttribute("margins", m_index_0_0_data.value(Qt::UserRole+1).toString());
	
	//header data
	QHash<int, QList<int>> horizontal_;
	for (auto key : m_header_data.keys())
	{
		auto list = m_header_data.value(key).keys();
		horizontal_.insert(key, list);
	}

	dom_element.appendChild(QETXML::modelHeaderDataToXml(document, this, horizontal_, QHash<int, QList<int>>()));

    // content
    auto dom_content = document.createElement("content");
    for (int rowIndex = 0; rowIndex < m_content.count(); rowIndex++)
    {
        QString rowTextsCombined;   // etc: A|B|C

        QStringList rowTexts = m_content.at(rowIndex);
        for (int colIndex = 0; colIndex < rowTexts.size(); colIndex++)
        {
            QString text = rowTexts.at(colIndex);
            rowTextsCombined += text.remove(m_row_separator);

            if (colIndex != (rowTexts.size() - 1))
                rowTextsCombined += m_row_separator;
        }

        auto dom_row = document.createElement("row");
        auto dom_row_text = document.createTextNode(rowTextsCombined);
        dom_row.appendChild(dom_row_text);
        dom_content.appendChild(dom_row);
    }
    dom_element.appendChild(dom_content);
	
	return dom_element;
}

/**
	@brief ADETableModel::fromXml
	Restore the model from xml
	@param element
*/
void ADETableModel::fromXml(const QDomElement &element)
{
	if (element.tagName() != xmlTagName())
		return;
	
    m_uuid = element.attribute("uuid");

	setIdentifier(element.firstChildElement("identifier").text());
	
	//Index 0,0
	auto index_00 = element.firstChildElement("index00");
	QFont font_;
	font_.fromString(index_00.attribute("font"));
	m_index_0_0_data.insert(Qt::FontRole, font_);
	auto me = QMetaEnum::fromType<Qt::Alignment>();
	m_index_0_0_data.insert(Qt::TextAlignmentRole, me.keyToValue(index_00.attribute("alignment").toStdString().data()));
	m_index_0_0_data.insert(Qt::UserRole+1, index_00.attribute("margins"));
	
    // header_data
	QETXML::modelHeaderDataFromXml(element.firstChildElement("header_data"), this);

    // content
    auto content = element.firstChildElement("content");
    for (QDomElement child : QETXML::directChild(content, "row"))
    {
        QString rowTextsCombined = child.text();
        QStringList rowTexts = rowTextsCombined.split(m_row_separator);
        m_content << rowTexts;
    }
}

/**
	@brief ADETableModel::setIdentifier
	Set the identifier of this model to identifier
	@param identifier
*/
void ADETableModel::setIdentifier(const QString &identifier) {
	m_identifier = identifier;
}

/**
	@brief ADETableModel::dataBaseUpdated
	slot called when the project database is updated
*/
void ADETableModel::dataBaseUpdated()
{
    auto original_record = m_content;
    //fillValue();
    auto new_record = m_content;
    m_content = original_record;
	
    if (new_record.size() != m_content.size())
	{
		emit beginResetModel();
        m_content = new_record;
		emit endResetModel();
	}
	else
	{
        m_content = new_record;
        auto row = m_content.size();
        auto col = row ? m_content.first().count() : 1;
		
		emit dataChanged(this->index(0,0), this->index(row-1, col-1), QVector<int>(Qt::DisplayRole));
	}
}

void ADETableModel::setHeaderString(const QStringList &headerLabels)
{
    for (auto i=0 ; i<headerLabels.count() ; ++i)
	{
        QString header_name = headerLabels.at(i);
        this->setHeaderData(i, Qt::Horizontal, header_name, Qt::DisplayRole);
	}
}

void ADETableModel::setUuid(const QString &uuid)
{
    m_uuid = uuid;
}
