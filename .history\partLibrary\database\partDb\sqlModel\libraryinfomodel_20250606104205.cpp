#include "libraryinfomodel.h"

// 字段名称定义
const QString LibraryInfoModel::FIELD_ID = "LibraryUUID";
const QString LibraryInfoModel::FIELD_LIBRARY_NAME = "LibraryName";
const QString LibraryInfoModel::FIELD_VERSION = "Version";
const QString LibraryInfoModel::FIELD_IS_OFFICIAL = "IsOfficial";
const QString LibraryInfoModel::FIELD_DESCRIPTION = "Description";
const QString LibraryInfoModel::FIELD_CREATION_TIMESTAMP = "CreationTimestamp";
const QString LibraryInfoModel::FIELD_CREATED_BY = "CreatedBy";
const QString LibraryInfoModel::FIELD_LAST_MODIFIED_TIMESTAMP = "LastModifiedTimestamp";

QVariantMap LibraryInfoModel::modelToDbMap() const
{
    QVariantMap map;
    map.insert(FIELD_ID, id);
    map.insert(FIELD_LIBRARY_NAME, libraryName);
    map.insert(FIELD_VERSION, version);
    map.insert(FIELD_IS_OFFICIAL, isOfficial);
    map.insert(FIELD_DESCRIPTION, description);
    map.insert(FIELD_CREATION_TIMESTAMP, creationTimestamp);
    map.insert(FIELD_CREATED_BY, createdBy);
    map.insert(FIELD_LAST_MODIFIED_TIMESTAMP, lastModifiedTimestamp);
    return map;
}

void LibraryInfoModel::dbToModel(const QSqlRecord &record)
{
    QMap<QString, QVariant> infos = getRecordValues(record);

    if (infos.contains(FIELD_ID)) this->id = infos[FIELD_ID].toString();
    if (infos.contains(FIELD_LIBRARY_NAME)) this->libraryName = infos[FIELD_LIBRARY_NAME].toString();
    if (infos.contains(FIELD_VERSION)) this->version = infos[FIELD_VERSION].toString();
    if (infos.contains(FIELD_IS_OFFICIAL)) this->isOfficial = infos[FIELD_IS_OFFICIAL].toInt();
    if (infos.contains(FIELD_DESCRIPTION)) this->description = infos[FIELD_DESCRIPTION].toString();
    if (infos.contains(FIELD_CREATION_TIMESTAMP)) this->creationTimestamp = infos[FIELD_CREATION_TIMESTAMP].toDateTime();
    if (infos.contains(FIELD_CREATED_BY)) this->createdBy = infos[FIELD_CREATED_BY].toString();
    if (infos.contains(FIELD_LAST_MODIFIED_TIMESTAMP)) this->lastModifiedTimestamp = infos[FIELD_LAST_MODIFIED_TIMESTAMP].toDateTime();
} 