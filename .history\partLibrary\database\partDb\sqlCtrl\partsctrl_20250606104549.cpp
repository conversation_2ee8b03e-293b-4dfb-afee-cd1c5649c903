#include "partsctrl.h"

#include "3rdParty/logBusiness/logbusiness.h"

#include <QApplication>

PartsCtrl::PartsCtrl(const QSqlDatabase &db) :
    SqlCtrlBase(db)
{
    if(!db.isValid())
    {
        LOG_ERROR("db not valid");
    }
    else
    {
        loadTable();
    }
}

PartsCtrl::~PartsCtrl()
{
    for(auto itor = mAllObj.begin(); itor != mAllObj.end(); )
    {
        auto obj = itor.value();
        obj.reset();

        mAllObj.erase(itor++);
    }
}

PartsModelList PartsCtrl::fetchAll() const
{
    return mAllObj;
}

PartsModelPtr PartsCtrl::fetch(const QString &id) const
{
    if (modelExist(id))
    {
        return mAllObj.value(id);
    }
    else
    {
        return nullptr;
    }
}

bool PartsCtrl::modelExist(const QString &id) const
{
    return mAllObj.contains(id);
}

bool PartsCtrl::insert(const PartsModelPtr &obj)
{
    if (obj.get() == nullptr)
        return false;

    if (insertTableRecord(*obj.get()))
    {
        mAllObj.insert(obj->id, obj);
        return true;
    }
    else
    {
        return false;
    }
}

bool PartsCtrl::modify(const PartsModelPtr &newValueObj)
{
    if (newValueObj.get() == nullptr)
        return false;

    QString id = newValueObj->id;
    if (!mAllObj.contains(id))
    {
        LOG_ERROR(QString("无法修改记录：该记录不存在 %1").arg(id));
        return false;
    }

    if (updateTableRecord(*newValueObj.get()))
    {
        auto obj = mAllObj.value(id);
        obj->partNumber = newValueObj->partNumber;
        obj->categoryUUID = newValueObj->categoryUUID;
        obj->manufacturerUUID = newValueObj->manufacturerUUID;
        obj->description = newValueObj->description;
        obj->lifecycleStatus = newValueObj->lifecycleStatus;
        obj->coreAttributesJson = newValueObj->coreAttributesJson;
        obj->customAttributesJson = newValueObj->customAttributesJson;
        obj->tagsJson = newValueObj->tagsJson;
        obj->creationTimestamp = newValueObj->creationTimestamp;
        obj->createdBy = newValueObj->createdBy;
        obj->lastModifiedTimestamp = newValueObj->lastModifiedTimestamp;
        obj->lastModifiedBy = newValueObj->lastModifiedBy;
        obj->revision = newValueObj->revision;

        return true;
    }
    else
    {
        return false;
    }
}

bool PartsCtrl::remove(const QString &id)
{
    if (!mAllObj.contains(id))
    {
        LOG_ERROR(QString("无法删除记录：该记录不存在 %1").arg(id));
        return false;
    }

    if (deleteTableRecord(id))
    {
        auto obj = mAllObj.take(id);
        obj.reset();

        return true;
    }
    else
    {
        return false;
    }
}

bool PartsCtrl::recordExist(const QString &id) const
{
    return mAllObj.contains(id);
}

QString PartsCtrl::tableSql() const
{
    return QString(R"(
        CREATE TABLE Parts (
            PartUUID         TEXT PRIMARY KEY,     -- [主键] 元器件的全局唯一标识符，UUID格式
            PartNumber            TEXT NOT NULL,        -- [字段] 制造商零件号
            CategoryUUID          TEXT NOT NULL,        -- [外键-逻辑] 引用全局库 `global_data.db` 中 `Categories` 表的UUID
            ManufacturerUUID      TEXT NOT NULL,        -- [外键-逻辑] 引用全局库 `global_data.db` 中 `Manufacturers` 表的UUID
            Description           TEXT,                 -- [字段] 元器件的详细功能、规格或备注信息
            LifecycleStatus       TEXT DEFAULT 'Production' CHECK(LifecycleStatus IN ('Production', 'Obsolete', 'NRND')), -- [字段] 元器件的生命周期状态
            CoreAttributes_JSON TEXT,               -- [字段] 存储该元器核心属性的JSON字符串，其结构由Category决定
            CustomAttributes_JSON TEXT,               -- [字段] 存储该元器件特有属性的JSON字符串
            Tags_JSON							TEXT, 							-- [字段] 存储该元器件的标签
            CreationTimestamp     DATETIME DEFAULT CURRENT_TIMESTAMP, -- [审计] 该元器件记录的创建时间
            CreatedBy             TEXT,                 -- [审计] 创建该元器件记录的用户名
            LastModifiedTimestamp DATETIME DEFAULT CURRENT_TIMESTAMP, -- [审计] 该元器件记录的最后一次修改时间
            LastModifiedBy        TEXT,                 -- [审计] 最后一次修改该元器件记录的用户名
            Revision              INTEGER NOT NULL DEFAULT 1, -- [审计] 该元器件记录自身的修订版本号，每次修改时递增
            IsDeleted             INTEGER NOT NULL DEFAULT 0, -- [字段] 软删除标志，0表示未删除，1表示已删除

            UNIQUE (ManufacturerUUID, PartNumber) -- [约束] 在同一个库文件中，同一个制造商下的零件号必须唯一
        );
    )");
}

void PartsCtrl::loadTable()
{
    QString sql = QString("select * from %1 where IsDeleted='0'").arg(tableName());
    QSqlQuery query(getCurrentDatabase());
    if(query.exec(sql))
    {
        while (query.next())
        {
            QSqlRecord record = query.record();

            PartsModelPtr obj;
            obj.reset(new PartsModel);
            obj->dbToModel(record);

            mAllObj.insert(obj->id, obj);
        }
    }
} 