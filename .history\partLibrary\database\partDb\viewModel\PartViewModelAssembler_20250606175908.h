#ifndef PARTVIEWMODELASSEMBLER_H
#define PARTVIEWMODELASSEMBLER_H

#include "PartViewModel.h"
#include "../sqlModel/partmodel.h"
#include "../../globalDb/sqlCtrl/attributedefinitionsctrl.h"

class PartViewModelAssembler
{
public:
    /**
     * @brief 装配元器件视图模型
     * @param partModel             [输入] 从ptlib数据库中读取的原始PartModel
     * @param attrDefinitionsCtrl   [输入] 全局属性定义表的控制器实例
     * @return                      一个包含了所有UI所需信息的完整ViewModel
     */
    static PartViewModel createViewModel(const PartsModelPtr& partModel,
                                         const AttributeDefinitionsCtrl& attrDefinitionsCtrl);
};

#endif // PARTVIEWMODELASSEMBLER_H 