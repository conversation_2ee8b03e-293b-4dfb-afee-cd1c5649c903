#ifndef TEST_PARTDB_H
#define TEST_PARTDB_H

/**
 * @brief Runs all tests for the Part Database controllers.
 *
 * This function will create a temporary 'part_test.db' in the application's
 * directory, connect to it, and run a series of tests for:
 * - LibraryInfoCtrl
 * - DatasheetsCtrl
 * - PartsCtrl
 * - PartDatasheetLinksCtrl
 *
 * All test output is logged via qDebug/qWarning.
 */
void run_partDb_tests();

#endif // TEST_PARTDB_H 