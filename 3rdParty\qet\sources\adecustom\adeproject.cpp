﻿#include "adeproject.h"

#include "version.h"
#include "layoutDiagram/createadeelement.h"

#include "../qet.h"
#include "../qetgraphicsitem/customconductor.h"

#include "ade.h"

ADEProject::ADEProject() :
    mIfElmtCollectionUpdated(false)
{

}

ADEProject::~ADEProject()
{

}

QString ADEProject::ADEVersion()
{
    return "ADEVersion";
}

void ADEProject::checkAndUpdateProjectDocument(QDomDocument &xml_project)
{
    QDomElement root_elmt = xml_project.documentElement();

    if (root_elmt.tagName() != QLatin1String("project"))
        return;

    // 读取ADE版本信息
    QString adeVersion;
    {
        QDomElement dom_elmt = root_elmt.firstChildElement("properties");
        if (!dom_elmt.isNull())
        {
            for (QDomElement property : QET::findInDomElement(dom_elmt, "property"))
            {
                if (!property.hasAttribute("name"))
                    continue;
                if (property.attribute("name") == ADEVersionTagName())
                    adeVersion = property.text();
            }
        }
    }

    // NOTE: V1.6.2.1版本开始导线添加lineType属性，对于之前版本工程的导线
    //       都作为CustomConductor处理
    // 检查更新导线定义
    QString updateConductorVersion = "V1.6.2.1";
    if (adeVersion.isEmpty() ||
            Version::isVersionLessThan(adeVersion, updateConductorVersion))
    {
        QDomNodeList diagram_nodes = xml_project.elementsByTagName(QStringLiteral("diagram"));
        for (int i = 0 ; i < diagram_nodes.length() ; ++ i)
        {
            if (diagram_nodes.at(i).isElement())
            {
                QDomElement diagram_xml_element = diagram_nodes.at(i).toElement();
                for (auto f : QET::findInDomElement(diagram_xml_element,
                                                    QStringLiteral("conductors"),
                                                    QStringLiteral("conductor")))
                {
                    f.setAttribute("lineType", QString::number(CustomConductor::LineType));
                }
            }
        }
    }

    // 检查并更新接口元素形状
    {
        QDomNodeList collection_roots = xml_project.elementsByTagName(QStringLiteral("collection"));
        QDomElement collection_root;

        if (!collection_roots.isEmpty())
            collection_root = collection_roots.at(0).toElement();

        if (!collection_root.isNull())
        {
            collection_root = CreateAdeElement::getInstance()
                    ->updateCollectionElementDescription(collection_root,
                                                         mIfElmtCollectionUpdated);
        }
    }
}
