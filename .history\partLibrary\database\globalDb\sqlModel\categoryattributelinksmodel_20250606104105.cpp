#include "categoryattributelinksmodel.h"

// 字段名称定义
const QString CategoryAttributeLinksModel::FIELD_ID = "LinkUUID";
const QString CategoryAttributeLinksModel::FIELD_CATEGORY_UUID = "CategoryUUID";
const QString CategoryAttributeLinksModel::FIELD_ATTRIBUTE_UUID = "AttributeUUID";
const QString CategoryAttributeLinksModel::FIELD_DEFAULT_VALUE = "DefaultValue";
const QString CategoryAttributeLinksModel::FIELD_IS_DELETED = "IsDeleted";

QVariantMap CategoryAttributeLinksModel::modelToDbMap() const
{
    QVariantMap map;
    map.insert(FIELD_ID, id);
    map.insert(FIELD_CATEGORY_UUID, categoryUUID);
    map.insert(FIELD_ATTRIBUTE_UUID, attributeUUID);
    map.insert(FIELD_DEFAULT_VALUE, defaultValue);
    return map;
}

void CategoryAttributeLinksModel::dbToModel(const QSqlRecord &record)
{
    QMap<QString, QVariant> infos = getRecordValues(record);

    if (infos.contains(FIELD_ID)) this->id = infos[FIELD_ID].toString();
    if (infos.contains(FIELD_CATEGORY_UUID)) this->categoryUUID = infos[FIELD_CATEGORY_UUID].toString();
    if (infos.contains(FIELD_ATTRIBUTE_UUID)) this->attributeUUID = infos[FIELD_ATTRIBUTE_UUID].toString();
    if (infos.contains(FIELD_DEFAULT_VALUE)) this->defaultValue = infos[FIELD_DEFAULT_VALUE].toString();
} 