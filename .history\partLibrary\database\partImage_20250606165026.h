#pragma once

#include "sqlModelBase.h"
#include <QByteArray>
#include <QUuid>

namespace part_library
{
namespace database
{

class PartImage : public SqlModelBase
{
public:
    // --- SqlModelBase interface ---
    QString tableName() const override;
    QString primaryKeyField() const override;
    QVariantMap modelToDbMap() const override;
    void dbToModel(const QSqlRecord& record) override;

public:
    // --- Data members ---
    QString imageUUID{QUuid::createUuid().toString(QUuid::WithoutBraces)};
    QString partUUID;
    QByteArray pixmap;
    QString imageType;
    int displayOrder = 0;
    QString description;
    bool isDeleted = false;
};

} // namespace database
} // namespace part_library 