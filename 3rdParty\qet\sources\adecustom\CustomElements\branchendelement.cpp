﻿#include "branchendelement.h"

#include "../diagram.h"
#include "../qetxml.h"

#include "../qetgraphicsitem/terminal.h"

BranchEndElement::BranchEndElement(const ElementsLocation &location,
                                   int *state) :
      Element(location, nullptr, state, Element::Simple)
{
    for (auto t : terminals())
    {
        t->setDrawTermianl(false);
        t->setDrawHover(true);
    }

    // 端点元素的层级最高
    setZValue(99999);
}


BranchEndElement::~BranchEndElement()
{

}

QRectF BranchEndElement::boundingRect() const
{
    return ( QRectF( QPointF(-mHalfLength, -mHalfLength) ,
                     QSizeF(mHalfLength * 2, mHalfLength * 2) ) );
}

bool BranchEndElement::fromXml(QDomElement& e, QHash<int, Terminal*>& table_id_adr)
{
    bool res = Element::fromXml(e, table_id_adr);

    if (res)
    {

    }

    return res;
}

QDomElement BranchEndElement::toXml(QDomDocument& document,
                                    QHash<Terminal*, int>& table_adr_id) const
{
    // 先在Element基类中创建xml元素, 再补充自定义信息
    QDomElement elmtXml = Element::toXml(document, table_adr_id);

    return elmtXml;
}
