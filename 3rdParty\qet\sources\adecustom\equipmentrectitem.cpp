﻿#include "equipmentrectitem.h"

#include <QPen>
#include <QDebug>
#include <QGraphicsScene>
#include <QGraphicsSceneMouseEvent>
#include <iostream>

#include "../diagram.h"

/*************************Start————AdeHandePointItem*************************/
RectPointHandlerItem::RectPointHandlerItem(QGraphicsItem *parent) :
    QGraphicsEllipseItem(QRect(-6,-6,12,12),parent)
{
    this->setFlags(QGraphicsItem::ItemIsSelectable  |
                   QGraphicsItem::ItemIsMovable     |
                   QGraphicsItem::ItemIsFocusable);
    setFlag(QGraphicsItem::ItemIgnoresTransformations);

    setAcceptHoverEvents(true);
    this->setPen(QPen(Qt::darkGreen));
    this->setBrush(QBrush(Qt::darkGreen));
}

RectPointHandlerItem::~RectPointHandlerItem()
{
    //qDebug() << Q_FUNC_INFO;
}

void RectPointHandlerItem::mousePressEvent(QGraphicsSceneMouseEvent *event)
{
    QGraphicsEllipseItem::mousePressEvent(event);
}

void RectPointHandlerItem::mouseMoveEvent(QGraphicsSceneMouseEvent *event)
{
    QPointF pos_ = event->pos();
    if (scene() && static_cast<Diagram *>(scene()) && parentItem())
    {
        QPointF scenePos = static_cast<Diagram *>(scene())->snapToGrid(event->scenePos());
        pos_ = parentItem()->mapFromScene(scenePos);
    }
    setPos(pos_);
    emit popPointPos(pos_);
}

void RectPointHandlerItem::mouseReleaseEvent(QGraphicsSceneMouseEvent *event)
{
    QGraphicsEllipseItem::mouseReleaseEvent(event);

    emit popPointPos(pos());
    emit stopMove();
}

void RectPointHandlerItem::hoverEnterEvent(QGraphicsSceneHoverEvent *event)
{
    QGraphicsEllipseItem::hoverEnterEvent(event);
    this->setPen(QPen(Qt::green));
    this->setBrush(QBrush(Qt::green));
    emit popCanPress(true);
}

void RectPointHandlerItem::hoverLeaveEvent(QGraphicsSceneHoverEvent *event)
{
    QGraphicsEllipseItem::hoverLeaveEvent(event);
    this->setPen(QPen(Qt::darkGreen));
    this->setBrush(QBrush(Qt::darkGreen));
    emit popCanPress(false);
}

/*************************End————AdeHandePointItem*************************/

/*************************Start————AdeHandeItem*************************/
EquipmentRectItem::EquipmentRectItem(const QRectF &rect, const QColor &color, QSize minSize) :
    QGraphicsRectItem(rect),
    m_minSize(minSize)
{
    //qDebug() << Q_FUNC_INFO << "m_minSize" << m_minSize;
    // BUG: 布置图无模板情况下缩放设备大小时会出现异常
    m_originSize = rect.size();
    m_lastRect = rect;
    m_offset = 0;
    this->setFlags(QGraphicsItem::ItemDoesntPropagateOpacityToChildren);
    setFlag(QGraphicsItem::ItemIsSelectable, false);
    setFlag(QGraphicsItem::ItemIsMovable, false);

    if(!minSize.isValid())
    {
        m_minSize = QSize(20, 20);
    }

    QPen pen = QPen(color);
    pen.setStyle(Qt::DashLine);
    setPen(pen);

    m_point1 = new RectPointHandlerItem(this);
    connect(m_point1,&RectPointHandlerItem::popPointPos,this,&EquipmentRectItem::firstPointMove);
    connect(m_point1,&RectPointHandlerItem::popCanPress,this,&EquipmentRectItem::setCanPress);
    connect(m_point1,&RectPointHandlerItem::stopMove,this,&EquipmentRectItem::stopMove);


    m_point2 = new RectPointHandlerItem(this);
    connect(m_point2,&RectPointHandlerItem::popPointPos,this,&EquipmentRectItem::secondPointMove);
    connect(m_point2,&RectPointHandlerItem::popCanPress,this,&EquipmentRectItem::setCanPress);
    connect(m_point2,&RectPointHandlerItem::stopMove,this,&EquipmentRectItem::stopMove);


    m_point3 = new RectPointHandlerItem(this);
    connect(m_point3,&RectPointHandlerItem::popPointPos,this,&EquipmentRectItem::thridPointMove);
    connect(m_point3,&RectPointHandlerItem::popCanPress,this,&EquipmentRectItem::setCanPress);
    connect(m_point3,&RectPointHandlerItem::stopMove,this,&EquipmentRectItem::stopMove);

    m_point4 = new RectPointHandlerItem(this);
    connect(m_point4,&RectPointHandlerItem::popPointPos,this,&EquipmentRectItem::fourthPointMove);
    connect(m_point4,&RectPointHandlerItem::popCanPress,this,&EquipmentRectItem::setCanPress);
    connect(m_point4,&RectPointHandlerItem::stopMove,this,&EquipmentRectItem::stopMove);


    m_leftPoint = new RectPointHandlerItem(this);
    connect(m_leftPoint,&RectPointHandlerItem::popPointPos,this,&EquipmentRectItem::leftPointMove);
    connect(m_leftPoint,&RectPointHandlerItem::popCanPress,this,&EquipmentRectItem::setCanPress);
    connect(m_leftPoint,&RectPointHandlerItem::stopMove,this,&EquipmentRectItem::stopMove);

    m_topPoint = new RectPointHandlerItem(this);
    connect(m_topPoint,&RectPointHandlerItem::popPointPos,this,&EquipmentRectItem::topPointMove);
    connect(m_topPoint,&RectPointHandlerItem::popCanPress,this,&EquipmentRectItem::setCanPress);
    connect(m_topPoint,&RectPointHandlerItem::stopMove,this,&EquipmentRectItem::stopMove);

    m_rightPoint = new RectPointHandlerItem(this);
    connect(m_rightPoint,&RectPointHandlerItem::popPointPos,this,&EquipmentRectItem::rightPointMove);
    connect(m_rightPoint,&RectPointHandlerItem::popCanPress,this,&EquipmentRectItem::setCanPress);
    connect(m_rightPoint,&RectPointHandlerItem::stopMove,this,&EquipmentRectItem::stopMove);

    m_bottomPoint = new RectPointHandlerItem(this);
    connect(m_bottomPoint,&RectPointHandlerItem::popPointPos,this,&EquipmentRectItem::bottomPointMove);
    connect(m_bottomPoint,&RectPointHandlerItem::popCanPress,this,&EquipmentRectItem::setCanPress);
    connect(m_bottomPoint,&RectPointHandlerItem::stopMove,this,&EquipmentRectItem::stopMove);

    updatePoints(rect);
//    setTransformOriginPoint(QPointF(rect.x(),rect.y()));
}


bool EquipmentRectItem::getMousePressState() const
{
    return m_isPress;
}

void EquipmentRectItem::enableStretchHandler(bool enable)
{
    if(m_point1){
        m_point1->setVisible(enable);
    }
    if(m_point2){
        m_point2->setVisible(enable);
    }
    if(m_point3){
        m_point3->setVisible(enable);
    }
    if(m_point4){
        m_point4->setVisible(enable);
    }
    if(m_leftPoint){
        m_leftPoint->setVisible(enable);
    }
    if(m_topPoint){
        m_topPoint->setVisible(enable);
    }
    if(m_rightPoint){
        m_rightPoint->setVisible(enable);
    }
    if(m_bottomPoint){
        m_bottomPoint->setVisible(enable);
    }

}

void EquipmentRectItem::setColor(const QColor &color)
{
    QPen pen = this->pen();
    pen.setColor(color);
    setPen(pen);
    update();
}

QVariant EquipmentRectItem::itemChange(QGraphicsItem::GraphicsItemChange change, const QVariant &value)
{


    return QGraphicsRectItem::itemChange(change,value);
}



QPainterPath EquipmentRectItem::shape() const
{
    QPainterPath path;
    return path;
}

void EquipmentRectItem::firstPointMove(QPointF pos)
{

    QPointF posT = mapToParent(pos);
    posT = mapFromParent(accordSceneGrid(posT));

    QPointF startPos = posT;

    QPointF endPos = this->rect().bottomRight();//m_point3->pos();
    QRectF rect = QRectF(startPos,endPos);
    QSize size = rect.size().toSize();

    if(size.width() < m_minSize.width()){
        startPos = QPointF(endPos.x()-m_minSize.width(),startPos.y());
    }
    if(size.height() < m_minSize.height()){
        startPos = QPointF(startPos.x(),endPos.y()-m_minSize.height());
    }


    rect = QRectF(startPos,endPos);
    updatePoints(rect);
}

void EquipmentRectItem::secondPointMove(QPointF pos)
{

    QPointF posT = mapToParent(pos);
    posT = mapFromParent(accordSceneGrid(posT));

    QPointF startPos = QPointF(/*m_point1->pos().x()*/this->rect().topLeft().x(),posT.y());
    QPointF endPos = QPointF(posT.x(),/*m_point3->pos().y()*/this->rect().bottomRight().y());
    QRectF rect = QRectF(startPos,endPos);
    QSize size = rect.size().toSize();

    if(size.width() < m_minSize.width()){

        startPos = QPointF(/*m_point4->pos().x()*/this->rect().bottomLeft().x(),startPos.y());
        endPos = QPointF(/*m_point4->pos().x()*/ this->rect().bottomLeft().x()+m_minSize.width(),endPos.y());
    }
    if(size.height() < m_minSize.height()){
        startPos = QPointF(startPos.x(),/*m_point4->pos().y()*/this->rect().bottomLeft().y()-m_minSize.height());
        endPos = QPointF(endPos.x(),/*m_point4->pos().y()*/this->rect().bottomLeft().y());
    }
    rect = QRectF(startPos,endPos);
//    if(size.width() < m_minSize.width() || size.height() < m_minSize.height()){
//        startPos = QPointF(m_point4->scenePos().x(),m_point4->scenePos().y() - m_minSize.height());
//        endPos = QPointF(m_point4->scenePos().x()+m_minSize.width(),m_point4->scenePos().y());
//        rect = QRectF(startPos,endPos);
//    }

    updatePoints(rect);
}

void EquipmentRectItem::thridPointMove(QPointF pos)
{

    QPointF posT = mapToParent(pos);
    posT = mapFromParent(accordSceneGrid(posT));

    QPointF startPos = /*m_point1->pos()*/this->rect().topLeft();
    QPointF endPos = posT;
    QRectF rect = QRectF(startPos,endPos);
    QSize size = rect.size().toSize();

    if(size.width() < m_minSize.width()){
        endPos = QPointF(startPos.x()+m_minSize.width(),endPos.y());
    }
    if(size.height() < m_minSize.height()){
        endPos = QPointF(endPos.x(),startPos.y()+m_minSize.height());
    }
    rect = QRectF(startPos,endPos);

    updatePoints(rect);
}

void EquipmentRectItem::fourthPointMove(QPointF pos)
{

    QPointF posT = mapToParent(pos);
    posT = mapFromParent(accordSceneGrid(posT));

    QPointF startPos = QPointF(posT.x(),/*m_point1->pos().y()*/this->rect().topLeft().y());
    QPointF endPos = QPointF(/*m_point3->pos().x()*/this->rect().bottomRight().x(),posT.y());
    QRectF rect = QRectF(startPos,endPos);

    QSize size = rect.size().toSize();

    if(size.width() < m_minSize.width()){

        startPos = QPointF(/*m_point2->pos().x()*/this->rect().topRight().x()-m_minSize.width(),startPos.y());
        endPos = QPointF(/*m_point2->pos().x()*/this->rect().topRight().x(),endPos.y());
    }
    if(size.height() < m_minSize.height()){
        startPos = QPointF(startPos.x(),/*m_point2->pos().y()*/this->rect().topRight().y());
        endPos = QPointF(endPos.x(),/*m_point2->pos().y()*/this->rect().topRight().y()+m_minSize.height());
    }
    rect = QRectF(startPos,endPos);
    updatePoints(rect);
}

void EquipmentRectItem::leftPointMove(QPointF pos)
{
    QPointF posT = mapToParent(pos);
    posT = mapFromParent(accordSceneGrid(posT));
    QPointF startPos = QPointF(posT.x(),this->rect().topLeft().y());
    QPointF endPos = rect().bottomRight();

    QRectF rect = QRectF(startPos,endPos);
    QSize size = rect.size().toSize();
    if(size.width() < m_minSize.width()){
        startPos = QPointF(this->rect().topRight().x()-m_minSize.width(),startPos.y());
    }
    rect = QRectF(startPos,endPos);
    updatePoints(rect);

}

void EquipmentRectItem::topPointMove(QPointF pos)
{
    QPointF posT = mapToParent(pos);
    posT = mapFromParent(accordSceneGrid(posT));
    QPointF startPos = QPointF(this->rect().topLeft().x(),posT.y());
    QPointF endPos = rect().bottomRight();
    QRectF rect = QRectF(startPos,endPos);
    QSize size = rect.size().toSize();
    if(size.height() < m_minSize.height()){
        startPos = QPointF(startPos.x(),this->rect().bottomRight().y()-m_minSize.height());
    }
    rect = QRectF(startPos,endPos);
    updatePoints(rect);
}

void EquipmentRectItem::rightPointMove(QPointF pos)
{
    QPointF posT = mapToParent(pos);
    posT = mapFromParent(accordSceneGrid(posT));
    QPointF startPos = this->rect().topLeft();
    QPointF endPos = QPointF(posT.x(),this->rect().bottomRight().y());
    QRectF rect = QRectF(startPos,endPos);
    QSize size = rect.size().toSize();
    if(size.width() < m_minSize.width()){
        endPos = QPointF(this->rect().topLeft().x()+m_minSize.width(),endPos.y());
    }
    rect = QRectF(startPos,endPos);
    updatePoints(rect);
}

void EquipmentRectItem::bottomPointMove(QPointF pos)
{
    QPointF posT = mapToParent(pos);
    posT = mapFromParent(accordSceneGrid(posT));
    QPointF startPos = this->rect().topLeft();
    QPointF endPos = QPointF(this->rect().bottomRight().x(),posT.y());
    QRectF rect = QRectF(startPos,endPos);
    QSize size = rect.size().toSize();
    if(size.height() < m_minSize.height()){
        endPos = QPointF(endPos.x(),this->rect().topLeft().y()+m_minSize.height());
    }
    rect = QRectF(startPos,endPos);
    updatePoints(rect);
}

void EquipmentRectItem::stopMove()
{
    QSizeF size = rect().size();

    QSizeF diffSize = (size - m_originSize);
//    if(fabs(diffSize.width()) < 5 && fabs(diffSize.height()) < 5){
//        updatePoints(m_lastRect);// 改变不超过5pix 不改变大小
//        return ;
//    }
    if((diffSize.width() == 0)  && (diffSize.height() == 0))
    {
        updatePoints(m_lastRect);// 改变不超过5pix 不改变大小
        return ;
    }
    m_lastRect = rect();
    QSize sizeT = size.toSize();
    if(sizeT.width() % 2)
    {
        sizeT.setWidth(sizeT.width() + 1);
    }
    if(sizeT.height() % 2)
    {
        sizeT.setHeight(sizeT.height() + 1);
    }
    QPointF pos = mapToScene(rect().center());

    emit popFinishMoveRectByScene(pos,sizeT);
}

void EquipmentRectItem::setCanPress(bool isPress)
{
    m_isPress = isPress;
    emit popIsPress(isPress);
}

void EquipmentRectItem::updatePoints(const QRectF &rect)
{
    QRectF rectT = rect;

    setRect(rectT);
//    m_offset = 3;
    m_point1->setPos(rectT.x()-m_offset,rectT.y()-m_offset);
    m_point2->setPos(rectT.x()+rectT.width()+m_offset,rectT.y()-m_offset);
    m_point3->setPos(rectT.x()+rectT.width()+m_offset,rectT.y()+rectT.height()+m_offset);
    m_point4->setPos(rectT.x()-m_offset,rectT.y()+rectT.height()+m_offset);
    m_leftPoint->setPos(rectT.x()-m_offset,rectT.center().y());
    m_topPoint->setPos(rectT.center().x(),rectT.y()-m_offset);
    m_rightPoint->setPos(rectT.topRight().x() + m_offset,rectT.center().y());
    m_bottomPoint->setPos(rectT.center().x(),rectT.bottomRight().y()+m_offset);
}

QPointF EquipmentRectItem::accordSceneGrid(const QPointF& pos)
{
    QPointF posT = pos;
    Diagram* diagram = dynamic_cast<Diagram*>(scene());
    if(!diagram) return posT;

    QSize posRange = diagram->rangeLimit();
    if (posRange == QSize(-1, -1))
        return posT;

    int sWidth = posRange.width();
    int sHeight = posRange.height();

    if(posT.x() < 0){
        posT.setX(0);
    }
    if(posT.x() > sWidth){
        posT.setX(sWidth);
    }
    if(posT.y() < 0){
        posT.setY(0);
    }
    if(posT.y() > sHeight){
        posT.setY(sHeight);
    }
//    qDebug() << Q_FUNC_INFO << posT << size;
    return posT;
}
/*************************End————AdeHandeItem*************************/

/*************************Start————NoTransformFineLineItem*************************/
void NoTransformFineLineItem::paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget)
{
    // 计算线条路径
    QPainterPath path;
    path.moveTo(line().p1());
    path.lineTo(line().p2());

    // 设置线条样式
    QPen pen = painter->pen();
    pen.setWidthF(1.0 / painter->transform().m11()); // 根据当前缩放比例计算线宽
    painter->setPen(pen);

    // 绘制线条路径
    painter->drawPath(path);
}
/*************************End————NoTransformFineLineItem*************************/


/*************************Start————AdeHandleDragRect*************************/
AdeHandleDragRect::AdeHandleDragRect(QSize minSize, QGraphicsItem *parent) :
    QGraphicsPathItem(parent),
    m_minSize(minSize)
{

}

QSize AdeHandleDragRect::getRectSize() const
{
    QSize sizeT = this->boundingRect().size().toSize();

    // FIXME: 尺寸比拖拽大小大一个像素
    sizeT = QSize(sizeT.width() - 1, sizeT.height() - 1);

    int width  = fabs(sizeT.width());
    int height = fabs(sizeT.height());

    if(width % 2)
    {
        width++;
    }

    if(height % 2)
    {
        height++;
    }

    return QSize(width,height);
}

QPointF AdeHandleDragRect::getCenterPos() const
{
    return mapToScene(this->boundingRect().center());
}

bool AdeHandleDragRect::isValid() const
{
    bool ret = true;

    QSize sizeT = this->boundingRect().size().toSize();//size.toSize();
    if(fabs( sizeT.width()) < m_minSize.width() || fabs(sizeT.height()) < m_minSize.height()){
        ret = false;
    }
    return ret;
}

void AdeHandleDragRect::updateRectSize(const qreal &width, const qreal &height)
{
    qreal xT = width;
    qreal yT = height;

    QPointF topLeft;
    QPointF bottomRight;
    qreal widthT = m_minSize.width();
    qreal heightT = m_minSize.height();

    if(xT > 0 && yT > 0){
        topLeft = QPointF(0,0);
        bottomRight = QPointF(widthT,heightT);
    }
    else if(xT < 0 && yT > 0){
        topLeft = QPointF(-widthT,0);
        bottomRight = QPointF(0,heightT);
    }
    else if(xT < 0 && yT < 0){
       topLeft = QPointF(-widthT,-heightT);
       bottomRight = QPointF(0,0);
    }
    else if(xT > 0 && yT < 0){
        topLeft = QPointF(0,-heightT);
        bottomRight = QPointF(widthT,0);
    }
    if(!mMinReferenceRect)
    {
        mMinReferenceRect = new QGraphicsRectItem(this);
        mMinReferenceRect->setPos(0,0);
        QPen pen = QPen(Qt::gray);
        pen.setStyle(Qt::DashLine);
        mMinReferenceRect->setPen(pen);
    }
    mMinReferenceRect->setRect(QRectF(topLeft,bottomRight));


    if(qFabs(width) < m_minSize.width() || qFabs(height) < m_minSize.height())
    {
        QPen pen = QPen(Qt::red);
        setPen(pen);
    }
    else
    {
        setPen(QPen(Qt::black));
    }

    QPainterPath path;
    path.addRect(0,0,xT,yT);
    setPath(path);
}

QPointF AdeHandleDragRect::minReferenceRectDiagonalPoint() const
{
    if (!mMinReferenceRect)
        return QPointF();

    QRectF rect = mMinReferenceRect->rect();
    QPointF topLeft = rect.topLeft();
    qreal width = m_minSize.width();
    qreal height = m_minSize.height();

    if (topLeft.x() == 0 && topLeft.y() == 0)
    {
        return mapToScene(rect.bottomRight());
    }
    else if (topLeft.x() == 0 && topLeft.y() == -height)
    {
        return mapToScene(rect.topRight());
    }
    else if (topLeft.x() == -width && topLeft.y() == 0)
    {
        return mapToScene(rect.bottomLeft());
    }
    else if (topLeft.x() == -width && topLeft.y() == -height)
    {
        return mapToScene(rect.topLeft());
    }

    return topLeft;
}

/*************************End————AdeHandleDragRect*************************/

