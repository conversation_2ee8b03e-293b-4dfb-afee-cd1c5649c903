﻿#ifndef ADELOGICCABLE_H
#define ADELOGICCABLE_H

#include <QObject>
#include <QMap>
#include <QDomElement>
#include <QDomDocument>
#include <QPointF>

#include "sql/sqlModel/cable.h"

class Diagram;
class Element;
class CustomConductor;
class ADEConnectRelation;
class ADECableTextItem;
class InterfaceElement;
class CableSpliceElement;

/**
* @file
* @brief      ADE电缆对象映射到布置图上的逻辑对象
* <AUTHOR>
* @version    0.1
* @date       2023/07/20
* @todo
* @note
    <cable uuid="">
        // 电缆ConnectRelations中所有的cableIfElement的uuid
        // 记录电缆网内部参与连接的接口uuid，可以是电缆自身的接口，也可以是其它设备上的接口
        <interfaceElements>
            <interfaceElement uuid="">
        </interfaceElements>
        // 记录电缆网中的分叉点
        <spliceElements>
            <spliceElement uuid="">
        <spliceElements/>
        // 记录电缆网接口的对插连接关系
        <connectRelations>
            <connectRelation cableIfUuid="" otherIfUuid="">
        </connectRelations>
        // 电缆名称文本
        <cableText x="" y="" roatation="" >
            <text>代号;SELF2;1;;名称;SELF;1;;</text>
        <cableText>
    </cable>
*/
class ADELogicCable : public QObject
{
    Q_OBJECT
public:
    /**
     * @brief The ForkInfo class 在布置图上新建电缆分支时需要的信息
     */
    struct ForkInfo
    {
        QPointF forkPos;               ///< 新增接口元素的坐标

        /* 下面几组参数一次只能有一种有效 */

        // PropGroup_1:连接到分叉点元素时需要的参数
        CableSpliceElement *attachSpliceElmt = nullptr;  ///< 分支位置的交叉点元素

        // PropGroup_2:连接到导线时需要的参数
        CustomConductor *attachConductor = nullptr;///< 分支连接的导线

        // PropGroup_3:连接到接口（接口连接多条导线）上时需要的参数
        Element *linkIfElmt = nullptr;
    };

public:
    explicit ADELogicCable(Diagram *diagram,
                           const QString &uuid = QString(),
                           const QString &srcText = QString(),
                           QObject *parent = nullptr);
    ~ADELogicCable();

    static cable::ConnectRelation adeCrMapToCableCr(ADEConnectRelation *adeCr);

    /**
     * @brief getSpliceElmtPos 获取与一个接口连接的交点元素的位置
     * @param interfaceElmt    接口元素
     * @return                 交点位置
     */
    static QPointF getSpliceElmtPos(Element *interfaceElmt);

public:
    QString uuid() const {return mUuid;}
    Diagram *diagram() const {return mDiagram;}
    ADECableTextItem *textItem() {return mTextItem;}
    QList<CustomConductor *> allConductors() const;
    QMap<QString, InterfaceElement*> interfaceElements() const;
    QList<InterfaceElement *> cableSelfInterfaceElements() const;
    QList<CableSpliceElement *> spliceElements() const {return mSpliceElmts;}
    QList<ADEConnectRelation*> connectRelations() const {return mConnectRelations;}

public:
    bool fromXml(const QDomElement &e);
    QDomElement toXml(QDomDocument &doc, bool &ok);

    void addInterfaceElement(InterfaceElement *elmt);
    void addSpliceElement(CableSpliceElement *elmt);
    void addConnectRelation(ADEConnectRelation *cr);

    void removeElement(Element *elmt);
    void removeDcInterface(const QString &uuid);
    void removeConnectRelation(ADEConnectRelation *cr);

    ADEConnectRelation *getInterfaceConnectRelation(InterfaceElement *elmt) const;
    ADEConnectRelation *getConnectRelationByCableCr(const cable::ConnectRelation &cbCr);

    void adjustCableTextPositon() const;
    void setHighlighted(bool hl, bool ifEleHl);

    void showConductorHoverEffect(bool hovered);
    void showTextHoverEffect(bool hovered);

    void deleteConnectRelations();

    // 获取当前电缆所有接口具备的连接关系时使用此函数
    QList<ADEConnectRelation*> relatedConnectRelations() const;

    QString adeMark() const;
    QString adeName() const;

signals:

private:
    Diagram *mDiagram;
    QString mUuid;      ///< 电缆uuid
    QString mSrcName;

    QMap<QString, InterfaceElement*>    mInterfaceElmts; ///< 电缆连接的所有接口
    QList<CableSpliceElement *>         mSpliceElmts;
    QList<ADEConnectRelation*> mConnectRelations;

    ADECableTextItem *mTextItem = nullptr;

    QList<CustomConductor *> mLastHighlightConductors;
};

#endif // ADELOGICCABLE_H
