#include "partdatasheetlinksmodel.h"

// 字段名称定义
const QString PartDatasheetLinksModel::FIELD_ID = "LinkUUID";
const QString PartDatasheetLinksModel::FIELD_PART_UUID = "PartUUID";
const QString PartDatasheetLinksModel::FIELD_DATASHEET_UUID = "DatasheetUUID";
const QString PartDatasheetLinksModel::FIELD_IS_DELETED = "IsDeleted";

QVariantMap PartDatasheetLinksModel::modelToDbMap() const
{
    QVariantMap map;
    map.insert(FIELD_ID, id);
    map.insert(FIELD_PART_UUID, partUUID);
    map.insert(FIELD_DATASHEET_UUID, datasheetUUID);
    return map;
}

void PartDatasheetLinksModel::dbToModel(const QSqlRecord &record)
{
    QMap<QString, QVariant> infos = getRecordValues(record);

    if (infos.contains(FIELD_ID)) this->id = infos[FIELD_ID].toString();
    if (infos.contains(FIELD_PART_UUID)) this->partUUID = infos[FIELD_PART_UUID].toString();
    if (infos.contains(FIELD_DATASHEET_UUID)) this->datasheetUUID = infos[FIELD_DATASHEET_UUID].toString();
} 