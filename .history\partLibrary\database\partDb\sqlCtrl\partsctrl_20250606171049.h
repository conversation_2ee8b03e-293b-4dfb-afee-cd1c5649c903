#ifndef PARTSCTRL_H
#define PARTSCTRL_H

#include "../../sqlCtrlBase.h"
#include "../sqlModel/partmodel.h"

//============================================================================
/// file
/// brief      元器件信息主表控制器
/// author     wm
/// version    1.0
/// date       2025/06/05
/// todo
//============================================================================

class PartsCtrl : public SqlCtrlBase
{
public:
    PartsCtrl(const QSqlDatabase& db);
    ~PartsCtrl();

    // 针对数据模型的操作
    PartsModelList fetchAll() const;
    PartsModelPtr  fetch(const QString &id) const;
    bool modelExist(const QString &id) const;
    // end

    // 针对数据库表的操作
    bool insert(const PartsModelPtr &obj);
    bool modify(const PartsModelPtr &newValueObj);
    bool remove(const QString &id);
    // end

public:
    QString tableName()       const override {return "Parts";}
    QString primaryKeyField() const override {return "PartUUID";}
    bool    recordExist(const QString &id) const override;
    virtual bool softDelete() const override { return true; }

private:
    QString tableSql()        const override;
    void loadTable();

private:
    PartsModelList mAllObj;
};

#define Parts_Factory objFactory<QString, PartsCtrl>::getInstance()

#endif // PARTSCTRL_H 