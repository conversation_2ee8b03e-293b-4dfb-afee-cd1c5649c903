#ifndef PARTCTRL_H
#define PARTCTRL_H

#include "../../sqlCtrlBase.h"
#include "../sqlModel/partmodel.h"

//============================================================================
/// file
/// brief      元器件信息主表控制器
/// author     wm
/// version    1.0
/// date       2025/06/05
/// todo
//============================================================================

class PartCtrl : public SqlCtrlBase
{
public:
    PartCtrl(const QSqlDatabase& db);
    ~PartCtrl();

    // 针对数据模型的操作
    PartModelList fetchAll() const;
    PartModelPtr  fetch(const QString &id) const;
    bool modelExist(const QString &id) const;
    // end

    // 针对数据库表的操作
    bool insert(const PartModelPtr &obj);
    bool modify(const PartModelPtr &newValueObj);
    bool remove(const QString &id);
    // end

public:
    QString tableName()       const override {return "Parts";}
    QString primaryKeyField() const override {return "PartUUID";}
    bool    recordExist(const QString &id) const override;
    virtual bool softDelete() const override { return true; }

private:
    QString tableSql()        const override;
    void loadTable();

private:
    PartModelList mAllObj;
};

#endif // PARTCTRL_H
