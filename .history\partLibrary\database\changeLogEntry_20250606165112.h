#pragma once

#include "sqlModelBase.h"
#include <QDateTime>
#include <QUuid>

namespace part_library
{
namespace database
{

class ChangeLogEntry : public SqlModelBase
{
public:
    // --- SqlModelBase interface ---
    QString tableName() const override;
    QString primaryKeyField() const override;
    QVariantMap modelToDbMap() const override;
    void dbToModel(const QSqlRecord& record) override;

public:
    // --- Data members ---
    QString changeLogUUID{QUuid::createUuid().toString(QUuid::WithoutBraces)};
    QDateTime timestamp{QDateTime::currentDateTime()};
    QString userName;
    QString action; // 'CREATE', 'UPDATE', 'DELETE'
    QString recordUUID;
    QString fieldName;
    QString oldValue;
    QString newValue;
    QString description;
};

} // namespace database
} // namespace part_library 