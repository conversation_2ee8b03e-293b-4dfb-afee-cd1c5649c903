﻿#ifndef LOGBUSINESS_H
#define LOGBUSINESS_H

#include <string>
#include <mutex>
#include <QString>
#include "easylogging++.h"

/** 对外接口宏 **/

// #define LOG_INFO(...) LOG(INFO) << __VA_ARGS__
// #define LOG_DEBUG(...) LOG(DEBUG) << __VA_ARGS__
// #define LOG_ERROR(...) LOG(ERROR) << __VA_ARGS__
// #define LOG_WARNING(...) LOG(WARNING) << __VA_ARGS__

// #define CLOG_INFO(category, ...) LOG(INFO) << "[" << category << "]" << __VA_ARGS__
// #define CLOG_DEBUG(category, ...) LOG(DEBUG) << "[" << category << "]" << __VA_ARGS__
// #define CLOG_ERROR(category, ...) LOG(ERROR) << "[" << category << "]" << __VA_ARGS__
// #define CLOG_WARNING(category, ...) LOG(WARNING) << "[" << category << "]" << __VA_ARGS__

#ifdef LOG_CATEGORY
    // 如果 LOG_CATEGORY 已经定义，则使用带分类的日志宏
    #define LOG_INFO(...) LOG(INFO) << "[" << LOG_CATEGORY << "] " << __VA_ARGS__
    #define LOG_DEBUG(...) LOG(DEBUG) << "[" << LOG_CATEGORY << "] " << __VA_ARGS__
    #define LOG_ERROR(...) LOG(ERROR) << "[" << LOG_CATEGORY << "] " << __VA_ARGS__
    #define LOG_WARNING(...) LOG(WARNING) << "[" << LOG_CATEGORY << "] " << __VA_ARGS__
#else
    // 如果 LOG_CATEGORY 未定义，则使用不带分类的原始宏
    #define LOG_INFO(...) LOG(INFO) << __VA_ARGS__
    #define LOG_DEBUG(...) LOG(DEBUG) << __VA_ARGS__
    #define LOG_ERROR(...) LOG(ERROR) << __VA_ARGS__
    #define LOG_WARNING(...) LOG(WARNING) << __VA_ARGS__
#endif

/**
 * @brief The logBusiness class 日志记录,采用回滚方式(xxx.log.1 xxx.log.2 ...)，单个文件大小默认100M，文件个数默认5个
 */

class logBusiness
{
public:
    static logBusiness *getInstance();
    ~logBusiness();
private:
    logBusiness();
public:
    /**
     * @brief setFileMaxSize    设置单个文件最大长度
     * @param size              长度（字节）
     */
    void setFileMaxSize(const int &size);
    const QString &curFilePath() const;

private:
    void buildConf();
    void logRolloutHandler(const char* filename, std::size_t size);
private:
    const std::string mConfFilePath = "./project_log.conf";   ///< 工程日志配置路径
    const std::string mLogerName = "projectlog";              ///< 文件名称
    int mFileMaxSize = 200*1024*1024;                    ///< 单个文件大小byte
    int mFileCount = 5;                                 ///< 文件回滚大小
    static std::mutex mMutex;
    static logBusiness *mInstance;
    QString mCurFilePath;
};

/**
 * @brief 文件的基本操作，包括文件是否存在、文件大小、删除文件、重命名等操作
 */

class vxFile
{
public:
    /**
     * @brief 文件是否存在
     * @param file	文件
     * @return 是否存在
     * - true:存在
     * - false:不存在
     */
    static bool isExist(const std::string &file);

    /**
     * @brief 删除文件
     * @param file	文件
     * @return 是否成功
     * - true:成功
     * - false:失败
     */
    static bool del(const std::string &file);

    /**
     * @brief mkFolder  创建文件夹
     * @param path      文件夹
     * @return
     */
    static bool mkFolder(const std::string &path);
    /**
     * @brief 文件重命名
     * @param srcFile	源文名称
     * @param dstFile	目标名称
     * @return 是否成功
     * - true:成功
     * - false:失败
     */
    static bool rname(const std::string &srcFile, const std::string &dstFile);

    /**
     * @brief 获取文件大小
     * @param file	文件
     * @return 文件大小
     */
    static unsigned long size(const std::string &file);
};



#endif // LOGBUSINESS_H
