#ifndef CONNECTORWIRINGELEMENT_H
#define CONNECTORWIRINGELEMENT_H

#include "../qetgraphicsitem/element.h"

class ContactorElement;
class QetGraphicsHandlerItem;

/**
* @file
* @brief      ADE电缆芯线连接图中表示连接器形状的元素
* <AUTHOR>
* @version    1.0
* @date       2023/12/05
* @todo
*/
class ConnectorWiringElement : public Element
{
    Q_OBJECT

public:
    explicit ConnectorWiringElement(const ElementsLocation &,
                                    int * = nullptr,
                                    const QString &uuid = QString());
    ~ConnectorWiringElement() override;

    /// 接触件元素之间的距离
    static int ContactorInterval() {return 20;}
    /// 接触件元素与连接器元素端点的距离（应该与ContactorInterval呈倍数关系, 否则旋转180°后接触件元素无法对应）
    static int ContactorDistance() {return (ContactorInterval() * 2); }
    /// 通过接触件数量计算长度
    static int lengthByContactorCount(int count);

    /// 文本相对于原点的坐标
    static QPoint TextPos() {return QPoint(-90, -40);}

private:
    ConnectorWiringElement(const Element&);

public:
    enum { ADEType = 9 };
    int adeType() const override {return ADEType;}

public:
    bool        fromXml(QDomElement&, QHash<int,Terminal*>&) override;
    QDomElement toXml(QDomDocument&, QHash<Terminal*,int>&)  const override;

public:
    /// 所有的接触件元素, 包括接壳
    QList<ContactorElement*> contactors() const;
    /// 所有的接触件元素按照x坐标从小到大排列, 包括接壳
    QList<ContactorElement*> sequentialContactors() const;
    /// 所有的接触件元素按照场景x坐标从小到大排列，包括接壳
    QList<ContactorElement*> sceneSequentialContactors() const;

    QVector<QPair<ContactorElement *, QPoint>> sceneSequentialContactoSlots() const;

    /// 可以放置接触件元素的位置
    QList<QPoint> contactorSlots() const;

    /// 接触件在连接器上的位置
    int contactorSlotIndex(ContactorElement* cttElmt) const;

    /// 连接器元素上的接壳接触件元素
    ContactorElement *enclosureElement() const;

    /// 根据子接口uuid和点位名称获取接触件元素
    ContactorElement *contactorElement(const QString &ifUuid,
                                       const QString &point) const;

    /// 设置接触件的名称, QMap<ifUuid, points>
    void setContactorPointNames(const QMap<QString, QStringList> &ifPointNames);

    /// 获取接触件点位所属的接口uuid列表
    QStringList getContactorFatherIfUuids() const;

    /// 交换两个接触件元素的位置
    bool swapContactorToPos(ContactorElement *cttElmt, const QPointF &pos);

    /// 获取当前连接器元素上与指定接触件相连接的接触件元素
    ContactorElement *getConnectedContactorToContactor(ContactorElement *otherCtt) const;

    /// 当连接器元素上的接触件数量变化时调整长度
    void adjustLength();

    /// 设置长度
    void setLength(qreal newLength);

    /// 设置连接器元素的名称
    void setName(const QString &name);

    /// 初始化接触件元素的位置
    void initContactorPos();

    /// 初始化文本的位置
    void updateTextPos();

    /// 移动接触件, 与其它接触件交换位置
    QList<ContactorElement *> moveContactor(ContactorElement *cttElmt, qreal movment);
    QList<ContactorElement *> moveContactors(QList<ContactorElement *> cttElmts, qreal movment);

protected:
    QVariant itemChange(GraphicsItemChange, const QVariant&) override;
    bool sceneEventFilter(QGraphicsItem *watched, QEvent *event) override;
    void mousePressEvent(QGraphicsSceneMouseEvent* event) override;

private slots:
    void onRotationChanged();

private:
    void updateEnclosurePos();
    QPoint enclosurePos() const;

    // 抓手相关
    void addHandler();
    void removeHandler();
    void updateHandlerState();
    void handlerMousePressEvent   (QetGraphicsHandlerItem *qghi, QGraphicsSceneMouseEvent *event);
    void handlerMouseMoveEvent    (QetGraphicsHandlerItem *qghi, QGraphicsSceneMouseEvent *event);
    void handlerMouseReleaseEvent (QetGraphicsHandlerItem *qghi, QGraphicsSceneMouseEvent *event);


private:
    bool mInitialized; ///< 连接器上的接触件是否被初始化过
    QetGraphicsHandlerItem *mLeftHandler;   ///< 元素左侧抓手
    QetGraphicsHandlerItem *mRightHandler;  ///< 元素右侧抓手
    QGraphicsLineItem *mFakeLine; ///< 拉伸元素时显示的示意线
};

#endif // CONNECTORWIRINGELEMENT_H
