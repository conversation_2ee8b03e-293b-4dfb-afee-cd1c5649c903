#include <QCoreApplication>
#include <QDebug>
#include <QUuid>
#include <QSqlDatabase>
#include <QSqlError>
#include <stdexcept>
#include <QDateTime>

// Assume LOG_INFO and LOG_ERROR are defined somewhere globally
#ifndef LOG_INFO
#define LOG_INFO qDebug
#endif
#ifndef LOG_ERROR
#define LOG_ERROR qWarning
#endif

// Include Part DB Controllers
#include "../database/partDb/sqlCtrl/libraryinfoctrl.h"
#include "../database/partDb/sqlCtrl/datasheetsctrl.h"
#include "../database/partDb/sqlCtrl/partsctrl.h"
#include "../database/partDb/sqlCtrl/partdatasheetlinksctrl.h"

// Include Part DB Models
#include "../database/partDb/sqlModel/libraryinfomodel.h"
#include "../database/partDb/sqlModel/datasheetsmodel.h"
#include "../database/partDb/sqlModel/partsmodel.h"
#include "../database/partDb/sqlModel/partdatasheetlinksmodel.h"

// A simple mock for a database manager.
namespace SimpleDbManager {
    bool connect(const QString& path, QString& err, const QString& connectionName, QSqlDatabase& db) {
        db = QSqlDatabase::addDatabase("QSQLITE", connectionName);
        db.setDatabaseName(path);
        if (!db.open()) {
            err = db.lastError().text();
            return false;
        }
        return true;
    }
    void close(const QString& connectionName) {
        QSqlDatabase::removeDatabase(connectionName);
    }
}

void testLibraryInfoCtrl(QSqlDatabase& db) {
    qDebug() << "--- Testing LibraryInfoCtrl ---";
    LibraryInfoCtrl ctrl(db);
    if (!ctrl.isTableExist()) {
        QSqlError err;
        if (!ctrl.createTable(err)) {
            LOG_ERROR(err.text().toStdString());
            throw std::runtime_error(err.text().toStdString());
        }
        qDebug() << "LibraryInfo table created.";
    }

    // This table is supposed to have only one record.
    // Clean up before test.
    auto all_infos = ctrl.fetchAll();
    for(auto& info : all_infos) {
        ctrl.remove(info->id);
    }

    // Insert
    QString newInfoId;
    {
        auto infoObj = LibraryInfoModel::createPtr();
        infoObj->libraryName = "Test Part Library";
        infoObj->version = "1.0.0";
        infoObj->createdBy = "TestHarness";
        infoObj->creationTimestamp = QDateTime::currentDateTime();
        if (ctrl.insert(infoObj)) {
            newInfoId = infoObj->id;
            qDebug() << "Inserted library info with ID:" << newInfoId;
        } else {
            qDebug() << "Insert library info failed";
        }
    }

    // Read
    {
        auto objs = ctrl.fetchAll();
        qDebug() << "Reading library info, count:" << objs.count();
        if(!objs.isEmpty()){
             qDebug() << "  - Name:" << objs.first()->libraryName << " Version: " << objs.first()->version;
        }
    }

    // Modify
    if(!newInfoId.isEmpty())
    {
        auto infoObj = ctrl.fetch(newInfoId);
        if (infoObj) {
            infoObj->version = "1.0.1";
            infoObj->lastModifiedTimestamp = QDateTime::currentDateTime();
            if(ctrl.modify(infoObj)) {
                qDebug() << "Modified library info with ID:" << newInfoId;
            } else {
                 qDebug() << "Modify failed for library info with ID:" << newInfoId;
            }
        }
    }

    // Delete
    if(!newInfoId.isEmpty())
    {
        if (ctrl.remove(newInfoId)) {
            qDebug() << "Deleted library info with ID:" << newInfoId;
        } else {
            qDebug() << "Delete failed for library info with ID:" << newInfoId;
        }
    }
     qDebug() << "--- Finished Testing LibraryInfoCtrl ---";
}

void testDatasheetsCtrl(QSqlDatabase& db, QString& outDatasheetId) {
    qDebug() << "--- Testing DatasheetsCtrl ---";
    DatasheetsCtrl ctrl(db);
    if (!ctrl.isTableExist()) {
        QSqlError err;
        if (!ctrl.createTable(err)) {
            LOG_ERROR(err.text().toStdString());
            throw std::runtime_error(err.text().toStdString());
        }
        qDebug() << "Datasheets table created.";
    }

    // Insert
    {
        auto dsObj = DatasheetsModel::createPtr();
        dsObj->title = "Test Datasheet";
        dsObj->filePath = "/path/to/datasheet.pdf";
        dsObj->md5Hash = "d41d8cd98f00b204e9800998ecf8427e"; // Empty file hash
        dsObj->uploadTimestamp = QDateTime::currentDateTime();
        if(ctrl.insert(dsObj)) {
            outDatasheetId = dsObj->id;
            qDebug() << "Inserted new datasheet with ID:" << outDatasheetId;
        } else {
            qDebug() << "Insert datasheet failed.";
        }
    }

    // Read
    {
        auto objs = ctrl.fetchAll();
        qDebug() << "Reading all datasheets, count:" << objs.count();
        for (const auto& obj : objs) {
            qDebug() << "  - ID:" << obj->id << "Title:" << obj->title;
        }
    }

    // Modify
    if(!outDatasheetId.isEmpty())
    {
        auto dsObj = ctrl.fetch(outDatasheetId);
        if(dsObj) {
            dsObj->title = "Modified Datasheet Title";
            if(ctrl.modify(dsObj)) {
                qDebug() << "Modified datasheet with ID:" << outDatasheetId;
            } else {
                qDebug() << "Modify datasheet failed for ID:" << outDatasheetId;
            }
        }
    }

    // Delete
    if(!outDatasheetId.isEmpty()) {
        if(ctrl.remove(outDatasheetId)) {
             qDebug() << "Deleted datasheet with ID:" << outDatasheetId;
        } else {
            qDebug() << "Delete datasheet failed for ID:" << outDatasheetId;
        }
    }
    qDebug() << "--- Finished Testing DatasheetsCtrl ---";
}

void testPartsCtrl(QSqlDatabase& db, QString& outPartId) {
    qDebug() << "--- Testing PartsCtrl ---";
    PartsCtrl ctrl(db);
    if (!ctrl.isTableExist()) {
        QSqlError err;
        if (!ctrl.createTable(err)) {
            LOG_ERROR(err.text().toStdString());
            throw std::runtime_error(err.text().toStdString());
        }
        qDebug() << "Parts table created.";
    }

    // Insert
    {
        auto partObj = PartsModel::createPtr();
        partObj->partNumber = "PN12345";
        // These UUIDs should exist in the global database.
        // Using placeholders here.
        partObj->categoryUUID = QUuid::createUuid().toString();
        partObj->manufacturerUUID = QUuid::createUuid().toString();
        partObj->description = "A test part";
        partObj->creationTimestamp = QDateTime::currentDateTime();
        partObj->createdBy = "TestHarness";

        if(ctrl.insert(partObj)) {
            outPartId = partObj->id;
            qDebug() << "Inserted new part with ID:" << outPartId;
        } else {
            qDebug() << "Insert part failed.";
        }
    }

    // Read
    {
        auto objs = ctrl.fetchAll();
        qDebug() << "Reading all parts, count:" << objs.count();
        for (const auto& obj : objs) {
            qDebug() << "  - ID:" << obj->id << "PartNumber:" << obj->partNumber;
        }
    }

    // Modify
    if(!outPartId.isEmpty()){
        auto partObj = ctrl.fetch(outPartId);
        if(partObj) {
            partObj->description = "An updated test part";
            partObj->lastModifiedTimestamp = QDateTime::currentDateTime();
            partObj->lastModifiedBy = "TestHarnessModify";
            if(ctrl.modify(partObj)){
                qDebug() << "Modified part with ID:" << outPartId;
            } else {
                qDebug() << "Modify part failed for ID:" << outPartId;
            }
        }
    }

    // Delete
    if(!outPartId.isEmpty()) {
        if(ctrl.remove(outPartId)) {
            qDebug() << "Deleted part with ID:" << outPartId;
        } else {
            qDebug() << "Delete part failed for ID:" << outPartId;
        }
    }
     qDebug() << "--- Finished Testing PartsCtrl ---";
}

void testPartDatasheetLinksCtrl(QSqlDatabase& db, const QString& partId, const QString& datasheetId)
{
    qDebug() << "--- Testing PartDatasheetLinksCtrl ---";

    if(partId.isEmpty() || datasheetId.isEmpty()) {
        qDebug() << "Part or Datasheet ID is empty, skipping test.";
        return;
    }

    PartDatasheetLinksCtrl ctrl(db);
    if (!ctrl.isTableExist()) {
        QSqlError err;
        if (!ctrl.createTable(err)) {
            LOG_ERROR(err.text().toStdString());
            throw std::runtime_error(err.text().toStdString());
        }
        qDebug() << "Part_Datasheet_Links table created.";
    }
    
    QString newLinkId;
    // Insert
    {
        auto linkObj = PartDatasheetLinksModel::createPtr();
        linkObj->partUUID = partId;
        linkObj->datasheetUUID = datasheetId;
        if(ctrl.insert(linkObj)) {
            newLinkId = linkObj->id;
            qDebug() << "Inserted new part-datasheet link with ID:" << newLinkId;
        } else {
            qDebug() << "Insert part-datasheet link failed.";
        }
    }

    // Read
    {
        auto objs = ctrl.fetchAll();
        qDebug() << "Reading all part-datasheet links, count:" << objs.count();
        for (const auto& obj : objs) {
            qDebug() << "  - ID:" << obj->id << "PartID:" << obj->partUUID << "DatasheetID:" << obj->datasheetUUID;
        }
    }

    // Modify - Links are not typically modified. Skipping.

    // Delete
    if(!newLinkId.isEmpty()){
        if(ctrl.remove(newLinkId)) {
            qDebug() << "Deleted part-datasheet link with ID:" << newLinkId;
        } else {
            qDebug() << "Delete part-datasheet link failed for ID:" << newLinkId;
        }
    }

    qDebug() << "--- Finished Testing PartDatasheetLinksCtrl ---";
}


int main(int argc, char *argv[])
{
    QCoreApplication a(argc, argv);

    QString err;
    QString uuid = QUuid::createUuid().toString();
    QSqlDatabase db;
    QString path = QString("%1/part_test.db").arg(QCoreApplication::applicationDirPath());

    if (!SimpleDbManager::connect(path, err, uuid, db))
    {
        QString errInfo = QString("Database connection failed: %1").arg(err);
        LOG_ERROR(errInfo.toStdString());
        return -1;
    }
    
    qDebug() << "Database connected:" << path;

    try {
        testLibraryInfoCtrl(db);

        QString datasheetId, partId;
        testDatasheetsCtrl(db, datasheetId);
        testPartsCtrl(db, partId);
        
        // Re-create a part and datasheet to ensure they exist for the link test
        DatasheetsCtrl dsCtrl(db);
        auto ds = DatasheetsModel::createPtr();
        ds->title = "Link Test DS";
        dsCtrl.insert(ds);

        PartsCtrl partCtrl(db);
        auto part = PartsModel::createPtr();
        part->partNumber = "PN-LINK-TEST";
        part->categoryUUID = QUuid::createUuid().toString();
        part->manufacturerUUID = QUuid::createUuid().toString();
        partCtrl.insert(part);
        
        testPartDatasheetLinksCtrl(db, part->id, ds->id);

        // Clean up link test items
        dsCtrl.remove(ds->id);
        partCtrl.remove(part->id);

    } catch (const std::runtime_error& e) {
        LOG_ERROR(QString("A runtime error occurred: %1").arg(e.what()).toStdString());
        SimpleDbManager::close(uuid);
        return -1;
    }

    SimpleDbManager::close(uuid);
    qDebug() << "Database connection closed.";

    return 0;
} 