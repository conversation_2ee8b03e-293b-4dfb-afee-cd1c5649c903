#ifndef LIBRARYINFOCTRL_H
#define LIBRARYINFOCTRL_H

#include "../../sqlCtrlBase.h"
#include "../sqlModel/libraryinfomodel.h"

//============================================================================
/// file
/// brief      数据库信息表控制器
/// author     wm
/// version    1.0
/// date       2025/06/05
/// todo
//============================================================================

class LibraryInfoCtrl : public SqlCtrlBase
{
public:
    LibraryInfoCtrl(const QSqlDatabase& db);
    ~LibraryInfoCtrl();

    // 针对数据模型的操作
    // LibraryInfo is a single record table, but we use fetchAll for consistency.
    LibraryInfoModelPtr fetch() const;
    // end

    // 针对数据库表的操作
    bool insert(const LibraryInfoModelPtr &obj);
    bool modify(const LibraryInfoModelPtr &newValueObj);
    // end

public:
    QString tableName()       const override {return "LibraryInfo";}
    QString primaryKeyField() const override {return "LibraryUUID";}
    bool    recordExist(const QString &id) const override;
    virtual bool softDelete() const override { return true; }

private:
    QString tableSql()        const override;
    void loadTable();

private:
    LibraryInfoModelPtr mObj;
};

#endif // LIBRARYINFOCTRL_H