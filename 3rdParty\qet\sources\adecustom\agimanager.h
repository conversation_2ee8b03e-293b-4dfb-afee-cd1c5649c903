﻿#ifndef AGIMANAGER_H
#define AGIMANAGER_H

#include <QGraphicsItem>

class Diagram;

/**
 * @brief The AGIManager class AGI(ADE Graphics Item)Manager 用于管理使用removeItem()方法
 * 从Diagram上移除的图形对象, 当需要(撤销)时将对象释放, 析构 Diagram 时将对象删除
 */
class AGIManager
{
public:
    AGIManager(Diagram *diagram);
    ~AGIManager();

public:
    void manage(QGraphicsItem *item);
    void release(QGraphicsItem *item);

    void deleteItem(QGraphicsItem *item);

private:
    Diagram *mDiagram;
    QList<QGraphicsItem *> mItems;
};

#endif // AGIMANAGER_H
