﻿#include "adediagramproperties.h"

#include <QDebug>

ADEDiagramProperties::ADEDiagramProperties()
{
    // 初始化
    mEquipmentTextShowState = ADETextProperties::DGM_BothShow;
    mInterfaceTextShowState = ADETextProperties::DGM_OnlyMarkShow;
    mCableTextShowState     = ADETextProperties::DGM_OnlyMarkShow;
}

ADEDiagramProperties::~ADEDiagramProperties()
{

}

QDomElement ADEDiagramProperties::toXml(QDomDocument &doc) const
{
    // 根节点
    QDomElement root = doc.createElement(tagName());

    // 文本显示信息
    root.setAttribute("eqTextShowState", mEquipmentTextShowState);
    root.setAttribute("ifTextShowState", mInterfaceTextShowState);
    root.setAttribute("cbTextShowState", mCableTextShowState);

    // 所属系统样式信息
    QDomElement xmlBlSysStyle = doc.createElement("belongingSystemStyles");
    // TODO: 补全所属系统样式xml记录
    /*
        <belongingSystemStyles>
            <belongingSystemStyle name="所属系统">
                <adeStyle>
                    <pen widthF="1" style="SolidLine" color="#000000" />
                    <brush style="SolidPattern" color="#aaffff" />
                    <text font="Sans Serif,7,-1,5,0,0,0,0,0,0,normal" color="#000000">
                </adeStyle>
            </belongingSystemStyle>
        </belongingSystemStyles>
     */
    if (!mBelongingSysStyles.isEmpty())
    {
        for (QString name : mBelongingSysStyles.keys())
        {
            QDomElement style = doc.createElement("belongingSystemStyle");
            style.setAttribute("name", name);

            AdeElementStyle blStyle = mBelongingSysStyles.value(name).first;
            QJsonObject blFontStyle = mBelongingSysStyles.value(name).second;

            QDomElement adeStyle = blStyle.toXml(doc);

            QDomElement fontStyle = doc.createElement("text");
            fontStyle.setAttribute("font", blFontStyle["font"].toString());
            fontStyle.setAttribute("color", blFontStyle["color"].toString());

            adeStyle.appendChild(fontStyle);
            style.appendChild(adeStyle);
            xmlBlSysStyle.appendChild(style);
        }
    }
    root.appendChild(xmlBlSysStyle);

    return root;
}

bool ADEDiagramProperties::fromXml(QDomElement &e)
{
    if (e.tagName() != tagName())
        return false;

    mEquipmentTextShowState = ADETextProperties::textStateFromStr(e.attribute("eqTextShowState"));
    mInterfaceTextShowState = ADETextProperties::textStateFromStr(e.attribute("ifTextShowState"));
    mCableTextShowState     = ADETextProperties::textStateFromStr(e.attribute("cbTextShowState"));

    // TODO: 读取所属系统样式信息
    mBelongingSysStyles.clear();

    for (QDomNode node = e.firstChild() ; !node.isNull() ; node = node.nextSibling())
    {
        QDomElement elmt = node.toElement();

        if (elmt.tagName() == "belongingSystemStyles")
        {
            for (QDomNode blNode = elmt.firstChild() ; !blNode.isNull() ; blNode = blNode.nextSibling())
            {
                QDomElement blElmt = blNode.toElement();

                QString blName;
                AdeElementStyle style;
                QJsonObject fontStyle;
                if (blElmt.tagName() == "belongingSystemStyle")
                {
                    blName = blElmt.attribute("name");
                    for (QDomNode adeStyleNode = blElmt.firstChild() ; !adeStyleNode.isNull() ; adeStyleNode = adeStyleNode.nextSibling())
                    {
                        QDomElement adeStyleElmt = adeStyleNode.toElement();
                        if (adeStyleElmt.tagName() == AdeElementStyle::tagName())
                        {
                            style.fromXml(adeStyleElmt);
                            for (QDomNode adeNode = adeStyleElmt.firstChild() ; !adeNode.isNull() ; adeNode = adeNode.nextSibling())
                            {
                                QDomElement adeElmt = adeNode.toElement();
                                if (adeElmt.tagName() != "text")
                                    continue;
                                else
                                {
                                    fontStyle["font"] = adeElmt.attribute("font");
                                    fontStyle["color"] = adeElmt.attribute("color");
                                }
                            }
                        }
                    }
                }

                mBelongingSysStyles.insert(blName, qMakePair(style, fontStyle));
            }
        }
    }

    return true;
}


ADETextProperties::ADEItemTextShowState ADEDiagramProperties::getTextShowStateByItemType(Ade::DiagramItemType type)
{
    if (type == Ade::DGM_Equipment)
        return mEquipmentTextShowState;
    else if (type == Ade::DGM_Interface)
        return mInterfaceTextShowState;
    else if (type == Ade::DGM_Cable)
        return mCableTextShowState;

    return ADETextProperties::DGM_BothShow;
}

void ADEDiagramProperties::updateDiagramBelongingSystem(const QStringList &belongingSystem)
{
    AdeElementStyle style;
    QPen pen;
    pen.setStyle(Qt::SolidLine);
    pen.setWidthF(1.0);
    pen.setColor(Qt::black);
    style.setPen(pen);
    QBrush brush;
    brush.setStyle(Qt::NoBrush);
    brush.setColor(Qt::black);
    style.setBrush(brush);

    QFont font;
    font.setFamily(Ade::fontFamilyChangFangSong());
    font.setPixelSize(8);
    QJsonObject fontStyle;
    fontStyle["font"] = font.toString();
    fontStyle["color"] = QColor(Qt::black).name();

    for (QString belongSysName : belongingSystem)
    {
        if (mBelongingSysStyles.keys().contains(belongSysName) || belongSysName.isEmpty())
            continue;
        else
        {
            mBelongingSysStyles.insert(belongSysName, qMakePair(style, fontStyle));
        }
    }
}

void ADEDiagramProperties::updateBelongingSysStyle(const QString &belongingSysName, AdeElementStyle style, QJsonObject fontStyle)
{
    for (QString name : mBelongingSysStyles.keys())
    {
        if (name == belongingSysName)
        {
            mBelongingSysStyles.insert(name, qMakePair(style, fontStyle));
        }
    }
}
