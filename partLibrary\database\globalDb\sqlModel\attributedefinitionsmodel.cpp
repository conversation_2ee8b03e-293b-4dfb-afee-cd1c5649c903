#include "attributedefinitionsmodel.h"
#include <QJsonDocument>
#include <QJsonArray>
#include <QJsonObject>
#include <QSqlRecord>
#include <QVariantMap>

// 字段名称定义
const QString AttributeDefinitionsModel::FIELD_ID = "AttributeUUID";
const QString AttributeDefinitionsModel::FIELD_NAME = "Name";
const QString AttributeDefinitionsModel::FIELD_DATA_TYPE = "DataType";
const QString AttributeDefinitionsModel::FIELD_OPTIONS_JSON = "Options_JSON";
const QString AttributeDefinitionsModel::FIELD_DESCRIPTION = "Description";
const QString AttributeDefinitionsModel::FIELD_IRDI = "IRDI";
const QString AttributeDefinitionsModel::FIELD_UNIT = "Unit";
const QString AttributeDefinitionsModel::FIELD_DEFAULT_VALUE = "DefaultValue";
const QString AttributeDefinitionsModel::FIELD_VALIDATION_RULE = "ValidationRule";
const QString AttributeDefinitionsModel::FIELD_IS_USER_DEFINED = "IsUserDefined";
const QString AttributeDefinitionsModel::FIELD_IS_DELETED = "IsDeleted";

QVariantMap AttributeDefinitionsModel::modelToDbMap() const
{
    QVariantMap map;
    map.insert(FIELD_ID, id);
    map.insert(FIELD_NAME, name);
    map.insert(FIELD_DATA_TYPE, dataTypeToString(dataType));
    map.insert(FIELD_OPTIONS_JSON, optionsToJson());
    map.insert(FIELD_DESCRIPTION, description);
    map.insert(FIELD_IRDI, irdi);
    map.insert(FIELD_UNIT, unit);
    map.insert(FIELD_DEFAULT_VALUE, defaultValue);
    map.insert(FIELD_VALIDATION_RULE, validationRule);
    map.insert(FIELD_IS_USER_DEFINED, isUserDefined);
    return map;
}

void AttributeDefinitionsModel::dbToModel(const QSqlRecord &record)
{
    id = record.value(FIELD_ID).toString();
    name = record.value(FIELD_NAME).toString();
    dataType = stringToDataType(record.value(FIELD_DATA_TYPE).toString());
    optionsFromJson(record.value(FIELD_OPTIONS_JSON).toString());
    description = record.value(FIELD_DESCRIPTION).toString();
    irdi = record.value(FIELD_IRDI).toString();
    unit = record.value(FIELD_UNIT).toString();
    defaultValue = record.value(FIELD_DEFAULT_VALUE).toString();
    validationRule = record.value(FIELD_VALIDATION_RULE).toString();
    isUserDefined = record.value(FIELD_IS_USER_DEFINED).toBool();
}

QString AttributeDefinitionsModel::dataTypeToString(AttributeDefinitionsModel::AttrDataType type)
{
    switch (type)
    {
    case Integer:
        return "Integer";
    case Float:
        return "Float";
    case Bool:
        return "Bool";
    case Enumeration:
        return "Enumeration";
    case String:
    default:
        return "String";
    }
}

AttributeDefinitionsModel::AttrDataType AttributeDefinitionsModel::stringToDataType(const QString &typeStr)
{
    if (typeStr == "Integer")
        return Integer;
    if (typeStr == "Float")
        return Float;
    if (typeStr == "Bool")
        return Bool;
    if (typeStr == "Enumeration")
        return Enumeration;
    return String;
}

void AttributeDefinitionsModel::optionsFromJson(const QString &json)
{
    options.clear();
    if (json.isEmpty())
        return;

    QJsonDocument doc = QJsonDocument::fromJson(json.toUtf8());
    if (doc.isArray())
    {
        QJsonArray jsonArray = doc.array();
        for (const QJsonValue &value : jsonArray)
        {
            options.append(value.toString());
        }
    }
}

QString AttributeDefinitionsModel::optionsToJson() const
{
    if (options.isEmpty())
        return QString();

    QJsonArray jsonArray;
    for (const QString &option : options)
    {
        jsonArray.append(option);
    }
    QJsonDocument doc(jsonArray);
    return doc.toJson(QJsonDocument::Compact);
} 