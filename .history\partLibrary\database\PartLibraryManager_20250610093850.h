#ifndef PARTLIBRARYMANAGER_H
#define PARTLIBRARYMANAGER_H

#include <QObject>
#include <QHash>
#include <QString>
#include <memory>
#include <QSqlDatabase>

#include "globalDb/sqlModel/manufacturermodel.h"
#include "globalDb/sqlModel/categoriesmodel.h"
#include "partDb/sqlModel/libraryinfomodel.h"
#include "partDb/sqlModel/partmodel.h"
#include "partDb/viewModel/PartViewModel.h"
#include "partDb/viewModel/PartViewModelAssembler.h"
#include "../tests/SimpleDbManager.h"

// --- 前向声明所有 Ctrl 类 ---
class ManufacturerCtrl;
class CategoriesCtrl;
class AttributeDefinitionsCtrl;
class CategoryAttributeLinksCtrl;
class LibraryInfoCtrl;
class PartCtrl;
class DatasheetsCtrl;
class PartDatasheetLinksCtrl;
class PartImagesCtrl;
class ChangeLogCtrl;

/**
 * @brief 全局库上下文，封装全局库的数据库连接和所有控制器
 */
struct GlobalLibraryContext {
    QSqlDatabase db;
    std::unique_ptr<ManufacturerCtrl> manufacturerCtrl;
    std::unique_ptr<CategoriesCtrl> categoriesCtrl;
    std::unique_ptr<AttributeDefinitionsCtrl> attributeDefinitionsCtrl;
    std::unique_ptr<CategoryAttributeLinksCtrl> categoryAttributeLinksCtrl;

    // 禁用拷贝，因为 unique_ptr 不可拷贝
    GlobalLibraryContext() = default;
    GlobalLibraryContext(GlobalLibraryContext&&) = default;
    GlobalLibraryContext& operator=(GlobalLibraryContext&&) = default;
    GlobalLibraryContext(const GlobalLibraryContext&) = delete;
    GlobalLibraryContext& operator=(const GlobalLibraryContext&) = delete;
};

/**
 * @brief 单个零件库上下文，封装其数据库连接和所有控制器
 */
struct PartLibraryContext {
    QString libraryId; // 库的UUID，来自LibraryInfo表
    QString filePath;  // .ptlib 文件的完整路径

    QSqlDatabase db;
    std::unique_ptr<LibraryInfoCtrl> libraryInfoCtrl;
    std::unique_ptr<PartCtrl> partCtrl;
    std::unique_ptr<DatasheetsCtrl> datasheetsCtrl;
    std::unique_ptr<PartDatasheetLinksCtrl> partDatasheetLinksCtrl;
    std::unique_ptr<PartImagesCtrl> partImagesCtrl;
    std::unique_ptr<ChangeLogCtrl> changeLogCtrl;

    // 禁用拷贝
    PartLibraryContext() = default;
    PartLibraryContext(PartLibraryContext&&) = default;
    PartLibraryContext& operator=(PartLibraryContext&&) = default;
    PartLibraryContext(const PartLibraryContext&) = delete;
    PartLibraryContext& operator=(const PartLibraryContext&) = delete;
};


class PartLibraryManager : public QObject
{
    Q_OBJECT
private:
    explicit PartLibraryManager(QObject *parent = nullptr);
    ~PartLibraryManager();

public:
    // 单例访问接口
    static PartLibraryManager* instance();

    // 禁用拷贝和赋值
    PartLibraryManager(const PartLibraryManager&) = delete;
    PartLibraryManager& operator=(const PartLibraryManager&) = delete;

public:
    /**
     * @brief 初始化管理器，加载所有元器件库
     * @param rootPath 元器件库的根目录 (e.g., ".../PartLibrary/")
     * @return true-初始化成功, false-失败
     */
    bool initialize(const QString& rootPath);

    /**
     * @brief 关闭所有数据库连接，释放资源
     */
    void shutdown();

    // --- 高层级业务 API ---

    /**
     * @brief 创建一个新的空白用户库
     * @param libraryName 库的显示名称
     * @param directoryPath 存储库文件的完整目录路径
     * @param[out] errorInfo 错误信息
     * @return 成功则返回新库的UUID, 否则返回空QString
     */
    QString createNewPartLibrary(const QString& libraryName, const QString& directoryPath, QString& errorInfo);

    /**
     * @brief 删除一个指定的用户库
     * @param libraryId 要删除库的UUID
     * @return true-删除成功, false-失败
     */
    bool deletePartLibrary(const QString& libraryId);

    // --- 数据聚合与访问 API ---

    /**
     * @brief 获取所有已加载的零件库信息
     * @return LibraryInfoModel 列表
     */
    QList<LibraryInfoModelPtr> getAllLoadedLibraries() const;

    /**
     * @brief 获取一个零件的完整视图模型 (聚合了全局库信息)
     * @param libraryId 零件所在的库UUID
     * @param partId    零件自身的UUID
     * @return 完整的 PartViewModel
     */
    PartViewModel getPartViewModel(const QString& libraryId, const QString& partId);

    /**
     * @brief 获取指定库中所有零件的基础模型
     * @param libraryId 目标库的UUID
     * @return PartModel 列表
     */
    PartModelList getPartsInLibrary(const QString& libraryId) const;

    /**
     * @brief 获取全局唯一的制造商列表
     */
    ManufacturerModelList getManufacturers() const;

    /**
     * @brief 获取全局唯一的分类列表
     */
    CategoriesModelList getCategories() const;

    // ... 其他需要的聚合或直通(pass-through)方法

private:
    bool loadGlobalLibrary(const QString& globalDbPath);
    bool loadPartLibrary(const QString& ptlibPath);

    // 清理和关闭一个零件库的连接
    void closePartLibrary(const QString& libraryId);

private:
    QString m_rootPath;

    // 全局库上下文实例
    GlobalLibraryContext m_globalContext;

    // 已加载的零件库上下文，Key为库的UUID
    QHash<QString, PartLibraryContext> m_partLibraries;
};

#endif // PARTLIBRARYMANAGER_H 