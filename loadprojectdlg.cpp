﻿#include "loadprojectdlg.h"
#include "ui_loadprojectdlg.h"

#include <QFile>
#include <QDir>
#include <QFileDialog>
#include <QDebug>
#include <QFont>
#include <QIcon>
#include <QSize>
#include <QSettings>
#include <QLabel>
#include <QMessageBox>

#include "ade.h"
#include "newprjdlg.h"
#include "customDialogStyle/dialogstylemodifier.h"
#include "utility/instrumentor.h"

// 调试
#include "partLibrary/tests/test_partdb.h"
#include "partLibrary/tests/test_globaldb.h""
#include "partLibrary/PartLibraryManager.h"
// end

LoadProjectDlg::LoadProjectDlg(QWidget *parent) :
    QDialog(parent),
    ui(new Ui::openProjectDlg)
{
    ui->setupUi(this);

    this->resize(600, 400);

    connect(ui->OP_openProj_btn,&QPushButton::clicked,this,[=](){
        openProjBtnClicked();
    });

    connect(ui->OP_newProj_btn,&QPushButton::clicked,this,[=](){
        newPrjDlg dlg;
        if (dlg.exec() == QDialog::Accepted)
        {
            mPrjName = dlg.getPrjName();
            mPrjPath = dlg.getPrjPath();

            done(QDialog::Accepted);
            close();
        }
    });

    initWidget();
    showRecentProject();

    DialogStyleModifier::getInstance()->installStyle(this, QSize(600, 450));

    // 调试代码
    {
        //run_globalDb_tests();
        //run_partDb_tests();
        // QString err;
        PLM->deletePartLibrary("{9bb8444f-08b0-4bc1-aaf4-37811d6cbc45}");

        // exit(0);
        // QString err;
        // QString uuid = QUuid::createUuid().toString();
        // QSqlDatabase db;
        // QString path = QString("%1/test.ptlib").arg(QApplication::applicationDirPath());

        // if (!SQL_DB->connect(path, err, uuid, db))
        // {
        //     QString errInfo = QString("%1创建失败：").arg(path).arg(err);

        //     LOG_ERROR(errInfo);
        //     throw std::runtime_error(errInfo.toStdString());
        // }

        // ManufacturerCtrl* ctrl = new ManufacturerCtrl(db);
        // if (!ctrl->isTableExist())
        // {
        //     QSqlError err;
        //     if (!ctrl->createTable(err))
        //     {
        //         LOG_ERROR(err.text().toStdString());
        //         throw std::runtime_error(err.text().toStdString());
        //     }
        // }
        // Manufacturer_Factory->setCtrlObj(uuid, ctrl);

        // // 读取
        // {
        //     auto objs = ctrl->fetchAll();
        //     for (auto obj : objs)
        //     {
        //         qDebug() << "READ TEST" << obj->name << obj->id;
        //     }
        // }

        // // 新增
        // {
        //     auto mfObj = ManufacturerModel::createPtr();
        //     mfObj.reset(new ManufacturerModel);
        //     mfObj->name = "测试厂家2";
        //     mfObj->website = "www.web.com";
        //     mfObj->description = "测试描述";

        //     ctrl->insert(mfObj);
        // }

        // // 修改
        // {
        //     auto mfObj = ctrl->fetch("{4cb42983-d71e-4eed-9bf2-0b4176a53d9f}");
        //     if (mfObj)
        //     {
        //         mfObj->name = "PPPOOO";
        //         mfObj->website = "aa";
        //         mfObj->description = "ss";

        //         ctrl->modify(mfObj);
        //     }
        // }

        // // 删除
        // {
        //     ctrl->remove("{4cb42983-d71e-4eed-9bf2-0b4176a53d9f}");
        // }
    }
}

LoadProjectDlg::~LoadProjectDlg()
{
    delete ui;
}

void LoadProjectDlg::initWidget()
{
    QFont fontBtn;
    fontBtn.setPixelSize(14);

    ui->OP_openProj_btn->setFont(fontBtn);
    ui->OP_newProj_btn->setFont(fontBtn);

    ui->OP_openProj_btn->setIcon(QIcon(":/icon/icon_open.png"));
    ui->OP_newProj_btn->setIcon(QIcon(":/icon/icon_new.png"));

    ui->OP_openProj_btn->setIconSize(QSize(32, 32));
    ui->OP_newProj_btn->setIconSize(QSize(32, 32));

    QFont fontTitle;
    fontTitle.setPixelSize(13);
    ui->OP_recentTitle_label->setText("最近使用的工程>>");
    ui->OP_recentTitle_label->setFont(fontTitle);

    ui->OP_recent_Frame->setStyleSheet("background: #F6F8FB");
}

bool LoadProjectDlg::showRecentProject()
{
    QVBoxLayout *layout = new QVBoxLayout();

    QSettings prjSettings(Ade::CompanyName, Ade::AppName);
    QStringList recentPrj = prjSettings.value(Ade::ST_RecentlyUsed).toStringList();

    while (recentPrj.size() > 7)
    {
        recentPrj.removeLast();
    }

    QStringList existedPrj;
    for (QString prj : recentPrj)
    {
        // NOTE: V1.3.6 兼容性
        if (!prj.contains(".linker"))
            continue;

        QFile fi(prj);
        if(fi.exists())
            existedPrj.append(prj);
    }

    // 占位符
    QWidget* spacer = new QWidget();
    spacer->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Expanding);

    foreach(QString path, existedPrj)
    {
        QVBoxLayout *group = new QVBoxLayout();

        QLabel *nameLabel = new QLabel();
        QLabel *pathLabel = new QLabel();

        QString slash = "/";
        if (path.contains("\\"))
            slash = "\\";

        nameLabel->setText(QString("<a href=\"%1\">%2").arg(path).arg(path.section(slash, -1)));
        nameLabel->setStyleSheet("font-size: 20px; font-weight: bold; text-align: left");
        nameLabel->setContextMenuPolicy(Qt::PreventContextMenu);    // 禁止弹出右键菜单
        connect(nameLabel, &QLabel::linkActivated, this, [=](){
            hide();

            emit openProject(path);

            accept();
            close();
        });

        pathLabel->setText(path);
        pathLabel->setStyleSheet("color: gray; font-size: 12px; text-align: left");

        group->addWidget(nameLabel);
        group->addWidget(pathLabel);
        group->setSpacing(0);

        layout->addLayout(group);
    }

    layout->setSpacing(1);
    layout->setMargin(4);
    layout->addWidget(spacer);

    ui->OP_recent_Frame->setLayout(layout);
    ui->OP_recent_Frame->setContentsMargins(0, 0, 0, 0);

    return true;
}

void LoadProjectDlg::openProjBtnClicked()
{
    // // 调试：故意让程序崩溃，测试breakpad功能
    // QPushButton *B = nullptr;
    // B->setAutoDefault(false);
    // //

    QFileDialog fileDialog(this);

    fileDialog.setNameFilter("*." + Ade::PrjSuffix);
    fileDialog.setFileMode(QFileDialog::ExistingFile);
    fileDialog.setDirectory(Ade::defaultProjectFolderPath());

    if (fileDialog.exec() == QDialog::Accepted)
    {
        QStringList files = fileDialog.selectedFiles();
        if(files.size() > 0)
        {
            emit openProject(files.first());

            accept();
            close();
        }
    }
}

