#ifndef MANUFACTURERCTRL_H
#define MANUFACTURERCTRL_H

#include "../../sqlCtrlBase.h"
#include "../sqlModel/manufacturermodel.h"

//============================================================================
/// file
/// brief      元器件库厂家表控制器
/// author     wm
/// version    1.0
/// date       2025/06/04
/// todo
//============================================================================

class ManufacturerCtrl : public SqlCtrlBase// 指定模板参数
{
public:
    ManufacturerCtrl(const QSqlDatabase& db);
    ~ManufacturerCtrl();

    // 针对数据模型的操作
    ManufacturerModelList fetchAll() const;
    ManufacturerModelPtr  fetch(const QString &id) const;
    QList<ManufacturerModelPtr> orderedList() const;
    bool modelExist(const QString &id) const;
    // end

    // 针对数据库表的操作
    bool insert(const ManufacturerModelPtr &obj);
    bool modify(const ManufacturerModelPtr &newValueObj);
    bool remove(const QString &id);
    // end

public:
    QString tableName()       const override {return "Manufacturers";}
    QString primaryKeyField() const override {return "ManufacturerUUID";}
    bool    recordExist(const QString &id) const override;
    virtual bool softDelete() const override { return true; }

private:
    QString tableSql()        const override;
    void loadTable();

private:
    ManufacturerModelList mAllObj;
};

#endif // MANUFACTURERCTRL_H
