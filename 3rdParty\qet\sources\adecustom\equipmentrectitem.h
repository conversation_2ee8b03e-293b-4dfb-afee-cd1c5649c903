#ifndef EQUIPMENTRECTITEM_H
#define EQUIPMENTRECTITEM_H

#include <QObject>
#include <QGraphicsRectItem>
#include <QVector>
#include <QGraphicsLineItem>

class RectPointHandlerItem : public QObject , public QGraphicsEllipseItem
{
    Q_OBJECT
public:
    RectPointHandlerItem(QGraphicsItem * parent = 0);
    ~RectPointHandlerItem();

protected:
    void mousePressEvent(QGraphicsSceneMouseEvent *event) override;
    void mouseMoveEvent(QGraphicsSceneMouseEvent *event) override;
    void mouseReleaseEvent(QGraphicsSceneMouseEvent *event) override;

    void hoverEnterEvent(QGraphicsSceneHoverEvent *event) override;
    void hoverLeaveEvent(QGraphicsSceneHoverEvent *event) override;
//    QVariant itemChange(
//        GraphicsItemChange, const QVariant&) override;

signals:
    /**
     * @brief popPointPos 弹出当前移动位置
     */
    void popPointPos(QPointF);

    void stopMove();

    void popCanPress(bool isPress);
};

/**
 * @brief The AdeHandeItem class
 */
class EquipmentRectItem : public QObject , public QGraphicsRectItem
{
    Q_OBJECT
public:
    EquipmentRectItem(const QRectF & rect, const QColor &color, QSize minSize = QSize(50,50));
    /**
     * @brief getPress 获取当前按压状态
     * @return
     */
    bool getMousePressState() const;

    /**
     * @brief setDrag 设置是否可以拖拽
     * @param isDrag true 可以拖拽 false 不可以拖拽
     * @return
     */
    void enableStretchHandler(bool enable = true);

    void setColor(const QColor &color);

    static QColor defaultColor() {return Qt::blue;}

protected:
    QVariant itemChange(GraphicsItemChange, const QVariant&) override;

public:

    QPainterPath shape() const override;

signals:
    /**
     * @brief popFinishMoveRectByScene 结束移动后 返回相应位置及其大小
     * originX  原点坐标X
     * originY  原点坐标Y
     * width    宽度
     * height   高度
     *          |
     *          |
     *          |
     *          |
     * -------(原点)-------->  (x)
     *          |
     *          |
     *          |
     *          |
     *          V
     *          (y)
     */

//    void popFinishMoveRectByScene(int originX,int originY,int width,int height);
    void popFinishMoveRectByScene(QPointF point,QSize size);

    /**
     * @brief popIsPress 是否被按压
     * @param isPress
     */
    void popIsPress(bool isPress);
public slots:

protected slots:
    void firstPointMove(QPointF);
    void secondPointMove(QPointF);
    void thridPointMove(QPointF);
    void fourthPointMove(QPointF);
    void leftPointMove(QPointF);
    void topPointMove(QPointF);
    void rightPointMove(QPointF);
    void bottomPointMove(QPointF);
    void stopMove();
    void setCanPress(bool isPress);

private:
    QColor mDefaultColor;

    RectPointHandlerItem* m_point1 = nullptr;
    RectPointHandlerItem* m_point2 = nullptr;
    RectPointHandlerItem* m_point3 = nullptr;
    RectPointHandlerItem* m_point4 = nullptr;
    RectPointHandlerItem* m_leftPoint = nullptr;
    RectPointHandlerItem* m_topPoint = nullptr;
    RectPointHandlerItem* m_rightPoint = nullptr;
    RectPointHandlerItem* m_bottomPoint = nullptr;


    void updatePoints(const QRectF & rect);

    QSize m_minSize;
    qreal m_offset = 1;
    bool  m_isPress = false;
    QSizeF m_originSize;// 初试大小
    QRectF m_lastRect;  // 最后一次改变的大小
    /**
     * @brief accordSceneGrid 返回当前 点位是否合法
     * @param pos
     * @return
     */
    QPointF accordSceneGrid(const QPointF& pos);

    /**
     * @brief compareValue 比较 n1 n2 是否相等
     * @param n1
     * @param n2
     * @return -1 (n1 < n2),
     *          0 (n1 = n2),
     *          1 (n1 > n2)

    static int compareValue(const qreal &n1,const qreal &n2) noexcept{
        int ret = -1;// 默认 n1 < n2
        if(qFabs(n1 - n2) < 1e-6){
            // n1 等于 n2
            ret = 0;// 相等
        }
        else{
            // 不相等
            if(n1 - n2 < 0){
                ret = -1;// n1 < n2 ==>  n1 - n2 <  0
            }
            else{
                ret = 1;// n1  > n2  ==>  n1 - n2 > 0
            }
        }
        return ret;

    }
    */
};

class Diagram;

/**
 * @brief The NoTransformFineLineItem class 粗细不变的直线
 */
class NoTransformFineLineItem : public QGraphicsLineItem
{
public:
    NoTransformFineLineItem(QGraphicsItem *parent = nullptr) : QGraphicsLineItem(parent) {}

protected:
    void paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget = nullptr) override;

};

/**
 * @brief The AdeHandleDragRect class 可拖拽框
 */
class AdeHandleDragRect : public QObject , public QGraphicsPathItem
{
    Q_OBJECT
public:
    AdeHandleDragRect(QSize minSize,QGraphicsItem *parent = 0);

    QSize getRectSize() const;
    QPointF getCenterPos() const;

    /**
     * @brief isValid 判断拖拽框是否有效
     * @return
     */
    bool isValid() const;

    /**
     * @brief updateRectSize 更新大小
     * @param width 宽度
     * @param height 高度
     */
    void updateRectSize(const qreal& width,const qreal& height);

    /**
     * @brief minReferenceRectDiagonalPoint 最小缩放矩形相对与原点的对角点
     * @return
     */
    QPointF minReferenceRectDiagonalPoint() const;

protected:
    QSize m_minSize; // 最小
    QGraphicsRectItem *mMinReferenceRect = nullptr;// 最小缩放框
};

#endif // EQUIPMENTRECTITEM_H
