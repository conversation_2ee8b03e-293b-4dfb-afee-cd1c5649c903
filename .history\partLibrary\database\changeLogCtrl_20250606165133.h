#pragma once

#include <QList>
#include <QSqlDatabase>
#include "changeLogEntry.h"

namespace part_library
{
namespace database
{

/**
 * @brief Provides stateless data access functions for the ChangeLog table.
 *
 * This class contains static methods to add and retrieve log entries.
 * It follows a stateless pattern suitable for append-only, high-volume data
 * like logs, avoiding in-memory caching.
 */
class ChangeLogCtrl
{
public:
    /**
     * @brief Adds a new log entry to the database.
     * @param db The database connection.
     * @param log The ChangeLogEntry object to add.
     * @return True on success, false otherwise.
     */
    static bool addLog(QSqlDatabase& db, const ChangeLogEntry& log);

    /**
     * @brief Retrieves all log entries for a given record UUID.
     * @param db The database connection.
     * @param recordUUID The UUID of the record (e.g., a Part).
     * @return A list of ChangeLogEntry objects, ordered by timestamp descending.
     */
    static QList<ChangeLogEntry> getLogsForRecord(QSqlDatabase& db, const QString& recordUUID);
};

} // namespace database
} // namespace part_library 