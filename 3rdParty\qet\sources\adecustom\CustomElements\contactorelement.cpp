﻿#include "contactorelement.h"

#include <QGraphicsScene>
#include <QDebug>

#include "../diagram.h"
#include "../qetxml.h"
#include "../qetgraphicsitem/terminal.h"
#include "../qetgraphicsitem/dynamicelementtextitem.h"
#include "../qetgraphicsitem/conductor.h"

#include "connectorwiringelement.h"

#include "layoutDiagram/diagramWgt/contactorpusher.h"
#include "3rdParty/qet/sources/CustomExtend/regionrectitem.h"
#include "3rdParty/logBusiness/logbusiness.h"

ContactorElement::ContactorElement(const ElementsLocation &location,
                                   int *state, Element *parentElmt, const QString &uuid,
                                   FunctionType fType) :
    Element(location, nullptr, state, Element::Simple, uuid, parentElmt),
    mTextOffset(8),
    mFunctionType(fType),
    mUserMovable(true),
    mTextEventFilterInstalled(false)
{
    for (auto t : terminals())
    {
        t->setDrawTermianl(true);
        t->setDrawHover(true);
    }

    // 端点元素的层级最高
    setZValue(99999);

    textItem()->setFlag(QGraphicsItem::ItemIsSelectable, false);
    textItem()->setFlag(QGraphicsItem::ItemIsMovable, false);
    // FIXME: 防止自动进入DiagramTextItem::ItemChange函数：由于DiagramTextItem具备文本中心点旋转的功能
    // 其内部记录了中心位置, 但对于存在parentElement的情况, 其中心位置记录不准确导致进入itemChange函数中时
    // 文本位移距离错误；
    textItem()->setFlag(QGraphicsItem::ItemSendsGeometryChanges, false);

    if (parentElmt)
    {
        Element *rotateEle = nullptr;
        if (parentElmt->parentElement())
            rotateEle = parentElmt->parentElement();
        else
            rotateEle = parentElmt;

        connect(rotateEle, &Element::rotationChanged, this, &ContactorElement::adjustTextToVisualPosition);
    }

    connect(textItem(), &DynamicElementTextItem::plainTextChanged, [=](){
        adjustTextPos();
    });

    // 当元素已经被添加到场景上时, 才能进行事件过滤器的安装
    if (scene() && !mTextEventFilterInstalled)
    {
        textItem()->installSceneEventFilter(this);
        mTextEventFilterInstalled = true;
    }

    // 芯线连接图上连接器上接触件元素端子禁止使能
    if (parentElmt)
    {
        if (parentElmt->adeType() == ConnectorWiringElement::ADEType)
        {
            terminal()->setEnabled(false);
            //terminal()->setDrawTermianl(false);
        }
    }

    if (scene())
    {
        terminal()->installSceneEventFilter(this);
    }

}


ContactorElement::~ContactorElement()
{

}

QRectF ContactorElement::boundingRect() const
{
    // return ( QRectF( QPointF(-mHalfLength, -mHalfLength) ,
    //                  QSizeF(mHalfLength * 2, mHalfLength * 2) ) );

    return ( QRectF( QPointF(-mHalfLength * 3 / 4, -mHalfLength) ,
                     QSizeF(mHalfLength * 1.5 - 1, mHalfLength * 2) ) );
}

bool ContactorElement::fromXml(QDomElement& e, QHash<int, Terminal*>& table_id_adr)
{
    bool res = Element::fromXml(e, table_id_adr);

    if (res)
    {
        if (e.hasAttribute("ADEFatherUuid"))
            mADEFatherUuid = e.attribute("ADEFatherUuid");

        if (e.hasAttribute("isVisible"))
        {
            QString show = e.attribute("isVisible");
            if (show == "1")
            {
                this->setVisible(true);
                this->textItem()->setVisible(true);
            }
            else if (show == "0")
                this->setVisible(false);
        }

        if (e.hasAttribute("functionType"))
        {
            mFunctionType = (FunctionType)e.attribute("functionType").toInt();
        }
        else
        {
            if (pointName() == "接壳")
            {
                mFunctionType = Enclosure;
            }
            else
            {
                mFunctionType = Contactor;
            }
        }

    }

    //if (!mADEFatherUuid.isEmpty())
    {
        textItem()->setFlag(QGraphicsItem::ItemSendsGeometryChanges, false);
        textItem()->setFlag(QGraphicsItem::ItemIsSelectable, false);
        textItem()->setFlag(QGraphicsItem::ItemIsMovable, false);

        textItem()->installSceneEventFilter(this);
    }

    return res;
}

QDomElement ContactorElement::toXml(QDomDocument& document,
                                    QHash<Terminal*, int>& table_adr_id) const
{
    // 先在Element基类中创建xml元素, 再补充自定义信息
    QDomElement elmtXml = Element::toXml(document, table_adr_id);

    if (!mADEFatherUuid.isEmpty())
        elmtXml.setAttribute("ADEFatherUuid", mADEFatherUuid);

    if (isVisible())
        elmtXml.setAttribute("isVisible", true);
    else
        elmtXml.setAttribute("isVisible", false);

    elmtXml.setAttribute("functionType", mFunctionType);

    return elmtXml;
}


ContactorElement::ContactorType ContactorElement::contactorType() const
{
    if (m_location.xml().hasAttribute("ADEContactorType"))
    {
        return (ContactorType)m_location.xml().attribute("ADEContactorType").toInt();
    }

    return ContactorType::Unknown;
}

void ContactorElement::setADEFatherUuid(const QString &uuid)
{
    mADEFatherUuid = uuid;
}

void ContactorElement::changeLocation(const ElementsLocation &newLocation)
{
    Element::changeLocation(newLocation);
}

bool ContactorElement::canRotate() const
{
    return false;
}

void ContactorElement::setFunctionType(FunctionType fType)
{
    mFunctionType = fType;
    if (fType == FunctionType::Enclosure)
    {
        textItem()->setPlainText("接壳");
    }
}

void ContactorElement::mousePressEvent(QGraphicsSceneMouseEvent *event)
{
    pressEvent(event);
    event->accept();
}

void ContactorElement::mouseMoveEvent(QGraphicsSceneMouseEvent *event)
{
    moveEvent(event);
    event->accept();
}

void ContactorElement::mouseReleaseEvent(QGraphicsSceneMouseEvent *event)
{
    releaseEvent(event);
    event->accept();
}

void ContactorElement::hoverEnterEvent(QGraphicsSceneHoverEvent *event)
{
    // 交换点位修改为鼠标直接拖动交换

    // 手动设置焦点，避免item可接收hoverEnterEvent,无法接受keyPressEvent;
    this->scene()->views().first()->setFocus();

    if (event->modifiers() == Qt::ControlModifier)
    {
        // 按下空格时显示拉伸连接器长度的抓手
        if ( /*event->modifiers() == Qt::ControlModifier &&*/
             diagram()->spaceKeyPressed())
        {
            setCursor(Qt::SizeHorCursor);
        }
        else
        {
            setCursor(Qt::SizeAllCursor);
        }

        setDrawTerminal(false);
        addRegionRect = true;
        disableConductorHighLight(true);

        mIsMoving = true;
    }
    else
    {
        setCursor(Qt::ArrowCursor);
        setDrawTerminal(true);
    }

    if (auto parElmt =parentElement())
    {
        if (parElmt->mouseHover())
        {
            parElmt->showHover(false);
            mParentHover = true;
        }
        else
        {
            mParentHover = false;
        }
    }

    Element::hoverEnterEvent(event);
}

void ContactorElement::hoverLeaveEvent(QGraphicsSceneHoverEvent *event)
{
    // 交换点位修改为鼠标直接拖动交换
    if (event->modifiers() == Qt::ControlModifier)
    {
        setCursor(Qt::ArrowCursor);
        setDrawTerminal(true);
        addRegionRect = false;
        disableConductorHighLight(false);

        mIsMoving = false;
    }

    if (mParentHover)
    {
        if (auto parElmt =parentElement())
        {
            parElmt->showHover(true);
        }
    }

    Element::hoverLeaveEvent(event);
}

void ContactorElement::setPointName(const QString &name)
{
    if (textItem())
    {
        textItem()->setPlainText(name);
    }
}

ContactorElement::SignalInfo ContactorElement::signalInfo()
{
    return mSignalInfo;
}

void ContactorElement::setSignalInfo(SignalInfo info)
{
    mSignalInfo.SignalName = info.SignalName;
    mSignalInfo.SignalCategory = info.SignalCategory;
}

void ContactorElement::adjustTextToVisualPosition()
{
    textItem()->setRotationToVisual();
    adjustTextPos();
}

void ContactorElement::adjustTextPos()
{
    if (!textItem())
        return;

    // 1. 获取文本的宽度
    QSizeF textSize = textItem()->boundingRect().size();

    Qet::Orientation textOri = Qet::getOrientationByRotation(textItem()->rotation());
    QPointF pos = textItem()->pos();
    if (Qet::North == textOri)
    {
        pos = QPointF(-textSize.width() / 2, mTextOffset);
    }
    else if (Qet::East == textOri)
    {
        pos = QPointF( textSize.height() / 2, mTextOffset);
    }
    else if (Qet::South == textOri)
    {
        pos = QPointF(textSize.width() / 2, mTextOffset + textSize.height());
    }
    else if (Qet::West == textOri)
    {
        pos = QPointF(-textSize.height() / 2, mTextOffset + textSize.width());
    }

    textItem()->setPos(pos);
}

void ContactorElement::setUserMovable(bool movable)
{
    mUserMovable = movable;
}

void ContactorElement::onSwitchPointKeyRelease(QKeyEvent *event)
{
    // qDebug() << Q_FUNC_INFO << event->modifiers() << (Qt::Key)event->key() << mMoveEvInterface << this;

    if (mMoveEvInterface != nullptr)
    {
        delete mMoveEvInterface;
        mMoveEvInterface = nullptr;

        // 删除事件 时取消选中状态
        setCursor(Qt::ArrowCursor);
        diagram()->views().first()->viewport()->setCursor(Qt::ArrowCursor);
        setDrawTerminal(true);
        addRegionRect = false;
        disableConductorHighLight(false);

        mIsMoving = false;
    }

    QPointF cursorPos = diagram()->views().first()->viewport()->mapFromGlobal(cursor().pos());
    cursorPos = diagram()->views().first()->mapToScene(cursorPos.toPoint());
    cursorPos = mapFromScene(cursorPos);
    QRectF rec = this->boundingRect();

    if (event->key() == Qt::Key_Control || !rec.contains(cursorPos))
    {
        setCursor(Qt::ArrowCursor);
        diagram()->views().first()->viewport()->setCursor(Qt::ArrowCursor);
        setDrawTerminal(true);
        addRegionRect = false;
        disableConductorHighLight(false);

        mIsMoving = false;
    }
}

void ContactorElement::onSwitchPointKeyPressed(QKeyEvent *event)
{
    // 元素中端子处于连线状态时，禁止交换点位
    if (!this->terminals().isEmpty() &&
            this->terminals().first()->getLinkState())
        return;

    // qDebug() << event->modifiers() << (Qt::Key)event->key();
    if (event->key() == Qt::Key_Control &&
            event->modifiers() == Qt::ControlModifier)
    {
        QPointF cursorPos = diagram()->views().first()->viewport()->mapFromGlobal(cursor().pos());
        cursorPos = diagram()->views().first()->mapToScene(cursorPos.toPoint());
        cursorPos = mapFromScene(cursorPos);
        QRectF rec = this->boundingRect();

        if (!rec.contains(cursorPos))
        {
            return;
        }

        // qDebug() << Q_FUNC_INFO  << "鼠标在元素内" << this;

        // 按下空格时显示拉伸连接器长度的抓手
        if (diagram()->spaceKeyPressed())
        {
            // if (event->modifiers() != Qt::ControlModifier)
            //     return;

            setCursor(Qt::SizeHorCursor);
        }
        else
        {
            setCursor(Qt::SizeAllCursor);
        }

        setDrawTerminal(false);
        addRegionRect = true;
        disableConductorHighLight(true);

        mIsMoving = true;

    }
    else
    {
        if (mMoveEvInterface)
        {
            delete mMoveEvInterface;
            mMoveEvInterface = nullptr;
        }
    }
}

void ContactorElement::setDrawTerminal(bool draw)
{
    if (!terminals().isEmpty())
    {
        for (auto ter : terminals())
        {
            ter->setDrawTermianl(draw);
            update();
        }
    }
}

void ContactorElement::mouseDoubleClickEvent(QGraphicsSceneMouseEvent *event)
{
    // 电缆信息连接图上不编辑属性
    if (parentItem() &&  (parentElement()->adeType() == ConnectorWiringElement::ADEType) )
        return;

    doubleClickEvent(event);
}

bool ContactorElement::sceneEventFilter(QGraphicsItem *watched, QEvent *event)
{
    if (!mTextEventFilterInstalled)
        return false;

    if (watched->type() == DynamicElementTextItem::Type)
    {
        DynamicElementTextItem *qghi = qgraphicsitem_cast<DynamicElementTextItem *>(watched);

        if (qghi == nullptr)
            return false;

        if (event->type() == QEvent::GraphicsSceneMousePress)
        {
            pressEvent(static_cast<QGraphicsSceneMouseEvent *>(event));
            return true;
        }
        else if (event->type() == QEvent::GraphicsSceneMouseMove)
        {
            moveEvent(static_cast<QGraphicsSceneMouseEvent *>(event));
            return true;
        }
        else if (event->type() == QEvent::GraphicsSceneMouseRelease)
        {
            releaseEvent(static_cast<QGraphicsSceneMouseEvent *>(event));
            return true;
        }
        else if (event->type() == QEvent::GraphicsSceneMouseDoubleClick)
        {
            doubleClickEvent(static_cast<QGraphicsSceneMouseEvent *>(event));
            return true;
        }
    }
    else if (watched->type() == Terminal::Type)
    {
        Terminal *t = qgraphicsitem_cast<Terminal *>(watched);
        // 当端子禁用时将鼠标事件传递到接触件元素
        if (!t->isEnabled())
        {
            if (event->type() == QEvent::GraphicsSceneMousePress)
            {
                QGraphicsSceneMouseEvent *mouseEvent = static_cast<QGraphicsSceneMouseEvent *>(event);
                // 处理父 item 的鼠标事件
                this->mousePressEvent(mouseEvent);
                return true; // 事件已处理
            }
        }
    }

    return false;
}

void ContactorElement::paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget)
{
    Element::paint(painter, option, widget);

    if (addRegionRect)
    {
        // 芯线连接图接壳不显示可交换点位高亮矩形
        if (parentElement())
        {
            if (parentElement()->adeType() == ConnectorWiringElement::ADEType
                    && mFunctionType == Enclosure)
                return;
        }

        painter->save();
        QPen pen(Qt::NoBrush, 0, Qt::DashLine);
        pen.setColor(Qt::black);
        pen.setCosmetic(true);
        pen.setDashOffset(1);

        QBrush brush;
        brush.setStyle(Qt::SolidPattern);

        QColor brushColor;
        brushColor = QColor(90, 167, 255, 64);
        brush.setColor(brushColor);
        painter->setPen(pen);
        painter->setBrush(brush);

        QPainterPath mPath;
        mPath.addRect(this->boundingRect());
        painter->drawPath(mPath);
        painter->restore();
    }
}

void ContactorElement::pressEvent(QGraphicsSceneMouseEvent *event)
{
    // 端口不可移动时将事件传递给父对象
    if (!isMovable())
    {
        return;
    }

    // 鼠标右键不做操作
    if (event->buttons() & Qt::RightButton)
        return;

    //当点位映射图只读时不做操作
    if (diagram()->ade()->diagramType() == ADEDiagram::DTCablePointsMapping && diagram()->isContentReadOnly())
    {
        return;
    }

    if (event->modifiers() != Qt::ControlModifier)
        return;

    if (diagram()->spaceKeyPressed())
    {
        // 点位映射图禁用
        if (diagram()->ade()->diagramType() == ADEDiagram::DTCablePointsMapping)
            return;

        // if (event->modifiers() != Qt::ControlModifier)
        //     return;

        setUserMovable(true);
        mPusher = new ContactorPusher(this);
        mPusher->beginPush();
        diagram()->setItemHandleMouseEventState(true);
    }
    else
    {
        if (mFunctionType == ContactorElement::Enclosure)
            return;

        setUserMovable(true);
        if (mUserMovable)
        {
            if (mMoveEvInterface != nullptr)
            {
                delete mMoveEvInterface;
                mMoveEvInterface = nullptr;
            }

            if (mMoveEvInterface == nullptr)
            {
                // if (diagram()->ade()->diagramType() == ADEDiagram::DTCablePointsMapping)
                //     setDrawTerminal(false);

                // addRegionRect = true;
                // disableConductorHighLight(true);
                // mIsMoving = true;

                mMoveEvInterface = new ContactorMoveEventInterface(this);
            }

            mMoveEvInterface->mousePressEvent(event);
            diagram()->setItemHandleMouseEventState(true);
        }
    }

    Element::mousePressEvent(event);
}

void ContactorElement::moveEvent(QGraphicsSceneMouseEvent *event)
{
    if (!isMovable() || !mUserMovable)
    {
        return;
    }

    if (event->modifiers() != Qt::ControlModifier)
        return;

    // 鼠标左键进入交换位置
    if (!(event->buttons() & Qt::LeftButton))
        return;

    if (mPusher)
    {
        mPusher->continuePush(event->scenePos().x());
    }
    else if (mMoveEvInterface)
    {
        mMoveEvInterface->mouseMoveEvent(event);

        // 更新导线路径
        for (Conductor* c : conductors())
            c->updatePath();
    }
}

void ContactorElement::releaseEvent(QGraphicsSceneMouseEvent *event)
{
    if (!isMovable())
    {
        return;
    }

    setUserMovable(true);

    // 鼠标左键使元素移动时才执行后续操作
    if (mUserMovable)
    {
        if (mPusher)
        {
            mPusher->endPush();
            delete mPusher;
            mPusher = nullptr;
            diagram()->setItemHandleMouseEventState(false);
        }
        else if(mMoveEvInterface != nullptr)
        {
            if (event->button() != Qt::LeftButton)
                return;

            // if (event->modifiers() != Qt::ControlModifier)
            //     return;
            mMoveEvInterface->mouseReleaseEvent(event);

            delete mMoveEvInterface;
            mMoveEvInterface = nullptr;

            //
            // if (diagram()->ade()->diagramType() == ADEDiagram::DTCablePointsMapping)
            //     setDrawTerminal(true);

            // addRegionRect = false;
            // disableConductorHighLight(false);
            // mIsMoving = false;

            diagram()->setItemHandleMouseEventState(false);
        }
    }

    setUserMovable(false);

    QPointF eventPos = this->mapFromScene(event->scenePos());
    if (!this->boundingRect().contains(eventPos))
    {
        diagram()->views().first()->viewport()->setCursor(Qt::ArrowCursor);
        setDrawTerminal(true);
        addRegionRect = false;
        disableConductorHighLight(false);

        mIsMoving = false;
    }

    update();
}

bool ContactorElement::isMoving()
{
    return mIsMoving;
}

void ContactorElement::doubleClickEvent(QGraphicsSceneMouseEvent *event)
{
    if (terminals().isEmpty())
        return;

    if (event->buttons() & Qt::RightButton)
        return;

    // 芯线连接图禁用
    if (diagram()->ade()->diagramType() != ADEDiagram::DTCablePointsMapping)
        return;

    QPointF cursorPos = diagram()->views().first()->viewport()->mapFromGlobal(cursor().pos());
    cursorPos = diagram()->views().first()->mapToScene(cursorPos.toPoint());
    cursorPos = mapFromScene(cursorPos);
    QRectF rec = this->boundingRect();
    QRectF textRect = this->textItem()->boundingRect();

    // 文本双击范围缩小到0.7
    textRect = this->textItem()->mapRectToScene(textRect);
    qreal width = textRect.width() * 0.7;
    qreal height = textRect.height() * 0.7;
    qreal centerX = textRect.center().x();
    qreal centerY = textRect.center().y();
    qreal newX = centerX - width / 2.0;
    qreal newY = centerY - height / 2.0;
    // 设置新的矩形大小和位置
    textRect.setRect(newX, newY, width, height);

    if (!rec.contains(cursorPos) &&
            textRect.contains(cursorPos))
        return;

    if (diagram()->ade())
    {
        diagram()->ade()->editSignalName(this);
    }
}

void ContactorElement::disableConductorHighLight(bool disable)
{
    for (auto cd : this->terminals().first()->conductors())
    {
        if (disable)
        {
            mOriginalConductorZValue = cd->zValue();
            cd->setZValue(0);
        }
        else
        {
            cd->setZValue(mOriginalConductorZValue);
        }
    }
}

QVariant ContactorElement::itemChange(GraphicsItemChange change, const QVariant &value)
{
    if (change == ItemSceneHasChanged)
    {
        if (scene() && !mTextEventFilterInstalled)
        {
            textItem()->installSceneEventFilter(this);
            mTextEventFilterInstalled = true;
        }

        if (scene())
        {
            terminal()->installSceneEventFilter(this);
        }
    }
    else if (change == ItemScenePositionHasChanged)
    {
        //qDebug() << "ItemScenePositionHasChanged";
    }

    return Element::itemChange(change, value);
}

QString ContactorElement::pointName()
{
    return textItem()->toPlainText();
}
