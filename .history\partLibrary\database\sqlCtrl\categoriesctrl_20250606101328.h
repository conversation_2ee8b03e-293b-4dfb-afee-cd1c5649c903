#ifndef CATEGORIESCTRL_H
#define CATEGORIESCTRL_H

#include "sqlCtrlBase.h"
#include "../sqlModel/categoriesmodel.h"
#include <QSharedPointer>
#include <QList>

class CategoriesCtrl : public SqlCtrlBase
{
    Q_OBJECT
public:
    explicit CategoriesCtrl(const QString &dbPath, QObject *parent = nullptr);

    bool checkAndCreateTable() override;

    bool insert(const CategoriesModel &model);
    bool update(const CategoriesModel &model);
    bool softDelete(const QString &uuid);
    QSharedPointer<CategoriesModel> getByUUID(const QString &uuid);
    QList<QSharedPointer<CategoriesModel>> getAll();
    QList<QSharedPointer<CategoriesModel>> getAllActive();
};

#endif // CATEGORIESCTRL_H 