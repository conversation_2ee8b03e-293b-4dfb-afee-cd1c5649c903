#ifndef ATTRIBUTEDEFINITIONSCTRL_H
#define ATTRIBUTEDEFINITIONSCTRL_H

#include "sqlCtrlBase.h"
#include "../sqlModel/attributedefinitionsmodel.h"

//============================================================================
/// file
/// brief      属性定义表控制器
/// author     wm
/// version    1.0
/// date       2025/06/05
/// todo
//============================================================================

class AttributeDefinitionsCtrl : public SqlCtrlBase
{
public:
    AttributeDefinitionsCtrl(const QSqlDatabase& db);
    ~AttributeDefinitionsCtrl();

    // 针对数据模型的操作
    AttributeDefinitionsModelList fetchAll() const;
    AttributeDefinitionsModelPtr  fetch(const QString &id) const;
    bool modelExist(const QString &id) const;
    // end

    // 针对数据库表的操作
    bool insert(const AttributeDefinitionsModelPtr &obj);
    bool modify(const AttributeDefinitionsModelPtr &newValueObj);
    bool remove(const QString &id);
    // end

public:
    QString tableName()       const override {return "AttributeDefinitions";}
    QString primaryKeyField() const override {return "AttributeUUID";}
    bool    recordExist(const QString &id) const override;

private:
    QString tableSql()        const override;
    void loadTable();

private:
    AttributeDefinitionsModelList mAllObj;
};

#define AttributeDefinitions_Factory objFactory<QString, AttributeDefinitionsCtrl>::getInstance()

#endif // ATTRIBUTEDEFINITIONSCTRL_H 