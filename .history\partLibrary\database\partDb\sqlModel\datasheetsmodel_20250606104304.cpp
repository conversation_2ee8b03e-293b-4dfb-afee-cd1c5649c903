#include "datasheetsmodel.h"

// 字段名称定义
const QString DatasheetsModel::FIELD_ID = "DatasheetUUID";
const QString DatasheetsModel::FIELD_TITLE = "Title";
const QString DatasheetsModel::FIELD_FILE_PATH = "FilePath";
const QString DatasheetsModel::FIELD_MD5_HASH = "MD5_Hash";
const QString DatasheetsModel::FIELD_REVISION = "Revision";
const QString DatasheetsModel::FIELD_UPLOAD_TIMESTAMP = "UploadTimestamp";
const QString DatasheetsModel::FIELD_IS_DELETED = "IsDeleted";

QVariantMap DatasheetsModel::modelToDbMap() const
{
    QVariantMap map;
    map.insert(FIELD_ID, id);
    map.insert(FIELD_TITLE, title);
    map.insert(FIELD_FILE_PATH, filePath);
    map.insert(FIELD_MD5_HASH, md5Hash);
    map.insert(FIELD_REVISION, revision);
    map.insert(FIELD_UPLOAD_TIMESTAMP, uploadTimestamp);
    return map;
}

void DatasheetsModel::dbToModel(const QSqlRecord &record)
{
    QMap<QString, QVariant> infos = getRecordValues(record);

    if (infos.contains(FIELD_ID)) this->id = infos[FIELD_ID].toString();
    if (infos.contains(FIELD_TITLE)) this->title = infos[FIELD_TITLE].toString();
    if (infos.contains(FIELD_FILE_PATH)) this->filePath = infos[FIELD_FILE_PATH].toString();
    if (infos.contains(FIELD_MD5_HASH)) this->md5Hash = infos[FIELD_MD5_HASH].toString();
    if (infos.contains(FIELD_REVISION)) this->revision = infos[FIELD_REVISION].toString();
    if (infos.contains(FIELD_UPLOAD_TIMESTAMP)) this->uploadTimestamp = infos[FIELD_UPLOAD_TIMESTAMP].toDateTime();
} 