#include "contactormoveeventinterface.h"

#include <QGraphicsScene>
#include <QCursor>
#include <QGraphicsSceneMouseEvent>

#include "3rdParty/logBusiness/logbusiness.h"
#include "./CustomElements/interfacemodelelement.h"
#include "./CustomElements/connectorwiringelement.h"

#include "3rdParty/qet/sources/qetgraphicsitem/dynamicelementtextitem.h"
#include "../diagram.h"
#include "3rdParty/qet/sources/undocommand/customCommand/customswapcontactorelement.h"
#include "3rdParty/qet/sources/qetgraphicsitem/terminal.h"
#include "3rdParty/qet/sources/qetgraphicsitem/conductor.h"

#include "3rdParty/qet/sources/qetgraphicsitem/customconductor.h"
#include "layoutDiagram/wiringdiagramlayouter.h"
#include "3rdParty/qet/sources/CustomExtend/conductorpathupdater/wiringconductorupdater.h"

#include <QGraphicsRectItem>

ContactorMoveEventInterface::ContactorMoveEventInterface(ContactorElement *ctEle) :
    mContactorEle(ctEle),
    mSnapRange(5.0)
{
    // 交换点位吸附范围
    if (mContactorEle->parentElement())
    {
        QPointF topLeft;
        topLeft = mContactorEle->parentElement()->boundingRect().topLeft();
        mUsefulRange = QRectF(topLeft, QSizeF(mContactorEle->parentElement()->boundingRect().width(),
                                              mContactorEle->parentElement()->boundingRect().height() * 2));
        mUsefulRange = mContactorEle->parentElement()->mapRectToScene(mUsefulRange);

        // 比较连接器模型原本左上角 与 boundingRect在scene上的左上角
        QPointF originalTopleft = mContactorEle->parentElement()->mapToScene(topLeft);
        QRectF sceneBoundingRect = mContactorEle->parentElement()->mapRectToScene(mContactorEle->parentElement()->boundingRect());
        QPointF contactorScenePos = mContactorEle->scenePos();

        // 增加端子朝向判断模型的朝向
        if (mContactorEle->terminal()->orientation() == Qet::North ||
                mContactorEle->terminal()->orientation() == Qet::South)
        {
            if (sceneBoundingRect.width() == mContactorEle->parentElement()->boundingRect().width())
            {
                // y坐标相近，向上下平移70，y坐标不同不处理
                if (std::abs(originalTopleft.y() - contactorScenePos.y()) < 20)
                {
                    // 比较mUsefulRange 与 连接器模型原左上角坐标， y坐标相等向上平移，反之向下
                    if (std::abs(originalTopleft.y() - mUsefulRange.topLeft().y()) > 0)
                    {
                        QPointF offset(0, mContactorEle->parentElement()->boundingRect().height());
                        mUsefulRange.translate(offset);
                    }
                    else
                    {
                        QPointF offset(0, -mContactorEle->parentElement()->boundingRect().height());
                        mUsefulRange.translate(offset);
                    }
                }
            }
        }
        else if (mContactorEle->terminal()->orientation() == Qet::West ||
                 mContactorEle->terminal()->orientation() == Qet::East)
        {
            if (sceneBoundingRect.height() == mContactorEle->parentElement()->boundingRect().width())
            {
                // x坐标相近，向左右平移70，x坐标不同不处理
                if (std::abs(originalTopleft.x() - contactorScenePos.x()) < 20)
                {
                    // 比较mUsefulRange 与 连接器模型原左上角坐标， x坐标相等向左平移，反之向右
                    if (std::abs(originalTopleft.x() - mUsefulRange.topLeft().x()) > 0)
                    {
                        QPointF offset(mContactorEle->parentElement()->boundingRect().height(), 0);
                        mUsefulRange.translate(offset);
                    }
                    else
                    {
                        QPointF offset(-mContactorEle->parentElement()->boundingRect().height(), 0);
                        mUsefulRange.translate(offset);
                    }
                }
            }
        }
    }

    updateExistElementPos();
    recordPointPosToNameBeforeSwitch();

    //显示可交换的点位
    if (mSnapPointsPos.isEmpty())
        return;

    for (auto p : mSnapPointsPos)
    {
        ContactorPortSnapPointItem *spi = new ContactorPortSnapPointItem(mContactorEle->parentElement());
        spi->setPos(mContactorEle->parentElement()->mapFromScene(p));
        mSnapPointItems << spi;
    }

    hideContactorMovedSnapItem();

}

ContactorMoveEventInterface::~ContactorMoveEventInterface()
{
    for (auto spi : mSnapPointItems)
        mContactorEle->scene()->removeItem(spi);

    qDeleteAll(mSnapPointItems);
    mSnapPointItems.clear();

    mContactorEle->diagram()->removeItem(mHighLightRect);
    mHighLightRect->deleteLater();

    mContactorEle->diagram()->removeItem(mDisableContactorRect);
    mDisableContactorRect->deleteLater();

    if (!mPreviewLinkLines.isEmpty())
    {
        for (auto line : mPreviewLinkLines)
            mContactorEle->diagram()->removeItem(line);
    }
    qDeleteAll(mPreviewLinkLines);
    mPreviewLinkLines.clear();

    if (!mPreviewLinkConductors.isEmpty())
    {
        for (CustomConductor* line : mPreviewLinkConductors)
            mContactorEle->diagram()->removeItem(line);
    }
    qDeleteAll(mPreviewLinkConductors);
    mPreviewLinkConductors.clear();

    mContactorEle->diagram()->removeItem(mDisableContactorEle);
    mDisableContactorEle->deleteLater();
}

void ContactorMoveEventInterface::mousePressEvent(QGraphicsSceneMouseEvent *event)
{
    mCurrentPos = mContactorEle->scenePos();
    mContactorEle->setCursor(QCursor(Qt::PointingHandCursor));

#ifdef QT_DEBUG
    // 显示有效吸附范围
    // if (mContactorEle->diagram())
    // {
    //     QGraphicsRectItem* rect = new QGraphicsRectItem(mUsefulRange);
    //     mContactorEle->diagram()->addItem(rect);

    //     QGraphicsEllipseItem* item = new QGraphicsEllipseItem;
    //     item->setRect(0, 0, 20, 20);
    //     item->setPen(QPen(Qt::red));

    //     QPointF topLeft = mContactorEle->parentElement()->boundingRect().topLeft();
    //     item->setPos(mContactorEle->parentElement()->mapToScene(topLeft));
    //     mContactorEle->diagram()->addItem(item);
    // }
#endif

    // 跟随鼠标点位
    mDisableContactorEle = CreateAdeElement::getInstance()->buildContactorEle(
                mContactorEle->diagram()->project(), nullptr, QString(""), mContactorEle->contactorType(),
                mContactorEle->textItem()->text());
    mDisableContactorEle->setEnabled(false);
    mDisableContactorEle->setVisible(false);
    mDisableContactorEle->setRotation((mContactorEle->parentElement()->rotation()) - (mContactorEle->rotation()));
    mDisableContactorEle->terminal()->setDrawTermianl(mContactorEle->terminal()->drawTermianl());
    mDisableContactorEle->adjustTextToVisualPosition();
    mContactorEle->diagram()->addItem(mDisableContactorEle);
    mDisableContactorEle->setPos(mContactorEle->scenePos(), false);

    QPainterPath mPath;
    mPath.addRect(mDisableContactorEle->boundingRect());
    mDisableContactorRect = new RegionRectItem(mPath);
    mDisableContactorRect->setType(RegionRectItem::Error);
    mContactorEle->diagram()->addItem(mDisableContactorRect);
    mDisableContactorRect->setRotation(mContactorEle->parentElement()->rotation());
    mDisableContactorRect->setVisible(false);

    // 吸附连线预览
    if (!mContactorEle->terminal()->conductors().isEmpty())
    {
        if (mContactorEle->diagram()->ade()->diagramType() == ADEDiagram::DTCableWiring)
        {
            for (auto cdt : mContactorEle->terminal()->conductors())
            {
                if (cdt->lineType() == CustomConductor::LineType)
                {
                    CustomConductor* conductor = dynamic_cast<CustomConductor*>(cdt);

                    // 判断原导线的端子顺序,预览导线与原导线端子顺序保持一致
                    Terminal* t1 = nullptr; Terminal* t2 = nullptr;
                    if (mContactorEle->terminal() == conductor->terminal1)
                    {
                        t1 = mDisableContactorEle->terminal();
                        t2 = conductor->terminal2;
                    }
                    else if (mContactorEle->terminal() == conductor->terminal2)
                    {
                        t1 = conductor->terminal1;
                        t2 = mDisableContactorEle->terminal();
                    }

                    if (!t1 && !t2)
                        continue;

                    CustomConductor* previewCdt = new CustomConductor(t1, t2);
                    QPen pen(Qt::NoBrush, 1.5, Qt::DashLine);
                    pen.setColor(QColor(0, 255, 0, 200));
                    previewCdt->setPen(pen);
                    previewCdt->setZValue(1000000);
                    mContactorEle->diagram()->addItem(previewCdt);
                    mPreviewLinkConductors.append(previewCdt);

                    copyMoveContactorEleConductorsPath(conductor, previewCdt);
                }
            }
        }
        else if (mContactorEle->diagram()->ade()->diagramType() == ADEDiagram::DTCablePointsMapping)
        {
            // 记录起始点坐标
            QList<QPointF> beginPos;
            for (auto cdt : mContactorEle->terminal()->conductors())
            {
                if (cdt->terminal1 == mContactorEle->terminal())
                    beginPos.append(cdt->terminal2->scenePos());
                else
                    beginPos.append(cdt->terminal1->scenePos());
            }

            if (!beginPos.isEmpty())
            {
                for (QPointF pos : beginPos)
                {
                    QPen pen(Qt::NoBrush, 1.5, Qt::DashLine);
                    pen.setColor(QColor(0, 255, 0, 200));
                    QGraphicsLineItem *conductor_setter_ = new QGraphicsLineItem(nullptr);
                    conductor_setter_ ->setZValue(1000000);
                    conductor_setter_->setPen(pen);

                    conductor_setter_ -> setLine(QLineF(pos, event->scenePos()));
                    mContactorEle->diagram()->addItem(conductor_setter_);
                    mPreviewLinkLines.append(conductor_setter_);
                }
            }
        }
    }
}

void ContactorMoveEventInterface::mouseMoveEvent(QGraphicsSceneMouseEvent *event)
{
    if (!endSwap)
        return;

    endSwap = false;
    QPointF scenePos = event->scenePos();

    mDistInfo.clear();
    mDistEmptyPoints.clear();

    if (!mUsefulRange.contains(scenePos))
    {
        // 鼠标位置显示红色浮动点位
        if (mDisableContactorEle)
        {
            mDisableContactorEle->setVisible(true);
            mDisableContactorEle->setPos(scenePos, false);
        }

        if (mDisableContactorRect)
        {
            mDisableContactorRect->setVisible(true);
            mDisableContactorRect->setPos(scenePos);
        }

        mLastSwitchPointPos = scenePos;
        mLastSwitchPointElement = nullptr;

        if (mHighLightRect)
        {
            mContactorEle->diagram()->removeItem(mHighLightRect);
            delete mHighLightRect;
            mHighLightRect = nullptr;
        }
        hideContactorMovedSnapItem();

        // 连线预览
        if (!mPreviewLinkLines.isEmpty())
        {
            QPen pen(Qt::NoBrush, 1.5, Qt::DashLine);
            pen.setColor(QColor(255, 0, 0, 200));

            if (mContactorEle->diagram()->ade()->diagramType() == ADEDiagram::DTCablePointsMapping)
            {
                for (auto line : mPreviewLinkLines)
                {
                    QGraphicsLineItem* lineItem = dynamic_cast<QGraphicsLineItem*>(line);
                    lineItem->setVisible(true);
                    lineItem->setPen(pen);
                    lineItem -> setLine(QLineF(lineItem -> line().p1(), event->scenePos()));
                }
            }
        }

        if (!mPreviewLinkConductors.isEmpty())
        {
            QPen pen(Qt::NoBrush, 1.5, Qt::DashLine);
            pen.setColor(QColor(255, 0, 0, 200));

            if (mContactorEle->diagram()->ade()->diagramType() == ADEDiagram::DTCableWiring)
            {
                for (CustomConductor* line : mPreviewLinkConductors)
                {
                    line->setVisible(true);
                    line->setPen(pen);
                }
            }
        }
    }
    else
    {
        // 隐藏鼠标位置显示红色浮动点位
        if (mDisableContactorEle->isVisible())
            mDisableContactorEle->setVisible(false);

        if (mDisableContactorRect->isVisible())
            mDisableContactorRect->setVisible(false);

        // 在吸附点位内进行查找
        for (auto snapPos : mSnapPointsPos)
        {
            // if (snapPos == mCurrentPos)
            //     continue;

            qreal dist = QET::calcDistanceBetweenPoints(scenePos, snapPos);
            // if (dist <= mSnapRange)
            {
                auto cttElmt = getContactorElementByPos(snapPos);
                if (cttElmt)
                {
                    mDistInfo.insert(cttElmt, dist);
                }
                else
                {
                    mDistEmptyPoints.insert(dist, snapPos);
                }
            }
        }

        // for (auto ele : mDistInfo.keys())
        // {
        //     if (ele->scenePos() == mCurrentPos)
        //         mDistInfo.remove(ele);
        // }

        if (mDistInfo.isEmpty() && mDistEmptyPoints.isEmpty())
        {
            mLastSwitchPointPos = QPointF();
            mLastSwitchPointElement = nullptr;

            if (mHighLightRect)
            {
                mContactorEle->diagram()->removeItem(mHighLightRect);
                delete mHighLightRect;
                mHighLightRect = nullptr;
            }
            hideContactorMovedSnapItem();
        }
        else
        {
            Element *e;          //被交换点位
            QPointF emptyPoint;  //被交换点位坐标
            if (!mDistInfo.isEmpty())
            {
                QList<qreal> distList = mDistInfo.values();
                qSort(distList);
                e = mDistInfo.key(distList.first());
                emptyPoint = e->scenePos();

                // 非空点位与空点位比较
                if (!mDistEmptyPoints.isEmpty())
                {
                    QList<qreal> distListEmptyPoint;
                    for (qreal dist : mDistEmptyPoints.keys())
                    {
                        distListEmptyPoint << dist;
                    }
                    qSort(distListEmptyPoint);

                    if (distList.first() > distListEmptyPoint.first())
                    {
                        emptyPoint = mDistEmptyPoints.value(distListEmptyPoint.first());
                        e = nullptr;
                    }
                }

                // 连线预览
                if (!mPreviewLinkLines.isEmpty())
                {
                    QPen pen(Qt::NoBrush, 1.5, Qt::DashLine);
                    pen.setColor(QColor(0, 255, 0, 200));
                    if (mContactorEle->diagram()->ade()->diagramType() == ADEDiagram::DTCablePointsMapping)
                    {
                        for (QGraphicsItem* line : mPreviewLinkLines)
                        {
                            QGraphicsLineItem* lineItem = dynamic_cast<QGraphicsLineItem*>(line);
                            lineItem->setVisible(true);
                            lineItem->setPen(pen);
                            lineItem -> setLine(QLineF(lineItem -> line().p1(), emptyPoint));
                        }
                    }
                }

                if (!mPreviewLinkConductors.isEmpty())
                {
                    QPen pen(Qt::NoBrush, 1.5, Qt::DashLine);
                    pen.setColor(QColor(0, 255, 0, 200));

                    if (mContactorEle->diagram()->ade()->diagramType() == ADEDiagram::DTCableWiring)
                    {
                        mDisableContactorEle->setPos(emptyPoint, false);
                        for (CustomConductor* line : mPreviewLinkConductors)
                        {
                            line->setVisible(true);
                            line->setPen(pen);
                        }
                    }
                }
            }

            if (emptyPoint != mCurrentPos)
            {
                mLastSwitchPointPos = emptyPoint;
                mLastSwitchPointElement = e;

                // 移动交换-随动功能
                // emptyPoint = mContactorEle->parentItem()->mapFromScene(emptyPoint);
                // swapContactorElmtPos(mContactorEle, e, emptyPoint, false);
                // mCurrentPos = mContactorEle->scenePos();
                // updateExistElementPos();

                // 被移动的点位高亮
                if (!mHighLightRect)
                {
                    QPainterPath mPath;
                    mPath.addRect(mContactorEle->boundingRect());
                    mHighLightRect = new RegionRectItem(mPath);
                    mHighLightRect->setType(RegionRectItem::Correct);

                    mContactorEle->diagram()->addItem(mHighLightRect);
                }

                mHighLightRect->setPos(mLastSwitchPointPos);
                mHighLightRect->setRotation(mContactorEle->parentElement()->rotation());

                hideContactorMovedSnapItem();
            }
            else
            {
                // 鼠标移动到自身点位，不显示交换效果
                mLastSwitchPointPos = QPointF();
                mLastSwitchPointElement = nullptr;

                if (mHighLightRect)
                {
                    mContactorEle->diagram()->removeItem(mHighLightRect);
                    delete mHighLightRect;
                    mHighLightRect = nullptr;
                }
                hideContactorMovedSnapItem();

                // 隐藏预览连线
                if (!mPreviewLinkLines.isEmpty())
                {
                    for (QGraphicsItem* line : mPreviewLinkLines)
                        line->setVisible(false);
                }
                if (!mPreviewLinkConductors.isEmpty())
                {
                    for (CustomConductor* line : mPreviewLinkConductors)
                        line->setVisible(false);
                }
            }
        }
    }

    endSwap = true;
}

void ContactorMoveEventInterface::mouseReleaseEvent(QGraphicsSceneMouseEvent *event)
{
    // 连线预览
    if (!mPreviewLinkLines.isEmpty())
    {
        for (auto line : mPreviewLinkLines)
            mContactorEle->diagram()->removeItem(line);
    }
    if (!mPreviewLinkConductors.isEmpty())
    {
        for (CustomConductor* line : mPreviewLinkConductors)
            mContactorEle->diagram()->removeItem(line);
    }

    if (mDisableContactorEle)
    {
        // delete mDisableContactorEle;
        // mDisableContactorEle = nullptr;

        mDisableContactorEle->setVisible(false);
    }

    if (mDisableContactorRect)
    {
        // delete mDisableContactorRect;
        // mDisableContactorRect = nullptr;

        mDisableContactorRect->setVisible(false);
    }

    if (mPointPosRecord.keys().contains(mLastSwitchPointPos))
    {
        mLastSwitchPointPos = mContactorEle->parentItem()->mapFromScene(mLastSwitchPointPos);
        swapContactorElmtPos(mContactorEle, mLastSwitchPointElement, mLastSwitchPointPos, true);
    }

    mContactorEle->showHint(Element::HintNone);
    mContactorEle->setCursor(QCursor(Qt::SizeAllCursor));

    // 被移动点位取消选中
    if (mContactorEle->isSelected())
        mContactorEle->setSelected(false);
}

void ContactorMoveEventInterface::swapContactorElmtPos(Element *e1, Element *e2, QPointF e2Pos, bool lastSwitch)
{
    if (e1 == nullptr)
        return;

    if (!e1->diagram())
        return;

    if (lastSwitch)
    {
        // 在交换接触件位置过程中可能会新增顶点, 记录新增的顶点，在当前操作撤销时移除这些顶点
        auto d = e1->diagram();
        QList<ConductorVertexItem * > newAddedVertexes;
        for (auto cc : d->customConductors())
        {
            for (auto v : cc->vertexList())
            {
                if (!mVertexInitialPos.contains(v))
                    newAddedVertexes << v;
            }
        }

        d->undoStack().push(new CustomSwapContactorElement(e1, e2, e2Pos));
    }
    else
    {
        QPointF elePos1 = e1->pos();
        QPointF elePos2;
        if (e2)    elePos2 = e2->pos();
        else       elePos2 = e2Pos;

        e1->setPos(e2Pos, false);

        if (e2 != nullptr)
            e2->setPos(elePos1, false);
    }
}

ContactorElement *ContactorMoveEventInterface::getContactorElementByPos(const QPointF &pos) const
{
    for (auto pair : mAllExistPointItems)
    {
       if (pair.first == pos)
           return pair.second;
    }

    return nullptr;
}

void ContactorMoveEventInterface::updateExistElementPos()
{
    mSnapPointsPos.clear();
    mAllExistPointItems.clear();

    if (mContactorEle->parentElement())
    {
        //获取父元素所有的点位元素的点位
        if (mContactorEle->parentElement()->adeType() == ConnectorWiringElement::ADEType)
        {
            ConnectorWiringElement* parentEle = static_cast<ConnectorWiringElement*>(mContactorEle->parentElement());
            QList<QPoint> ctSlots = parentEle->contactorSlots();

            ctSlots.removeOne(parentEle->enclosureElement()->pos().toPoint());

            for (auto p : ctSlots)
            {
                mSnapPointsPos << parentEle->mapToScene(QPointF(p));
            }

            for (auto e : parentEle->contactors())
            {
                ContactorElement* ele = static_cast<ContactorElement*>(e);
                // 接壳接触件不能调整位置
                if (ele->functionType() == ContactorElement::Enclosure)
                    continue;

                // mSnapPointsPos << ele->scenePos();
                mAllExistPointItems.append(qMakePair(ele->scenePos(), ele));
            }
        }
        else if (mContactorEle->parentElement()->adeType() == InterfaceModelElement::ADEType)
        {
            for (auto e : mContactorEle->parentElement()->childElements())
            {
                if (!e->isVisible())
                    continue;

                ContactorElement* ele = static_cast<ContactorElement*>(e);
                // 接壳接触件不能调整位置
                if (ele->functionType() == ContactorElement::Enclosure)
                    continue;

                mSnapPointsPos << e->scenePos();
                mAllExistPointItems.append(qMakePair(ele->scenePos(), ele));
            }
        }
    }
}

void ContactorMoveEventInterface::recordPointPosToNameBeforeSwitch()
{
    mPointPosRecord.clear();

    if (mContactorEle->parentElement())
    {
        //获取父元素所有的点位元素的点位
        if (mContactorEle->parentElement()->adeType() == ConnectorWiringElement::ADEType)
        {
            ConnectorWiringElement* parentEle = static_cast<ConnectorWiringElement*>(mContactorEle->parentElement());
            QList<QPoint> ctSlots = parentEle->contactorSlots();

            ctSlots.removeOne(parentEle->enclosureElement()->pos().toPoint());

            // 记录点位场景坐标-点位名称
            QMap<QString, QPointF> existPoints;
            for (auto pair : mAllExistPointItems)
            {
                existPoints.insert(pair.second->textItem()->text(), pair.first);
            }

            for (auto p : ctSlots)
            {
                QPointF scenePos = parentEle->mapToScene(QPointF(p));

                if (existPoints.values().contains(scenePos))
                    mPointPosRecord.insert(scenePos, existPoints.key(scenePos));
                else
                    mPointPosRecord.insert(scenePos, "");
            }

        }
        else if (mContactorEle->parentElement()->adeType() == InterfaceModelElement::ADEType)
        {
            for (auto e : mContactorEle->parentElement()->childElements())
            {
                if (!e->isVisible())
                    continue;

                ContactorElement* ele = static_cast<ContactorElement*>(e);
                // 接壳接触件不能调整位置
                if (ele->functionType() == ContactorElement::Enclosure)
                    continue;

                mPointPosRecord.insert(ele->scenePos(), ele->textItem()->text());
            }
        }
    }
}

void ContactorMoveEventInterface::hideContactorMovedSnapItem()
{
    // 隐藏被移动点位的蓝点
    for (auto item : mSnapPointItems)
    {
        if (item->scenePos() == mContactorEle->scenePos() ||
                item->scenePos() == mLastSwitchPointPos)
            item->setVisible(false);
        else
            item->setVisible(true);
    }
}

void ContactorMoveEventInterface::copyMoveContactorEleConductorsPath(CustomConductor *copy, CustomConductor *previewCdt)
{
    QList<QPointF> points = copy->vertexPoints();
    previewCdt->makePathFromPoints(points);
}
