#include <QCoreApplication>
#include <QDebug>
#include <QUuid>
#include <QSqlDatabase>
#include <QSqlError>
#include <stdexcept>
#include <QDateTime>

// Assume TEST_INFO and TEST_ERROR are defined somewhere globally
#ifndef TEST_INFO
#define TEST_INFO(msg) qDebug("%s", qPrintable(msg))
#endif
#ifndef TEST_ERROR
#define TEST_ERROR(msg) qWarning("%s", qPrintable(msg))
#endif

// Include Part DB Controllers
#include "../database/partDb/sqlCtrl/libraryinfoctrl.h"
#include "../database/partDb/sqlCtrl/datasheetsctrl.h"
#include "../database/partDb/sqlCtrl/partsctrl.h"
#include "../database/partDb/sqlCtrl/partdatasheetlinksctrl.h"

// Include Part DB Models
#include "../database/partDb/sqlModel/libraryinfomodel.h"
#include "../database/partDb/sqlModel/datasheetsmodel.h"
#include "../database/partDb/sqlModel/partsmodel.h"
#include "../database/partDb/sqlModel/partdatasheetlinksmodel.h"

#include "SimpleDbManager.h"
#include "TestTracker.h"

void testLibraryInfoCtrl(QSqlDatabase& db, TestTracker& tracker) {
    qDebug() << "--- Testing LibraryInfoCtrl ---";
    LibraryInfoCtrl ctrl(db);
    if (!ctrl.isTableExist()) {
        QSqlError err;
        bool created = ctrl.createTable(err);
        tracker.check(created, "Create LibraryInfo table. Error: " + err.text());
        if (!created) return;
    }

    // Fetch or Create
    auto infoObj = ctrl.fetch();
    if (!infoObj)
    {
        qDebug() << "No library info found, inserting initial record.";
        infoObj = LibraryInfoModel::createPtr();
        infoObj->libraryName = "Test Part Library";
        infoObj->version = "1.0.0";
        infoObj->createdBy = "TestHarness";
        infoObj->creationTimestamp = QDateTime::currentDateTime();
        bool inserted = ctrl.insert(infoObj);
        tracker.check(inserted, "Insert initial library info record");
        if(!inserted) return; // Can't proceed
    }
    else
    {
        tracker.check(true, "Found existing library info record");
    }

    // Read Test
    auto fetchedObj = ctrl.fetch();
    tracker.check(fetchedObj != nullptr, "Fetch library info record");

    // Modify Test
    if(fetchedObj) {
        fetchedObj->version = "1.0.1";
        fetchedObj->lastModifiedTimestamp = QDateTime::currentDateTime();
        bool modified = ctrl.modify(fetchedObj);
        tracker.check(modified, "Modify library info version");
        if(modified) {
            auto verifiedObj = ctrl.fetch();
            tracker.check(verifiedObj && verifiedObj->version == "1.0.1", "Verify modification");
        }
    }
}

void testDatasheetsCtrl(QSqlDatabase& db, TestTracker& tracker) {
    qDebug() << "--- Testing DatasheetsCtrl ---";
    DatasheetsCtrl ctrl(db);
    if (!ctrl.isTableExist()) {
        QSqlError err;
        bool created = ctrl.createTable(err);
        tracker.check(created, "Create Datasheets table. Error: " + err.text());
        if (!created) return;
    }

    // Insert
    QString newDsId;
    {
        auto dsObj = DatasheetsModel::createPtr();
        dsObj->title = "Test Datasheet";
        dsObj->filePath = "/path/to/datasheet.pdf";
        dsObj->md5Hash = "d41d8cd98f00b204e9800998ecf8427e"; // Empty file hash
        dsObj->uploadTimestamp = QDateTime::currentDateTime();
        bool inserted = ctrl.insert(dsObj);
        tracker.check(inserted, "Insert new datasheet");
        if(inserted) {
            newDsId = dsObj->id;
        } else {
            return;
        }
    }

    // Modify and Verify
    {
        auto dsObj = ctrl.fetch(newDsId);
        if(dsObj) {
            dsObj->title = "Modified Datasheet Title";
            bool modified = ctrl.modify(dsObj);
            tracker.check(modified, "Modify datasheet title");
            if(modified) {
                auto fetched = ctrl.fetch(newDsId);
                tracker.check(fetched && fetched->title == "Modified Datasheet Title", "Verify datasheet modification");
            }
        }
    }

    // Delete and Verify
    {
        bool removed = ctrl.remove(newDsId);
        tracker.check(removed, "Delete datasheet");
        if(removed) {
            auto fetched = ctrl.fetch(newDsId);
            tracker.check(fetched == nullptr, "Verify datasheet deleted");
        }
    }
}

void testPartsCtrl(QSqlDatabase& db, TestTracker& tracker) {
    qDebug() << "--- Testing PartsCtrl ---";
    PartsCtrl ctrl(db);
    if (!ctrl.isTableExist()) {
        QSqlError err;
        bool created = ctrl.createTable(err);
        tracker.check(created, "Create Parts table. Error: " + err.text());
        if(!created) return;
    }

    // Insert
    QString newPartId;
    {
        auto partObj = PartsModel::createPtr();
        partObj->partNumber = "PN12345";
        partObj->categoryUUID = QUuid::createUuid().toString();
        partObj->manufacturerUUID = QUuid::createUuid().toString();
        partObj->description = "A test part";
        partObj->creationTimestamp = QDateTime::currentDateTime();
        partObj->createdBy = "TestHarness";

        bool inserted = ctrl.insert(partObj);
        tracker.check(inserted, "Insert new part");
        if(inserted) {
            newPartId = partObj->id;
        } else {
            return;
        }
    }

    // Modify and Verify
    {
        auto partObj = ctrl.fetch(newPartId);
        if(partObj) {
            partObj->description = "An updated test part";
            partObj->lastModifiedTimestamp = QDateTime::currentDateTime();
            partObj->lastModifiedBy = "TestHarnessModify";
            bool modified = ctrl.modify(partObj);
            tracker.check(modified, "Modify part description");
            if(modified) {
                auto fetched = ctrl.fetch(newPartId);
                tracker.check(fetched && fetched->description == "An updated test part", "Verify part modification");
            }
        }
    }

    // Delete and Verify
    {
        bool removed = ctrl.remove(newPartId);
        tracker.check(removed, "Delete part");
        if(removed) {
            auto fetched = ctrl.fetch(newPartId);
            tracker.check(fetched == nullptr, "Verify part deleted");
        }
    }
}

void testPartDatasheetLinksCtrl(QSqlDatabase& db, TestTracker& tracker)
{
    qDebug() << "--- Testing PartDatasheetLinksCtrl ---";

    // Setup
    PartsCtrl partCtrl(db);
    auto part = PartsModel::createPtr();
    part->partNumber = "PN-LINK-TEST";
    partCtrl.insert(part);

    DatasheetsCtrl dsCtrl(db);
    auto ds = DatasheetsModel::createPtr();
    ds->title = "Link Test DS";
    ds->filePath = "/path/to/link_test_ds.pdf";
    dsCtrl.insert(ds);

    tracker.check(!part->id.isEmpty() && !ds->id.isEmpty(), "Setup: Create temporary part and datasheet for link test");
    if(part->id.isEmpty() || ds->id.isEmpty()) {
        partCtrl.remove(part->id); // cleanup
        dsCtrl.remove(ds->id); // cleanup
        return;
    }

    PartDatasheetLinksCtrl ctrl(db);
    if (!ctrl.isTableExist()) {
        QSqlError err;
        bool created = ctrl.createTable(err);
        tracker.check(created, "Create Part_Datasheet_Links table. Error: " + err.text());
        if(!created) return;
    }
    
    // Insert and Verify
    QString newLinkId;
    {
        auto linkObj = PartDatasheetLinksModel::createPtr();
        linkObj->partUUID = part->id;
        linkObj->datasheetUUID = ds->id;
        bool inserted = ctrl.insert(linkObj);
        tracker.check(inserted, "Insert new part-datasheet link");
        if(inserted) {
            newLinkId = linkObj->id;
        } else {
            // cleanup
            partCtrl.remove(part->id);
            dsCtrl.remove(ds->id);
            return;
        }
    }

    // Delete and Verify
    {
        bool removed = ctrl.remove(newLinkId);
        tracker.check(removed, "Delete part-datasheet link");
        if(removed) {
            auto fetched = ctrl.fetch(newLinkId);
            tracker.check(fetched == nullptr, "Verify part-datasheet link deleted");
        }
    }

    // Teardown
    bool partRemoved = partCtrl.remove(part->id);
    bool dsRemoved = dsCtrl.remove(ds->id);
    tracker.check(partRemoved && dsRemoved, "Teardown: Remove temporary part and datasheet");
}

void run_partDb_tests()
{
    qDebug() << "\n\n===== Starting Part DB Tests... =====";
    TestTracker tracker;

    QString err;
    QString uuid = QUuid::createUuid().toString();
    QSqlDatabase db;
    // An application instance is expected to exist, so applicationDirPath is valid.
    QString path = QString("%1/part_test.db").arg(QCoreApplication::applicationDirPath());

    if (!SimpleDbManager::connect(path, err, uuid, db))
    {
        QString errInfo = QString("Database connection failed: %1").arg(err);
        TEST_ERROR(errInfo);
        return;
    }

    qDebug() << "Database connected:" << path;

    try {
        testLibraryInfoCtrl(db, tracker);
        testDatasheetsCtrl(db, tracker);
        testPartsCtrl(db, tracker);
        testPartDatasheetLinksCtrl(db, tracker);

    } catch (const std::runtime_error& e) {
        tracker.check(false, QString("A runtime error occurred: %1").arg(e.what()));
    }

    SimpleDbManager::close(uuid);
    qDebug() << "Database connection closed.";
    tracker.printSummary("Part DB");
} 
