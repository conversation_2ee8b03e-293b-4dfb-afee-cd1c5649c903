#ifndef VIEWMODELASSEMBLER_H
#define VIEWMODELASSEMBLER_H

#include "PartViewModel.h"
#include "CategoriesViewModel.h"
#include "../partDb/sqlModel/partmodel.h"
#include "../globalDb/sqlCtrl/attributedefinitionsctrl.h"
#include "../globalDb/sqlCtrl/categoriesctrl.h"
#include "../globalDb/sqlCtrl/categoryattributelinksctrl.h"

#include <QList>
#include <QString>

class ViewModelAssembler
{
public:
    /**
     * @brief 装配元器件视图模型
     * @param partModel             [输入] 从ptlib数据库中读取的原始PartModel
     * @param attrDefinitionsCtrl   [输入] 全局属性定义表的控制器实例
     * @return                      一个包含了所有UI所需信息的完整ViewModel
     */
    static PartViewModel createViewModel(const PartModelPtr& partModel,
                                         const AttributeDefinitionsCtrl& attrDefinitionsCtrl);

    /**
     * @brief 装配分类视图模型
     * @param categoriesModel       [输入] 从全局数据库中读取的原始CategoriesModel
     * @param categoryAttrLinks     [输入] 分类属性链接列表
     * @param attrDefinitionsCtrl   [输入] 全局属性定义表的控制器实例
     * @return                      一个包含了所有UI所需信息的完整CategoriesViewModel
     */
    static CategoriesViewModel createCategoriesViewModel(const CategoriesModelPtr& categoriesModel,
                                                         const QList<CategoryAttributeLinksModelPtr>& categoryAttrLinks,
                                                         const AttributeDefinitionsCtrl& attrDefinitionsCtrl);

    /**
     * @brief 装配分类视图模型（便捷方法）
     * @param categoryId            [输入] 分类ID
     * @param categoriesCtrl        [输入] 分类表控制器实例
     * @param categoryAttrLinksCtrl [输入] 分类属性链接表控制器实例
     * @param attrDefinitionsCtrl   [输入] 全局属性定义表的控制器实例
     * @return                      一个包含了所有UI所需信息的完整CategoriesViewModel
     */
    static CategoriesViewModel createCategoriesViewModel(const QString& categoryId,
                                                         const CategoriesCtrl& categoriesCtrl,
                                                         const CategoryAttributeLinksCtrl& categoryAttrLinksCtrl,
                                                         const AttributeDefinitionsCtrl& attrDefinitionsCtrl);
};

#endif // VIEWMODELASSEMBLER_H
