﻿/*
	Copyright 2006-2021 The QElectroTech Team
	This file is part of QElectroTech.

	QElectroTech is free software: you can redistribute it and/or modify
	it under the terms of the GNU General Public License as published by
	the Free Software Foundation, either version 2 of the License, or
	(at your option) any later version.

	QElectroTech is distributed in the hope that it will be useful,
	but WITHOUT ANY WARRANTY; without even the implied warranty of
	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
	GNU General Public License for more details.

	You should have received a copy of the GNU General Public License
	along with QElectroTech.  If not, see <http://www.gnu.org/licenses/>.
*/
#ifndef CABLESPLICEELEMENT_H
#define CABLESPLICEELEMENT_H

#include "../qetgraphicsitem/element.h"

class ADELogicCable;
class InterfaceElement;

/**
* @file
* @brief      ADE电缆端点元素
* <AUTHOR>
* @version    1.0
* @date       2023/09/25
* @todo
*/
class CableSpliceElement : public Element
{
    Q_OBJECT

public:
    explicit CableSpliceElement(const ElementsLocation &,
                              int * = nullptr);
    ~CableSpliceElement() override;

private:
    CableSpliceElement(const Element&);

public:
    enum { ADEType = 3 };
    int adeType() const override {return ADEType;}

public:
    QRectF      boundingRect() const override;
    bool        fromXml(QDomElement&, QHash<int,Terminal*>&) override;
    QDomElement toXml(QDomDocument&, QHash<Terminal*,int>&) const override;

protected:
    void mousePressEvent(QGraphicsSceneMouseEvent *event) override;
    void mouseMoveEvent(QGraphicsSceneMouseEvent* event) override;
    void mouseReleaseEvent(QGraphicsSceneMouseEvent* event) override;

public:
    ADELogicCable *cable() const {return mCable;}
    void setCable(ADELogicCable *cable);
    QList<InterfaceElement *> connectedInterfaceElements() const;
    QList<CableSpliceElement *> connectedSpliceElements() const;

private:
    ADELogicCable *mCable = nullptr;
    int mHalfLength;

    bool mMergeMode;
    QPointF mInitialMergePos;
    InterfaceElement *mMergeTargetInterfaceElement; ///< 在移动过程中合并的模板接口元素

private:
    static int MergeSearchRadius;
};

#endif // CABLESPLICEELEMENT_H
