﻿#include "connectorwiringelement.h"

#include "../qetgraphicsitem/dynamicelementtextitem.h"
#include "../qetgraphicsitem/conductor.h"
#include "../qetgraphicsitem/terminal.h"

#include "../diagram.h"
#include "../qetxml.h"
#include "../../sources/ElementsCollection/xmlelementcollection.h"
#include "../other/QetGraphicsItemModeler/qetgraphicshandleritem.h"
#include "../undocommand/customCommand/changewiringelementlengthcommand.h"

#include "contactorelement.h"

ConnectorWiringElement::ConnectorWiringElement(const ElementsLocation &location,
                                   int *state, const QString &uuid) :
    Element(location, nullptr, state, Element::Simple, uuid, nullptr),
    mInitialized(false)
    , mLeft<PERSON><PERSON><PERSON>(nullptr)
    , mRightHandler(nullptr)
    , mFakeLine(nullptr)
{
    // FIXME: 会导致元素旋转时文本无法固定不动的问题
    //setFlag(ItemSendsGeometryChanges, true);
    //qDebug() << Q_FUNC_INFO << flags();
    // connect(this, &ConnectorWiringElement::childrenChanged, [=](){
    //     for (auto ctt : contactors())
    //         ctt->setUserMovable(false);
    // });

    setFlag(ItemSendsGeometryChanges, true);

    for (auto ctt : contactors())
        ctt->setUserMovable(true);

    connect(this, &ConnectorWiringElement::rotationChanged, this, &ConnectorWiringElement::onRotationChanged);

    updateTextPos();
}


ConnectorWiringElement::~ConnectorWiringElement()
{
    removeHandler();
}

int ConnectorWiringElement::lengthByContactorCount(int count)
{
    // 连接器形状上两个接触件元素的间距固定为interval, 接触件与形状端点的距离为dist
    // 由此计算形状长度 length = (count - 1) * interval + 2 * dist
    int interval  = ConnectorWiringElement::ContactorInterval();
    int dist      = ConnectorWiringElement::ContactorDistance();

    int length = ((count - 1) * interval) + (2 * dist);

    return length;
}
bool ConnectorWiringElement::fromXml(QDomElement& e, QHash<int, Terminal*>& table_id_adr)
{
    bool res = Element::fromXml(e, table_id_adr);

    if (res)
    {

    }

    return res;
}

QDomElement ConnectorWiringElement::toXml(QDomDocument& document,
                                    QHash<Terminal*, int>& table_adr_id) const
{
    // 先在Element基类中创建xml元素, 再补充自定义信息
    QDomElement elmtXml = Element::toXml(document, table_adr_id);

    return elmtXml;
}

QList<ContactorElement *> ConnectorWiringElement::contactors() const
{
    QList<ContactorElement*> contactors;
    for (auto e : childElements())
    {
        if (e->adeType() == ContactorElement::ADEType)
        {
            contactors << static_cast<ContactorElement*>(e);
        }
    }

    return contactors;
}

QList<ContactorElement *> ConnectorWiringElement::sequentialContactors() const
{
    // 将所有接触键按照x坐标从小到大顺序排列
    QList<ContactorElement*> contactors = this->contactors();
    std::sort(contactors.begin(), contactors.end(), [](ContactorElement* c1, ContactorElement* c2){
        return c1->pos().x() < c2->pos().x();
    });
    return contactors;
}

QList<ContactorElement *> ConnectorWiringElement::sceneSequentialContactors() const
{
    QList<ContactorElement*> cdts = sequentialContactors();

    Qet::Orientation ori = sceneOrientation();
    if (ori == Qet::South)
    {
        std::reverse(cdts.begin(), cdts.end());
    }

    return cdts;
}

/**
 * @brief ConnectorWiringElement::sceneSequentialContactoSlots
 * @return
 */
QVector<QPair<ContactorElement *, QPoint> > ConnectorWiringElement::sceneSequentialContactoSlots() const
{
    QList<QPoint> pointSlots = contactorSlots();
    Qet::Orientation ori = sceneOrientation();
    if (ori == Qet::South)
    {
        std::reverse(pointSlots.begin(), pointSlots.end());
    }

    int cttIndex = 0;
    auto cttElmts = sceneSequentialContactors();

    QVector<QPair<ContactorElement *, QPoint>> cttSlots;
    for (int i = 0; i < pointSlots.size(); i++)
    {
        QPoint p = pointSlots.at(i);

        if (cttIndex < cttElmts.size())
        {
            ContactorElement *cttElmt = cttElmts.at(cttIndex);
            if (cttElmt->pos().toPoint() == p)
            {
                cttSlots << qMakePair(cttElmt, p);
                cttIndex++;
            }
            else
            {
                cttSlots << qMakePair(nullptr, p);
            }
        }
        else
        {
            cttSlots << qMakePair(nullptr, p);
        }
    }

    return cttSlots;
}

QList<QPoint> ConnectorWiringElement::contactorSlots() const
{
    QList<QPoint> points;
    for (int x = ContactorDistance();
         x <= (size().width() - ContactorDistance());
         x += ContactorInterval())
    {
        points << QPoint(x, 0);
    }

    return points;
}

int ConnectorWiringElement::contactorSlotIndex(ContactorElement *cttElmt) const
{
    QList<QPoint> pointSlots = contactorSlots();
    Qet::Orientation ori = sceneOrientation();
    if (ori == Qet::South)
    {
        std::reverse(pointSlots.begin(), pointSlots.end());
    }

    QPoint point = cttElmt->pos().toPoint();
    if (pointSlots.contains(point))
    {
        return pointSlots.indexOf(point);
    }
    else
    {
        int closestIndex = -1;
        int minDistance = INT_MAX;

        for (int i = 0; i < pointSlots.size(); ++i)
        {
            int distance = (pointSlots[i].x() - point.x()) * (pointSlots[i].x() - point.x()) +
                           (pointSlots[i].y() - point.y()) * (pointSlots[i].y() - point.y());

            if (distance < minDistance)
            {
                minDistance = distance;
                closestIndex = i;
            }
        }

        return closestIndex;
    }
}

ContactorElement *ConnectorWiringElement::enclosureElement() const
{
    QList<ContactorElement*> contactors = this->contactors();
    if (contactors.isEmpty())
    {
        return nullptr;
    }

    for (auto ctcElmt : contactors)
    {
        if (ctcElmt->functionType() == ContactorElement::Enclosure)
            return ctcElmt;
    }
    return nullptr;
}

ContactorElement *ConnectorWiringElement::contactorElement(const QString &ifUuid,
                                                           const QString &point) const
{
    for (auto e : contactors())
    {
        if (e->functionType()  == ContactorElement::Contactor &&
            e->adeFatherUuid() == ifUuid &&
            e->pointName()     == point)
            return e;
    }

    return nullptr;
}

void ConnectorWiringElement::setContactorPointNames(const QMap<QString, QStringList> &ifPointNames)
{
    QList<ContactorElement *> cttElmts = sequentialContactors();

    for (QString ifUuid : ifPointNames.keys())
    {
        QStringList points = ifPointNames.value(ifUuid);
        for (auto point : points)
        {
            if (cttElmts.isEmpty())
                return;
            ContactorElement *ctcElmt = cttElmts.takeFirst();
            if (ctcElmt->functionType() == ContactorElement::Contactor)
            {
                ctcElmt->setPointName(point);
                /// 设置接触件的ADE父接口uuid, 对于多腔体接口, 其整个接口用一个连接器元素表示,
                /// 用父接口uuid区分不同腔体
                ctcElmt->setADEFatherUuid(ifUuid);
            }
        }
    }
}

QStringList ConnectorWiringElement::getContactorFatherIfUuids() const
{
    QStringList uuids;
    for (auto c : contactors())
    {
        if (c->functionType() == ContactorElement::Contactor &&
            !uuids.contains(c->adeFatherUuid()))
        {
            uuids << c->adeFatherUuid();
        }
    }

    return uuids;
}

bool ConnectorWiringElement::swapContactorToPos(ContactorElement *cttElmt, const QPointF &pos)
{
    if (!cttElmt)
        return false;

    // 接触件元素位置不能设置到两端边界距离内
    if (std::abs(pos.x()) > (boundingRect().width() - ContactorDistance()))
        return false;

    // 将接触件元素移动到指定位置, 如果该位置已有接触件, 则将两个接触件的位置互换
    QList<ContactorElement*> contactors = sequentialContactors();
    if (contactors.isEmpty())
    {
        return false;
    }

    for (auto c : contactors)
    {
        if (c == cttElmt)
            continue;
        if (c->functionType() == ContactorElement::Enclosure)
            continue;
        if (c->pos() == pos)
        {
            c->setPos(cttElmt->pos(), false);
            cttElmt->setPos(pos, false);
            return true;
        }
    }

    if (contactorSlots().contains(pos.toPoint()))
    {
        cttElmt->setPos(pos, false);
        return true;
    }
    else
    {
        return false;
    }
}

ContactorElement *ConnectorWiringElement::getConnectedContactorToContactor(ContactorElement *otherCtt) const
{
    if (!otherCtt)
        return nullptr;

    for (auto ctt : contactors())
    {
        if (ctt->terminal()->isLinkedTo(otherCtt->terminal()))
            return ctt;
    }

    return nullptr;
}

void ConnectorWiringElement::adjustLength()
{
    // 计算新长度
    int contactorCount = contactors().count();
    int newLength = lengthByContactorCount(contactorCount);

    // 新长度小于旧长度时说明连接器元素被手动延长过，此时可以满足放置所有接触件元素
    // 的条件，不需要再调整长度
    if (newLength <= size().width())
        return;

    setLength(newLength);
}

void ConnectorWiringElement::setLength(qreal newLength)
{
    // 构造元素定义xml
    QDomElement definitionXml = location().xml();

    definitionXml.setAttribute("width", QString("%1").arg(newLength));

    // 获取description节点
    QDomNodeList descriptions = definitionXml.elementsByTagName("description");
    if (descriptions.size() > 0)
    {
        QDomElement description = descriptions.at(0).toElement();

        // 获取line节点
        QDomNodeList lines = description.elementsByTagName("line");
        if (lines.size() > 0) {
            QDomElement line = lines.at(0).toElement();
            line.setAttribute("x2", QString("%1").arg(newLength));
        }
    }

    // 更新元素定义xml
    ElementsLocation newLoc = location().project()
                                  ->embeddedElementCollection()
                                  ->replaceDefinition(location(), definitionXml);
    changeLocation(newLoc);

    setSize(newLength, size().height());

    updateHandlerState();
}

void ConnectorWiringElement::setName(const QString &name)
{
    if (dynamicTextItems().isEmpty())
        return;

    DynamicElementTextItem *nameText = textItem();
    nameText->setPlainText(name);
}

QVariant ConnectorWiringElement::itemChange(GraphicsItemChange change, const QVariant& value)
{
    if (QGraphicsItem::ItemSceneHasChanged == change)
    {
        if (scene() && !mInitialized)
        {
            initContactorPos();
            mInitialized = true;
        }
    }
    else if (change == ItemSelectedHasChanged && scene())
    {
        if (value.toBool() == true)
        {
            if (scene()->selectedItems().size() == 1)
                addHandler();
        }
        else
        {
            removeHandler();
        }
    }
    else if (QGraphicsItem::ItemRotationHasChanged == change)
    {
        updateHandlerState();
    }
    else if (QGraphicsItem::ItemChildAddedChange == change)
    {
        // QGraphicsItem* child = value.value<QGraphicsItem*>();
        // // 接触件元素端子禁止交互
        // if (Element *elmt = qgraphicsitem_cast<Element *>(child))
        // {
        //     if (ContactorElement *cttElmt = element_cast<ContactorElement *>(elmt))
        //     {
        //         cttElmt->terminal()->setEnabled(false);
        //     }
        // }
    }
    else if (QGraphicsItem::ItemScenePositionHasChanged == change)
    {
        updateHandlerState();
    }

    return Element::itemChange(change, value);
}

bool ConnectorWiringElement::sceneEventFilter(QGraphicsItem *watched, QEvent *event)
{
    //Watched must be an handler
    if(watched->type() == QetGraphicsHandlerItem::Type)
    {
        QetGraphicsHandlerItem *qghi = qgraphicsitem_cast<QetGraphicsHandlerItem *>(watched);
        if(event->type() == QEvent::GraphicsSceneMousePress) //Click
        {
            handlerMousePressEvent(qghi, static_cast<QGraphicsSceneMouseEvent *>(event));
            return true;
        }
        else if(event->type() == QEvent::GraphicsSceneMouseMove) //Move
        {
            handlerMouseMoveEvent(qghi, static_cast<QGraphicsSceneMouseEvent *>(event));
            return true;
        }
        else if (event->type() == QEvent::GraphicsSceneMouseRelease) //Release
        {
            handlerMouseReleaseEvent(qghi, static_cast<QGraphicsSceneMouseEvent *>(event));
            return true;
        }
    }

    return false;
}

void ConnectorWiringElement::mousePressEvent(QGraphicsSceneMouseEvent *event)
{
    Element::mousePressEvent(event);
}

void ConnectorWiringElement::onRotationChanged()
{
    updateEnclosurePos();
}

void ConnectorWiringElement::initContactorPos()
{
    // 1. 获取所有的接触件元素
    QList<ContactorElement*> contactors = this->contactors();
    if (contactors.isEmpty())
    {
        return;
    }

    // 2. 计算接触件的位置
    // QList<QPoint> contactorSlots;
    // int interval = ContactorInterval();
    // int x        = ContactorDistance();
    // int y        = 0;
    // for (int i = 0; i < contactors.count(); i++)
    // {
    //     contactorSlots << QPoint(x, y);
    //     x += interval;
    // }
    QList<QPoint> contactorsSlots = contactorSlots();

    // 3. 去除已经在固定位置里的接触件
    QList<ContactorElement *> unSetedContactors;
    for (auto ctt : contactors)
    {
        QPoint p = ctt->pos().toPoint();
        if (contactorsSlots.contains(p))
        {
            contactorsSlots.removeOne(p);
        }
        else
        {
            unSetedContactors << ctt;
        }
    }

    // 4. 给未固定位置的接触件设置位置
    for (auto ctt : unSetedContactors)
    {
        if (!contactorsSlots.isEmpty())
        {
            QPoint p = contactorsSlots.takeFirst();
            ctt->setPos(p, false);
        }
    }

    // 5. 更新接壳位置
    updateEnclosurePos();
}

void ConnectorWiringElement::updateEnclosurePos()
{
    QList<ContactorElement*> contactors = sequentialContactors();
    if (contactors.count() <= 1)
    {
        return;
    }

    ContactorElement *enclosure = enclosureElement();
    if (!enclosure)
    {
        return;
    }

    // Qet::Orientation ori = sceneOrientation();
    // if ((ori != Qet::North) && (ori != Qet::South))
    // {
    //     return;
    // }

    QPoint ecPos = enclosurePos();
    if (enclosure->pos().toPoint() != ecPos)
    {
        swapContactorToPos(enclosure, ecPos);
    }

    // // 连接器角度为0时, 接壳位于末尾
    // Qet::Orientation ori = sceneOrientation();
    // if (ori == Qet::North)
    // {
    //     // 接壳已经位于末尾
    //     if (contactors.last() == enclosure)
    //         return;

    //     // 将接壳与最后一个接触件交换位置
    //     QPointF ePos = enclosure->pos();
    //     enclosure->setPos(contactors.last()->pos(), false);
    //     contactors.last()->setPos(ePos, false);

    // }
    // // 连接器角度为180时, 接壳位于头
    // else if (ori == Qet::South)
    // {
    //     // 接壳已经位于首部
    //     if (contactors.first() == enclosure)
    //         return;

    //     // 将接壳与第一个接触件交换位置
    //     QPointF ePos = enclosure->pos();
    //     enclosure->setPos(contactors.first()->pos(), false);
    //     contactors.first()->setPos(ePos, false);
    // }
}

QPoint ConnectorWiringElement::enclosurePos() const
{
    Qet::Orientation ori = sceneOrientation();
    if (ori != Qet::South)
    {
        return QPoint(size().width() - ContactorDistance(), 0);
    }
    else if (ori == Qet::South)
    {
        return QPoint(ContactorDistance(), 0);
    }

    return QPoint(size().width() - ContactorDistance(), 0);
}

void ConnectorWiringElement::addHandler()
{
    // 初始化抓手相关
    if (!mLeftHandler && scene())
    {
        mLeftHandler = new QetGraphicsHandlerItem();
        mRightHandler = new QetGraphicsHandlerItem();
        QList<QetGraphicsHandlerItem *> handlers{mLeftHandler, mRightHandler};
        for (auto handler : handlers)
        {
            handler->setColor(Qt::blue);
            scene()->addItem(handler);
            handler->installSceneEventFilter(this);
            handler->setZValue(this->zValue()+1);
        }

        mFakeLine = new QGraphicsLineItem();
        mFakeLine->setZValue(this->zValue() + 1);
        mFakeLine->setPen(QPen(Qt::blue, 1, Qt::DashLine));
    }

    updateHandlerState();
}

void ConnectorWiringElement::removeHandler()
{
    delete mLeftHandler;
    mLeftHandler = nullptr;

    delete mRightHandler;
    mRightHandler = nullptr;

    delete mFakeLine;
    mFakeLine = nullptr;
}

void ConnectorWiringElement::updateHandlerState()
{
    if (!isSelected())
        return;

    // 如果连接器不是水平显示时移除抓手
    Qet::Orientation ori = sceneOrientation();
    if (Qet::North == ori)
    {
        if (!mLeftHandler || !mRightHandler)
            addHandler();

        mLeftHandler->setPos(this->scenePos());
        mRightHandler->setPos(this->mapToScene(QPointF(size().width(), 0)));
    }
    else if (Qet::South == ori)
    {
        if (!mLeftHandler || !mRightHandler)
            addHandler();

        mLeftHandler->setPos(this->mapToScene(QPointF(size().width(), 0)));
        mRightHandler->setPos(this->scenePos());
    }
    else
    {
        removeHandler();
    }
}

void ConnectorWiringElement::handlerMousePressEvent(QetGraphicsHandlerItem *qghi, QGraphicsSceneMouseEvent *event)
{
    Q_UNUSED(qghi)
    Q_UNUSED(event)
}

void ConnectorWiringElement::handlerMouseMoveEvent(QetGraphicsHandlerItem *qghi, QGraphicsSceneMouseEvent *event)
{
    if (sequentialContactors().size() < 2)
        return;

    // 计算视觉方向移动范围的限制与视觉方向初始点位
    int maxShirnkLengthFromLeft = 0;
    int maxShrinkLengthFromRight = 0;
    QPointF initLeftPos;
    QPointF initRightPos;
    {
        auto ctts = sequentialContactors();
        QList<QPoint> cttSlots = contactorSlots();

        // 规则：接壳元素始终位于视觉方向最右侧槽位
        // 根据连接器旋转方向判断接壳位于连接器元素坐标系最左侧还是最右侧
        Qet::Orientation ori = sceneOrientation();
        if (Qet::North == ori)
        {
            initLeftPos = this->scenePos();
            initRightPos = this->mapToScene(QPointF(size().width(), 0));
        }
        else if (Qet::South == ori)
        {
            initLeftPos = this->mapToScene(QPointF(size().width(), 0));
            initRightPos = this->scenePos();

            std::reverse(ctts.begin(), ctts.end());
            std::reverse(cttSlots.begin(), cttSlots.end());
        }

        QPointF farLeftCttPos = ctts.first()->pos();
        QPointF farRightCttPos = ctts.last()->pos();


        // // 调试
        // QList<qreal> ppp;
        // for (auto ctt : ctts)
        // {
        //     ppp << ctt->x();
        // }
        // //

        // 从左侧向内拉伸的限制是最左侧的点位与第一个可插槽位置的水平距离
        maxShirnkLengthFromLeft = qAbs(farLeftCttPos.x() - cttSlots.first().x());

        // 从右侧向内拉伸的限制是最右侧接壳与其左侧第一个点的多余空位
        maxShrinkLengthFromRight = qAbs(farRightCttPos.x() - ctts.at(ctts.size() - 2)->pos().x()) - ContactorInterval();
    }

    int leftHandlerFarRightXPos = initLeftPos.x() + maxShirnkLengthFromLeft;
    int rightHandlerFarLeftXPos = initRightPos.x() - maxShrinkLengthFromRight;

    // 调整抓手位置
    QPointF ePos = event->scenePos();
    if (mLeftHandler == qghi)
    {
        if (leftHandlerFarRightXPos <= ePos.x())
        {
            mLeftHandler->setX(leftHandlerFarRightXPos);
        }
        else
        {
            // 将事件坐标调整为contactorInterval的倍数（网格吸附）
            int interval = ContactorInterval();

            int adjustedX = qRound(this->mapFromScene(ePos).x() / interval) * interval;
            mLeftHandler->setX(this->mapToScene(QPointF(adjustedX, 0)).x());
        }
    }
    else if (mRightHandler == qghi)
    {
        if (ePos.x() <= rightHandlerFarLeftXPos)
        {
            mRightHandler->setX(rightHandlerFarLeftXPos);
        }
        else
        {
            // 将事件坐标调整为contactorInterval的倍数（网格吸附）
            int interval = ContactorInterval();

            int adjustedX = qRound(this->mapFromScene(ePos).x() / interval) * interval;
            mRightHandler->setX(this->mapToScene(QPointF(adjustedX, 0)).x());
        }
    }

    // 显示指示线
    {
        if (!mFakeLine->scene())
        {
            diagram()->addItem(mFakeLine);
        }

        // 根据拖拽的抓手位置调整起始点，防止出现虚线交错的显示效果
        QLineF line;
        if (mLeftHandler == qghi)
            line = QLineF(mRightHandler->pos(), mLeftHandler->pos());
        else
            line = QLineF(mLeftHandler->pos(), mRightHandler->pos());

        mFakeLine->setLine(line);
    }
}

void ConnectorWiringElement::handlerMouseReleaseEvent(QetGraphicsHandlerItem *qghi, QGraphicsSceneMouseEvent *event)
{
    // 移除指示线
    diagram()->removeItem(mFakeLine);

    // 计算连接器左右两端初始坐标
    QPointF initLeftPos;
    QPointF initRightPos;
    {
        Qet::Orientation ori = sceneOrientation();
        if (Qet::North == ori)
        {
            initLeftPos = this->scenePos();
            initRightPos = this->mapToScene(QPointF(size().width(), 0));
        }
        else if (Qet::South == ori)
        {
            initLeftPos = this->mapToScene(QPointF(size().width(), 0));
            initRightPos = this->scenePos();
        }
    }

    // 计算抓手移动的距离
    int leftStretchLength = 0;
    int rightStretchLength = 0;
    if (mLeftHandler == qghi)
    {
        leftStretchLength = initLeftPos.x() - mLeftHandler->scenePos().x();
    }
    else if (mRightHandler == qghi)
    {
        rightStretchLength = mRightHandler->scenePos().x() - initRightPos.x();
    }
    else
    {
        return;
    }

    if (leftStretchLength == 0 && rightStretchLength == 0)
        return;

    // 添加撤销命令
    auto cmd = new ChangeWiringElementLengthCommand(this, leftStretchLength, rightStretchLength);
    diagram()->undoStack().push(cmd);
}

void ConnectorWiringElement::updateTextPos()
{
    QPointF pos = TextPos();

    // 保持文本位于连接器左边
    if (rotation() == 0)
    {
        pos = TextPos();
    }
    else if (rotation() == 180)
    {
        pos = QPointF(size().width() + abs(TextPos().x()),
                      -TextPos().y());
    }
    else
       pos = TextPos();

    textItem()->setPos(pos);
}

QList<ContactorElement *> ConnectorWiringElement::moveContactor(ContactorElement *cttElmt, qreal movment)
{
    if (movment == 0)
        return QList<ContactorElement *>();

    // 获取有序的非接壳接触件元素
    QList<ContactorElement *> cttElmts;
    for (const auto &ctt : sequentialContactors())
    {
        if (ctt->functionType() == ContactorElement::Enclosure)
            continue;
        cttElmts << ctt;
    }

    // 连接器元素方向不朝上时将接触件顺序反转以获取场景上x轴排序的顺序
    bool visualPositive = (((int)rotation() % 360) == 0) ? true : false;
    if (!visualPositive)
    {
        // 反转列表
        std::reverse(cttElmts.begin(), cttElmts.end());
    }

    // 计算接触件元素步进的位数
    int step = 0;
    int interval = ContactorInterval();
    if (movment > 0)
    {
        step = (int)std::ceil(movment / interval);
    }
    else
    {
        step = (int)std::floor(movment / interval);
    }

    // 对步进位置进行合法校验
    int index = contactorSlotIndex(cttElmt);
    int slotCount = contactorSlots().size();

    if (step == 0)
        return QList<ContactorElement *>();
    else if (step > 0)
    {
        int maxStep = (slotCount - 1) - index;
        if (step > maxStep) step = maxStep;
    }
    else
    {
        int maxStep = -index;
        if (step < maxStep) step = maxStep;
    }

    // 若当前接触件索引位置为n
    // 大于0时向右移动step个位置, 将[n+1, n+step]位置的接触件向左移动一个位置
    // 小于0时向左移动step个位置, 将[n-abs(step), n-1]位置的接触件向右移动一个位置
    int factor = visualPositive ? 1 : -1;
    QList<ContactorElement *> affectedCttElmts;
    QVector<QPair<ContactorElement *, QPoint> > cttElmtSlots = sceneSequentialContactoSlots();

    if (step > 0)
    {
        for (int i = index + 1; i <= index + step; i++)
        {
            QPair<ContactorElement *, QPoint> cttSlot = cttElmtSlots.at(i);
            if (auto cttElmt = cttSlot.first)
            {
                cttElmt->moveBy(-interval * factor, 0);
                affectedCttElmts << cttElmt;
            }
        }
    }
    else
    {
        for (int i = index + step; i <= index - 1; i++)
        {
            QPair<ContactorElement *, QPoint> cttSlot = cttElmtSlots.at(i);
            if (auto cttElmt = cttSlot.first)
            {
                cttElmt->moveBy(interval * factor, 0);
                affectedCttElmts << cttElmt;
            }
        }
    }

    cttElmt->moveBy(interval * step * factor, 0);

    return affectedCttElmts;
}

QList<ContactorElement *> ConnectorWiringElement::moveContactors(QList<ContactorElement *> cttElmts, qreal movment)
{
    if (movment == 0)
        return QList<ContactorElement *>();

    // 将接触件元素按照场景坐标从小到大排序
    std::sort(cttElmts.begin(), cttElmts.end(), [](ContactorElement *a, ContactorElement *b) {
        return a->scenePos().x() < b->scenePos().x();
    });

    // 向右移动时将列表反转从最右侧开始移动
    if (movment > 0)
    {
        std::reverse(cttElmts.begin(), cttElmts.end());
    }

    QList<ContactorElement *> allAffectedCttElmts;
    for (const auto &cttElmt : cttElmts)
        allAffectedCttElmts << moveContactor(cttElmt, movment);

    return allAffectedCttElmts.toSet().toList();
}
