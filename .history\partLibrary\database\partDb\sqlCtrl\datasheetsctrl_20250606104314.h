#ifndef DATASHEETSCTRL_H
#define DATASHEETSCTRL_H

#include "sqlCtrlBase.h"
#include "../sqlModel/datasheetsmodel.h"

//============================================================================
/// file
/// brief      Datasheet信息表控制器
/// author     wm
/// version    1.0
/// date       2025/06/05
/// todo
//============================================================================

class DatasheetsCtrl : public SqlCtrlBase
{
public:
    DatasheetsCtrl(const QSqlDatabase& db);
    ~DatasheetsCtrl();

    // 针对数据模型的操作
    DatasheetsModelList fetchAll() const;
    DatasheetsModelPtr  fetch(const QString &id) const;
    bool modelExist(const QString &id) const;
    // end

    // 针对数据库表的操作
    bool insert(const DatasheetsModelPtr &obj);
    bool modify(const DatasheetsModelPtr &newValueObj);
    bool remove(const QString &id);
    // end

public:
    QString tableName()       const override {return "Datasheets";}
    QString primaryKeyField() const override {return "DatasheetUUID";}
    bool    recordExist(const QString &id) const override;

private:
    QString tableSql()        const override;
    void loadTable();

private:
    DatasheetsModelList mAllObj;
};

#define Datasheets_Factory objFactory<QString, DatasheetsCtrl>::getInstance()

#endif // DATASHEETSCTRL_H 