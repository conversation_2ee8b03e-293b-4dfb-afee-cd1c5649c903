﻿#ifndef ADETEXTPROPERTIES_H
#define ADETEXTPROPERTIES_H

#include <QString>
#include <QList>

#include "ade.h"

class ADETextProperties
{
public:
    enum ADEItemTextShowState
    {
        DGM_BothShow = 1,   ///< 代号和名称都显示
        DGM_OnlyMarkShow,   ///< 只显示代号
        DGM_OnlyNameShow    ///< 只显示名称
    };

    /**
     * @brief The TextType enum 文本类别
     */
    enum TextType
    {
        Basic,  ///< 基础文本，只包含代号和名称
        Full    ///< 所有文本
    };

    /**
     * @brief The Align enum 文本排列方向
     */
    enum Align
    {
        Left,   ///< 居左显示
        Center  ///< 居中显示
    };

public:
    /**
     * @brief ADETextProperties
     * @param srcText
     *       名称---内容---是否显示(1:显示, 0:不显示)
     * 格式：<代号;K12-25;1;;名称;时序控制器;1;>
     */
    ADETextProperties(const QString &srcText, Align textAlign = Align::Center);

    /**
     * @brief The TextInfo struct 文本块属性
     */
    struct TextInfo
    {
        QString textName;   ///< 文本名称
        QString content;    ///< 文本内容
        bool show;          ///< 是否显示
    };

    bool isEmpty() const;

    QString toSourceText() const;

    QList<TextInfo> textInfos() const {return mTextInfos;}

    QString getOrganisedText(TextType type = Basic) const;

    bool hasADETextType() const;

    /**
     * @brief syncShowState 同步文本显示状态
     * @param other
     */
    void syncShowState(const ADETextProperties &other);

    /**
     * @brief setTextShowState 设置文本显示状态
     * @param name             文本名称
     * @param show             是否显示
     * @return
     */
    bool setTextShowState(const QString &name, const bool &show);

    /**
     * @brief setBasicTextShowState 设置名称/代号文本是否显示
     * @param option                显示选项
     */
    void setBasicTextShowState(ADEItemTextShowState option);

    void setTextAlignDirection(Align direction);
    Align getTextAlignDirection() const { return mTextAlign;}

    QString getContentByTextName(const QString &name) const;

    QString htmlStrMakeAlign(const QString &html);

public:
    /**
     * @brief getBasicSourceText 获取初始化基本文本属性源文本
     * @param mark               代号文本
     * @param name               名称文本
     * @return
     */
    static QString getBasicSourceText(const QString &mark, const QString &name,
                                      bool showMark = true, bool showName = true);

    static QString getFullSourceText(QVector< QPair<QString, QString> > texts);

    static ADETextProperties::ADEItemTextShowState textStateFromStr(const QString &state);

private:
    QList<TextInfo> mTextInfos;
    QString mSrcText;
    Align mTextAlign;
};

#endif // ADETEXTPROPERTIES_H
