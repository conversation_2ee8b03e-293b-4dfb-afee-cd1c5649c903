﻿#include "equipmentelement.h"

#include <sstream>

#include "../diagram.h"
#include "../qetxml.h"
#include "../qetgraphicsitem/dynamicelementtextitem.h"
#include "../qetgraphicsitem/conductor.h"
#include "../factory/elementpicturefactory.h"
#include "../ElementsCollection/xmlelementcollection.h"
#include "../adetextproperties.h"
#include "../factory/elementfactory.h"

#include "layoutDiagram/elementresizer.h"
#include "interfaceelement.h"
#include "../equipmentrectitem.h"

EquipmentElement::EquipmentElement(const ElementsLocation &location,
                                   int *state, const QString &uuid) :
      Element(location, nullptr, state, Element::Simple, uuid)
{
    initADEStyle();

    for (auto dti : dynamicTextItems())
    {
        connect(dti, &DynamicElementTextItem::plainTextChanged, [=](){ dti->adjustSize();});
    }
}

EquipmentElement::~EquipmentElement()
{

}

bool EquipmentElement::fromXml(QDomElement& e, QHash<int, Terminal*>& table_id_adr)
{
    bool res = Element::fromXml(e, table_id_adr);

    if (res)
    {
        // 设置ADE文本显示
        for (auto dti : dynamicTextItems())
        {
            ADETextProperties textProp = ADETextProperties(dti->text());
            dti->setTextProperty(textProp);

            // 文本被修改后自动重新居中(新增的文本对象需重新连接槽函数)
            // Note: In order to align HTML text in the center, the item's text width must be set.
            // Otherwise, you can call adjustSize() after setting the item's text.
            connect(dti, &DynamicElementTextItem::plainTextChanged, [=](){
                dti->adjustSize();
            });
        }
    }

    return res;
}

QDomElement EquipmentElement::toXml(QDomDocument& document,
                                    QHash<Terminal*, int>& table_adr_id) const
{
    // 先在Element基类中创建xml元素, 再补充自定义信息
    QDomElement elmtXml = Element::toXml(document, table_adr_id);    

    return elmtXml;
}

QPainterPath EquipmentElement::shape() const
{
    QPainterPath path;
    path.addRoundRect(QRectF(QPointF(-hotspot_coord.x(), -hotspot_coord.y()), dimensions), 0);
    return path;
}

QVariant EquipmentElement::itemChange(GraphicsItemChange change, const QVariant& value)
{
    if (change == QGraphicsItem::ItemSelectedHasChanged)
    {
        if (value.toBool() == true)
            m_selected = true;
        else
            m_selected = false;

        if(mRectHandle)
        {
            if(!mRectHandle->getMousePressState())
            {
                scene()->removeItem(mRectHandle);
                mRectHandle = nullptr;
            }
        }
    }
    else if(change == QGraphicsItem::ItemSceneHasChanged)
    {
        if(!this->scene())
        {
            deleteHandlerItem();// 移除抓手
        }
    }
    else if (change == QGraphicsItem::ItemSelectedChange)
    {
        //qDebug() << Q_FUNC_INFO << "ItemSelectedChange";
    }
    else if (change == QGraphicsItem::ItemZValueHasChanged)
    {
        if (this->isSelected() && mRectHandle)
            mRectHandle->setZValue(this->zValue() + 1);
    }

    return Element::itemChange(change, value);
}

void EquipmentElement::mouseMoveEvent(QGraphicsSceneMouseEvent* event)
{
    // 当元素形状抓手被点击时禁止移动元素
    if (mRectHandle && mRectHandle->getMousePressState())
        return;

    // 移动
    Element::mouseMoveEvent(event);

    // 更新子元素导线
    for (Element* ele : childElements())
    {
        for (Conductor* c : ele->conductors())
        {
            c->updatePath();
        }
    }

    // 高亮所属位置矩形框
    diagram()->ade()->highlightEquipmentCompartment(m_uuid.toString(), true);
}

void EquipmentElement::mouseReleaseEvent(QGraphicsSceneMouseEvent* event)
{
    // 鼠标左键使元素移动时才执行后续操作
    if (event->button() != Qt::LeftButton)
        return;

    QetGraphicsItem::mouseReleaseEvent(event);
    update();

    // 取消高亮所属位置矩形框
    diagram()->ade()->highlightEquipmentCompartment(m_uuid.toString(), false);
}

void EquipmentElement::paint(QPainter *painter, const QStyleOptionGraphicsItem *option, QWidget *widget)
{
    Q_UNUSED(widget)

    painter->save();
    painter->setRenderHint(QPainter::Antialiasing, true);
    painter->setPen(mAdeStyle.getPen());
    painter->setBrush(mAdeStyle.getBrush());

    if (m_selected && !diagram()->isContentReadOnly())
    {
        QColor hoverColor = is_movable_ ? Qt::darkBlue : Qt::darkRed;

        painter->save();
        QColor color(hoverColor);
        color.setAlpha(50);
        painter -> setBrush (QBrush (color));
        painter -> setPen   (Qt::NoPen);

        QPainterPath path = shape();
        QPainterPathStroker pps;
        pps.setWidth(mAdeStyle.getPen().widthF() + 7.5);
        pps.setJoinStyle(Qt::RoundJoin);
        path = pps.createStroke(path);
        painter -> drawPath (path);
        painter -> restore  ();
    }

    if (mHighLight)
        drawHighlight(painter, option, QColor(69, 137, 255));

    if (option && option -> levelOfDetail < 1.0)
    {
        painter->drawPicture(0, 0, m_low_zoom_picture);
    }
    else
    {
        painter->drawPicture(0, 0, m_picture);
    }
    painter->restore();

    Element::paint(painter, option, widget);
}

void EquipmentElement::drawHighlight(QPainter *painter, const QStyleOptionGraphicsItem *, const QColor &color)
{
    painter->save();
    QColor c(color);
    // c.setAlpha(90);
    painter -> setBrush (QBrush (c));
    painter -> setPen   (Qt::NoPen);

    QPainterPath path = shape();
    QPainterPathStroker pps;
    pps.setWidth(mAdeStyle.getPen().widthF() + 4);
    pps.setJoinStyle(Qt::RoundJoin);
    path = pps.createStroke(path);
    painter -> drawPath (path);
    painter -> restore  ();
}

//Terminal *EquipmentElement::parseTerminal(const QDomElement &dom_element)
//{
//    qDebug() << Q_FUNC_INFO;
//    return Element::parseTerminal(dom_element);
//}

void EquipmentElement::initADEStyle()
{
    QPen pen;
    QBrush brush;

    bool hasAdeStyle = false;
    for (QDomNode node = m_location.xml().firstChild(); !node.isNull(); node = node.nextSibling())
    {
        QDomElement elmts = node.toElement();
        if (elmts.isNull())
            continue;

        if (elmts.nodeName() == AdeElementStyle::tagName())
        {
            hasAdeStyle = true;

            for (QDomNode childNode = elmts.firstChild(); !childNode.isNull(); childNode = childNode.nextSibling())
            {
                QDomElement childElmt = childNode.toElement();

                if (childElmt.tagName() == "pen")
                {
                    pen = QETXML::penFromXml(childElmt);
                }
                else if (childElmt.tagName() == "brush")
                {
                    brush = QETXML::brushFromXml(childElmt);
                }
            }
        }
    }

    if (!hasAdeStyle)
    {
        pen.setStyle(Qt::SolidLine);
        // 设备默认边框宽度
        pen.setWidthF(1.0);
        pen.setColor(Qt::black);

        brush.setColor(Qt::black);
        brush.setStyle(Qt::NoBrush);
    }

    AdeElementStyle style;
    style.setPen(pen);
    style.setBrush(brush);
    mAdeStyle = style;
}

QRectF EquipmentElement::boundingRect() const
{
    QPen pen = mAdeStyle.getPen();

    //去掉设备移动时重影
    // return(QRectF(QPointF(-hotspot_coord.x(), -hotspot_coord.y()),
    //               dimensions).adjusted(-pen.widthF(), -pen.widthF(), pen.widthF(), pen.widthF()));


    return shape().boundingRect().adjusted(-pen.widthF() - 7.5, -pen.widthF() - 7.5, pen.widthF() + 7.5, pen.widthF() + 7.5);

}

void EquipmentElement::setMovable(bool movable)
{
    if (mRectHandle)
    {
        QColor color = movable ? EquipmentRectItem::defaultColor() : Qt::red;
        mRectHandle->setColor(color);
    }

    Element::setMovable(movable);
}

void EquipmentElement::drawSelection(
        QPainter* painter,
        const QStyleOptionGraphicsItem* options)
{
    // 设备元素不显示选中效果
    Q_UNUSED(painter)
    Q_UNUSED(options)
    return;
}

QString EquipmentElement::adeMark() const
{
    if (dynamicTextItems().count())
    {
        ADETextProperties prop = dynamicTextItems().first()->getTextProp();
        return prop.getContentByTextName("代号");
    }
    return QString();
}

QString EquipmentElement::adeName() const
{
    if (dynamicTextItems().count())
    {
        ADETextProperties prop = dynamicTextItems().first()->getTextProp();
        return prop.getContentByTextName("名称");
    }
    return QString();
}

AdeElementStyle EquipmentElement::getAdeElementStyle()
{
    return mAdeStyle;
}

bool EquipmentElement::elementPatternLockState()
{
    return mPtternLocked;
}

void EquipmentElement::highlightByAdecompartmentItem(bool hl)
{
    mHighLight = hl;
}

void EquipmentElement::changeShapeSize(const QSize& size)
{
    QSizeF sizeF(size);

    int upwidth = size.width();
    int upheight = size.height();

    if (upwidth % 2)
    {
        upwidth += 1;
    }
    if (upheight % 2)
    {
        upheight += 1;
    }

    int upperLeftCornerXPos = -qRound((sizeF.width() / 2));
    int upperLeftCornerYPos = -qRound((sizeF.height() / 2));

    //更新location中的相关信息
    //<definition width、height hotspot_x hotspot_y/>
    pugi::xml_document docu = m_location.pugiXml();
    pugi::xml_node defNode = docu.child("definition");
    if(!defNode.empty())
    {
        defNode.attribute("width").set_value(QString("%1").arg(upwidth).toUtf8().data());
        defNode.attribute("height").set_value(QString("%1").arg(upheight).toUtf8().data());

        defNode.attribute("hotspot_x").set_value(QString("%1").arg(upwidth / 2).toUtf8().data());
        defNode.attribute("hotspot_y").set_value(QString("%1").arg(upheight / 2).toUtf8().data());
    }

    //<definition/description/rect x y width height/>
    pugi::xml_node rectNode = docu.child("definition").child("description").child("rect");

    if(!rectNode.empty())
    {
        rectNode.attribute("x").set_value(QString("%1").arg(upperLeftCornerXPos).toUtf8().data());
        rectNode.attribute("y").set_value(QString("%1").arg(upperLeftCornerYPos).toUtf8().data());

        rectNode.attribute("width").set_value(QString("%1").arg(sizeF.width()).toUtf8().data());
        rectNode.attribute("height").set_value(QString("%1").arg(sizeF.height()).toUtf8().data());
    }

    std::stringstream m_string_stream;
    docu.save(m_string_stream);

    if(QETProject* prj = m_location.project())
    {
        QDomDocument qdoc;
        qdoc.setContent(QString(m_string_stream.str().c_str()));

        QDomElement tmpEle = qdoc.createElement("element");
        tmpEle.setAttribute("name", QString("%1").arg(m_location.fileName()));
        tmpEle.appendChild(qdoc.firstChildElement("definition"));

        m_location = prj->embeddedElementCollection()->replaceElementDefinition(m_location, tmpEle);
    }

    int w = upwidth, h = upheight, hot_x = upwidth / 2, hot_y = upheight / 2;

    setSize(w, h);
    setHotspot(QPoint(hot_x, hot_y));

    //重新生成picture
    ElementPictureFactory* epf = ElementPictureFactory::instance();
    epf->getPictures(m_location,
                     const_cast<QPicture&>(m_picture),
                     const_cast<QPicture&>(m_low_zoom_picture));

    setPrefix(autonum::elementPrefixForLocation(m_location));

    //重绘
    this->prepareGeometryChange();
    this->update();
    this->diagram()->update();

    if (mRectHandle)
    {
        deleteHandlerItem();// 移除抓手
        createHandlerItem();// 重新创建抓手
    }
}

void EquipmentElement::changeLocation(const ElementsLocation &newLocation)
{
    Element::changeLocation(newLocation);

    if(mRectHandle)
    {
        deleteHandlerItem();// 移除抓手
        createHandlerItem();// 重新创建抓手
    }
}

void EquipmentElement::changeElementStyle(AdeElementStyle &style)
{
    if (mPtternLocked)
        return;

    //更新location中的相关信息
    QDomDocument doc;

    QDomElement oldElmt = doc.createElement("element");
    oldElmt.setAttribute("name", QString("%1").arg(m_location.fileName()));

    QDomElement oldXml = m_location.xml();
    QDomElement adeStyle = style.toXml(doc);
    oldXml.removeChild(oldXml.firstChildElement(AdeElementStyle::tagName()));
    QDomElement desp = oldXml.firstChildElement("description");
    oldXml.insertBefore(adeStyle, desp);

    oldElmt.appendChild(oldXml);

    if(QETProject* prj = m_location.project())
    {
        m_location = prj->embeddedElementCollection()->replaceElementDefinition(m_location, oldElmt);
    }

    //重新生成picture
    ElementPictureFactory* epf = ElementPictureFactory::instance();
    epf->getPictures(m_location,
                     const_cast<QPicture&>(m_picture),
                     const_cast<QPicture&>(m_low_zoom_picture));

    setPrefix(autonum::elementPrefixForLocation(m_location));

    //重绘
    this->prepareGeometryChange();
    this->update();
    this->diagram()->update();

    mAdeStyle = style;
}

void EquipmentElement::createHandlerNode()
{
    if(this->isSelected())
    {
        createHandlerItem();
    }
}

void EquipmentElement::tryRotateHandlerItem()
{
    // hzh 20230417
    if(mRectHandle )
        mRectHandle->setRotation(rotation());
    else
        createHandlerItem();
}

void EquipmentElement::removeHandlerNode()
{
    if(mRectHandle)
        deleteHandlerItem();
}

void EquipmentElement::showStretchHandler(bool show)
{
    if(!mRectHandle)
    {
        createHandlerItem();
    }
    if(mRectHandle)
    {
        mRectHandle->enableStretchHandler(show);
    }
}

void EquipmentElement::createHandlerItem()
{
    // hzh 20230414
    if(diagram())
    {
        QList <QGraphicsView *> views = diagram()->views();
        if(views.isEmpty()) return ;
        QGraphicsView *view = views.first();
        if(!view->isInteractive()){
            return ;
        }
    }
    else
    {
        return ;
    }

    if(!mRectHandle)
    {
        QSize sizeT = size();

        QRect rect = QRect(QPoint(0, 0), sizeT);

        // 计算当前元素的最小缩放尺寸
        QSize minSize = ElementResizer::getInstance()->getDeviceElementMinimumSize(this);
        QColor color = is_movable_ ? EquipmentRectItem::defaultColor() : Qt::red;
        mRectHandle = new EquipmentRectItem(rect, color, minSize);
        connect(this, &EquipmentElement::destroyed, mRectHandle, &EquipmentRectItem::deleteLater);
        connect(mRectHandle, &EquipmentRectItem::popFinishMoveRectByScene, this, &EquipmentElement::elementResized);
        connect(mRectHandle, &EquipmentRectItem::popIsPress, this, &EquipmentElement::setIsMove);
        scene()->addItem(mRectHandle);

        QPointF posT = QPointF(pos().x() - sizeT.width() / 2, pos().y() - sizeT.height() / 2);
        mRectHandle->setZValue(this->zValue() + 1);
        mRectHandle->setPos(posT);
        mRectHandle->setTransformOriginPoint(pos() - mRectHandle->pos());
        mRectHandle->setRotation(rotation());
    }
}

void EquipmentElement::deleteHandlerItem()
{
    if(mRectHandle)
    {
        delete mRectHandle;
        mRectHandle = nullptr;
    }
}

void EquipmentElement::onUpdateHandlerState()
{
    if(mRectHandle )
    {
        if(mRectHandle->getMousePressState() && !m_origin_pos.isNull() && pos() != m_origin_pos)
        {
            setPos(m_origin_pos);
        }
        else
        {
            QPointF posT = QPointF(pos().x() - size().width() / 2, pos().y() - size().height() / 2);
            if(posT != mRectHandle->pos())
            {
                mRectHandle->setPos(posT);
            }
        }
    }
}

void EquipmentElement::setIsMove(bool isPress)
{
    if(isPress)
    {
        m_origin_pos = pos();
    }
}

void EquipmentElement::onElementPoseChanged()
{
    onUpdateHandlerState();
    for (Element *child : childElements())
        child->sgScenePoseChanged();

    Element::onElementPoseChanged();
}
