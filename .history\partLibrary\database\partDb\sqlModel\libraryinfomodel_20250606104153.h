#ifndef LIBRARYINFOMODEL_H
#define LIBRARYINFOMODEL_H

//============================================================================
/// file
/// brief      数据库信息数据模型
/// author     wm
/// version    1.0
/// date       2025/06/05
/// todo
//============================================================================

#include "sqlModelBase.h"
#include <memory>
#include <QUuid>
#include <QDateTime>

class LibraryInfoModel : public SqlModelBase
{
public:
    LibraryInfoModel() : id(QUuid::createUuid().toString()), isOfficial(0){}

public:
    // 模型属性
    QString id;                 ///< 库uuid
    QString libraryName;        ///< 库名称
    QString version;            ///< 版本
    int     isOfficial;         ///< 是否官方库
    QString description;        ///< 描述
    QDateTime creationTimestamp;    ///< 创建时间
    QString createdBy;          ///< 创建者
    QDateTime lastModifiedTimestamp;///< 最后修改时间

public:
    QString tableName()       const override {return "LibraryInfo";}
    QString primaryKeyField() const override {return "LibraryUUID";}
    QVariantMap modelToDbMap() const override;
    void dbToModel(const QSqlRecord& record) override;

public:
    // 字段名称常量
    static const QString FIELD_ID;
    static const QString FIELD_LIBRARY_NAME;
    static const QString FIELD_VERSION;
    static const QString FIELD_IS_OFFICIAL;
    static const QString FIELD_DESCRIPTION;
    static const QString FIELD_CREATION_TIMESTAMP;
    static const QString FIELD_CREATED_BY;
    static const QString FIELD_LAST_MODIFIED_TIMESTAMP;
    static const QString FIELD_IS_DELETED;

    static std::shared_ptr<LibraryInfoModel> createPtr()
    {
        return std::make_shared<LibraryInfoModel>();
    }
};

using LibraryInfoModelPtr = std::shared_ptr<LibraryInfoModel>;
using LibraryInfoModelList = QHash<QString, LibraryInfoModelPtr>;

#endif // LIBRARYINFOMODEL_H 