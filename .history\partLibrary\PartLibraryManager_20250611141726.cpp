#include "PartLibraryManager.h"
#include <QDir>
#include <QFileInfo>
#include <QDebug>
#include <QUuid>
#include <QSqlError>
#include <QThread>

// Include all required Ctrl headers
#include "database/globalDb/sqlCtrl/manufacturerctrl.h"
#include "database/globalDb/sqlCtrl/categoriesctrl.h"
#include "database/globalDb/sqlCtrl/attributedefinitionsctrl.h"
#include "database/globalDb/sqlCtrl/categoryattributelinksctrl.h"
#include "database/partDb/sqlCtrl/libraryinfoctrl.h"
#include "database/partDb/sqlCtrl/partctrl.h"
#include "database/partDb/sqlCtrl/datasheetsctrl.h"
#include "database/partDb/sqlCtrl/partdatasheetlinksctrl.h"
#include "database/partDb/sqlCtrl/partimagesctrl.h"
#include "database/partDb/sqlCtrl/changelogctrl.h"

#include "database/partDb/viewModel/PartViewModelAssembler.h"
#include "database/SimpleDbManager.h"

#include "3rdParty/logBusiness/logbusiness.h"
#include "ade.h"

QString PartLibraryManager::GlobalDbFolder = "global";
QString PartLibraryManager::GlobalDbName   = "global_data.db";
QString PartLibraryManager::TrashFolder    = ".trash";
QString PartLibraryManager::LOGCAT         = "[元器件库]";

// 构造函数：无需实现，智能指针默认初始化为 nullptr
PartLibraryManager::PartLibraryManager(QObject *parent) : QObject(parent)
    , mInitialized(false)
{

}

// 析构函数：无需实现，智能指针会自动释放其管理的所有对象
PartLibraryManager::~PartLibraryManager()
{
}

PartLibraryPtr PartLibraryManager::getPartLibrary(const QString &id) const
{
    return m_partLibraries.value(id);
}

PartLibraryPtr PartLibraryManager::officialPartLibrary() const
{
    for (const auto& libContext : m_partLibraries) {
        if (libContext && libContext->cachedInfo && libContext->cachedInfo->isOfficial) {
            return libContext;
        }
    }
    return PartLibraryPtr();
}

PartLibraryManager* PartLibraryManager::instance()
{
    static PartLibraryManager inst;
    return &inst;
}

QString PartLibraryManager::rootPath()
{
    // 假设 Ade::partLibraryFolderPath() 返回正确的根路径
    return Ade::partLibraryFolderPath();
}

QString PartLibraryManager::partLibSuffix()
{
    return "ptlib";
}

bool PartLibraryManager::initialize(QString &err)
{
    if (mInitialized)
    {
        err = QString("元器件库已经初始化");
        return false;
    }

    m_partLibraries.clear();
    m_globalContext.reset();
    mInitialized = true;

    QString path = rootPath();

    if (path.isEmpty()) {
        err = "Root path is not set.";
        LOG_ERROR(LOGCAT + err);
        return false;
    }
    QDir rootDir(path);
    if (!rootDir.exists()) {
        LOG_WARNING(LOGCAT + "Part library root path does not exist, attempting to create it: " + path);
        if (!rootDir.mkpath(".")) {
            err = "Failed to create part library root directory: " + path;
            LOG_ERROR(LOGCAT + err);
            return false;
        }
    }

    // 检查并创建 trash 文件夹
    QString trashPath = QDir(path).filePath(TrashFolder);
    QDir trashDir(trashPath);
    if (!trashDir.exists()) {
        LOG_INFO(LOGCAT + "Trash folder does not exist, attempting to create it: " + trashPath);
        if (!trashDir.mkpath(".")) {
            err = "Failed to create trash directory: " + trashPath;
            LOG_ERROR(LOGCAT + err);
            return false;
        }
        LOG_INFO(LOGCAT + "Successfully created trash directory: " + trashPath);
    }

    // 1. 加载全局库
    QString globalDbPath = QDir(path).filePath(GlobalDbFolder + "/" + GlobalDbName);
    if (!loadGlobalLibrary(globalDbPath, err)) {
        LOG_ERROR(LOGCAT + "Failed to load global library: " + err);
        return false;
    }

    // 2. 发现并加载所有零件库
    for (const QFileInfo& dirInfo : rootDir.entryInfoList(QDir::Dirs | QDir::NoDotAndDotDot))
    {
        if (dirInfo.fileName() == GlobalDbFolder) continue; // 跳过全局库目录
        if (dirInfo.fileName() == TrashFolder)    continue; // 跳过回收站目录

        QDir libDir(dirInfo.absoluteFilePath());
        for (const QFileInfo& fileInfo : libDir.entryInfoList(QStringList() << QString("*.%1").arg(partLibSuffix()), QDir::Files))
        {
            if (!loadPartLibrary(fileInfo.absoluteFilePath(), err)) {
                LOG_WARNING(LOGCAT + QString("Failed to load part library '%1', error: %2. Skipping.").arg(fileInfo.absoluteFilePath(), err));
            }
        }
    }

    LOG_INFO(LOGCAT + "PartLibraryManager initialized, loaded " + QString::number(m_partLibraries.count()) + " part libraries.");

    // 当只有一个官方零件库时，自动创建一个非官方零件库
    int officialCount = 0;
    int userCount = 0;
    for (const auto& libContext : m_partLibraries) {
        if (libContext && libContext->cachedInfo) {
            if (libContext->cachedInfo->isOfficial) {
                officialCount++;
            } else {
                userCount++;
            }
        }
    }

    if (officialCount == 0)
    {
        LOG_WARNING(LOGCAT + "缺少官方元件库");
    }

    if (userCount == 0) { // 确保至少有一个官方库，且没有用户库
        QString defaultNewLibName = "UserLibrary";
        QString creationError;
        QString newLibId = createNewPartLibrary(defaultNewLibName, creationError);
        if (newLibId.isEmpty()) {
            LOG_WARNING(LOGCAT + QString("Failed to automatically create a user library. Error: %1").arg(creationError));
        } else {
            LOG_INFO(LOGCAT + QString("Automatically created a new user library: %1 (ID: %2)").arg(defaultNewLibName, newLibId));
        }
    }

    return true;
}

void PartLibraryManager::shutdown()
{
    // 当 unique_ptr 从哈希表中移除时，其析构函数会自动被调用，
    // 从而 delete PartLibraryContext，并关闭其内部的数据库连接（如果需要手动关闭的话）。
    m_partLibraries.clear();

    // 重置全局上下文的 unique_ptr，其管理的对象的析构函数也会被调用。
    if (m_globalContext) {
        QString connectionName = m_globalContext->db.connectionName();
        // 必须先关闭数据库连接，再reset智能指针
        if (m_globalContext->db.isOpen()) {
            SimpleDbManager::close(connectionName);
        }
        m_globalContext.reset();
        LOG_INFO("Global library connection closed: " + connectionName);
    }
}

QString PartLibraryManager::createNewPartLibrary(const QString& libraryName, QString& errorInfo)
{
    QString directoryPath = QDir(rootPath()).filePath(libraryName);

    QDir dir(directoryPath);
    if (!dir.exists()) {
        if (!dir.mkpath(".")) {
            errorInfo = QString("无法创建目录: %1").arg(directoryPath);
            return QString();
        }
    }

    QString libFileName = libraryName + "." + partLibSuffix();
    QString filePath = dir.filePath(libFileName);

    if (QFile::exists(filePath)) {
        errorInfo = QString("库文件已存在: %1").arg(filePath);
        return QString();
    }

    // 使用 unique_ptr 进行自动资源管理
    auto context = QSharedPointer<PartLibraryContext>::create();
    context->filePath = filePath;

    QString connectionName = filePath;
    QSqlError dbError;
    if (!SimpleDbManager::connect(filePath, dbError, connectionName, context->db)) {
        errorInfo = QString("无法创建或连接到新的数据库文件: %1. 错误: %2").arg(filePath, dbError.text());
        return QString(); // 内存自动释放
    }

    // 实例化所有控制器
    context->libraryInfoCtrl = QSharedPointer<LibraryInfoCtrl>::create(context->db);
    context->partCtrl = QSharedPointer<PartCtrl>::create(context->db);
    context->datasheetsCtrl = QSharedPointer<DatasheetsCtrl>::create(context->db);
    context->partDatasheetLinksCtrl = QSharedPointer<PartDatasheetLinksCtrl>::create(context->db);
    context->partImagesCtrl = QSharedPointer<PartImagesCtrl>::create(context->db);
    context->changeLogCtrl = QSharedPointer<ChangeLogCtrl>::create(context->db);

    QList<SqlCtrlBase*> ctrls = {
        context->libraryInfoCtrl.data(), context->partCtrl.data(), context->datasheetsCtrl.data(),
        context->partDatasheetLinksCtrl.data(), context->partImagesCtrl.data(), context->changeLogCtrl.data()
    };
    for (auto* ctrl : ctrls) {
        if (!checkAndCreateTable(ctrl, errorInfo)) {
            SimpleDbManager::close(connectionName);
            QFile::remove(filePath); // 清理失败的文件
            return QString(); // 内存自动释放
        }
    }

    // 创建并写入初始 LibraryInfo
    auto info = LibraryInfoModel::createPtr();
    info->id = QUuid::createUuid().toString();
    info->libraryName = libraryName;
    info->version = "1.0.0";
    info->isOfficial = false;
    info->creationTimestamp = QDateTime::currentDateTime();
    info->lastModifiedTimestamp = info->creationTimestamp;
    info->createdBy = Ade::computerName();

    if (!context->libraryInfoCtrl->insert(info)) {
        errorInfo = "无法向新库中写入元信息(LibraryInfo)。";
        SimpleDbManager::close(connectionName);
        QFile::remove(filePath);
        return QString(); // 内存自动释放
    }

    context->libraryId = info->id;
    context->cachedInfo = info; // 缓存信息

    // 使用 std::move 转移所有权到哈希表
    QString newId = info->id;
    m_partLibraries.insert(newId, std::move(context));

    LOG_INFO("Successfully created new part library: " + libraryName + " with ID: " + newId);
    return newId;
}

bool PartLibraryManager::deletePartLibrary(const QString& libraryId)
{
    if (!m_partLibraries.contains(libraryId)) {
        return false;
    }

    // 从哈希表中移除 unique_ptr。当指针离开这个作用域时，它所管理的 context 会被自动销毁。
    // 销毁 context 会自动销毁其内部的所有 unique_ptr<Ctrl>。
    QSharedPointer<PartLibraryContext> context = m_partLibraries.take(libraryId);
    QString filePath = context->filePath;

    // 必须先关闭数据库，再移动文件
    context->db.close();
    SimpleDbManager::close(context->db.connectionName());
    context.reset(); // 确保在移动文件前，所有资源已释放

    // 获取库文件所在的目录（即库文件夹）
    QFileInfo fileInfo(filePath);
    QDir libraryDir = fileInfo.dir();
    QString libraryDirPath = libraryDir.absolutePath();
    QString libraryDirName = libraryDir.dirName();

    // 构建 trash 目录路径
    QString trashPath = QDir(rootPath()).filePath(TrashFolder);
    QDir trashDir(trashPath);

    // 生成带时间戳的目标文件夹名称，格式为：[libraryDirName]_[YYYYMMDDHHMMSS]
    QString timestamp = QDateTime::currentDateTime().toString("yyyyMMddhhmmss");
    QString targetDirName = QString("%1_%2").arg(libraryDirName, timestamp);
    QString targetDirPath = trashDir.filePath(targetDirName);

    // 移动整个库文件夹到 trash 目录
    if (libraryDir.rename(libraryDirPath, targetDirPath)) {
        LOG_INFO(QString("Successfully moved library folder to trash: %1 -> %2").arg(libraryDirPath, targetDirPath));
        return true;
    } else {
        QString errorMsg = QString("Failed to move library folder to trash: %1 -> %2").arg(libraryDirPath, targetDirPath);
        LOG_ERROR(errorMsg);

        // 额外检查文件夹是否存在或权限问题
        QFileInfo dirInfo(libraryDirPath);
        LOG_ERROR(QString(
                      "Move failed. Source path: %1\n"
                      "Exists: %2, Readable: %3, Writable: %4\n"
                      "Target path: %5\n"
                      "Target parent exists: %6"
                      ).arg(
                          libraryDirPath,
                          dirInfo.exists() ? "Yes" : "No",
                          dirInfo.isReadable() ? "Yes" : "No",
                          dirInfo.isWritable() ? "Yes" : "No",
                          targetDirPath,
                          trashDir.exists() ? "Yes" : "No"
                          ));

        return false;
    }
}

QList<PartLibraryPtr> PartLibraryManager::getAllPartLibraries() const
{
    QList<PartLibraryPtr> result;
    result.reserve(m_partLibraries.size());

    // 先收集所有库
    for (const auto& context : m_partLibraries) {
        if (context) {
            result.append(context);
        }
    }

    // 自定义排序
    std::sort(result.begin(), result.end(), [](const PartLibraryPtr& a, const PartLibraryPtr& b) {
        // 先比较是否官方库，官方库排在前面
        if (a->cachedInfo->isOfficial != b->cachedInfo->isOfficial) {
            return a->cachedInfo->isOfficial > b->cachedInfo->isOfficial;
        }
        // 如果都是官方库或都不是官方库，则比较创建时间
        return a->cachedInfo->creationTimestamp > b->cachedInfo->creationTimestamp;
    });

    return result;
}

PartViewModel PartLibraryManager::getPartViewModel(const QString& libraryId, const QString& partId)
{
    if (!m_partLibraries.contains(libraryId)) {
        LOG_WARNING("getPartViewModel called with invalid libraryId: " + libraryId);
        return PartViewModel();
    }

    const auto& partContext = m_partLibraries.value(libraryId);
    PartModelPtr partModel = partContext->partCtrl->fetch(partId);
    if (!partModel) {
        return PartViewModel();
    }

    if (!m_globalContext || !m_globalContext->attributeDefinitionsCtrl) {
        LOG_ERROR("Global context is not initialized, cannot create PartViewModel.");
        return PartViewModel();
    }

    return PartViewModelAssembler::createViewModel(partModel, *m_globalContext->attributeDefinitionsCtrl);
}

PartModelList PartLibraryManager::getPartsInLibrary(const QString& libraryId) const
{
    if (!m_partLibraries.contains(libraryId)) {
        LOG_WARNING("getPartsInLibrary called with invalid libraryId: " + libraryId);
        return PartModelList();
    }
    return m_partLibraries.value(libraryId)->partCtrl->fetchAll();
}

QList<ManufacturerModelPtr> PartLibraryManager::getManufacturers() const
{
    if (m_globalContext && m_globalContext->manufacturerCtrl) {
        return m_globalContext->manufacturerCtrl->orderedList();
    }
    return QList<ManufacturerModelPtr>();
}

QList<CategoriesModelPtr> PartLibraryManager::getCategories() const
{
    if (m_globalContext && m_globalContext->categoriesCtrl) {
        return m_globalContext->categoriesCtrl->orderedList();
    }
    return QList<CategoriesModelPtr>();
}

bool PartLibraryManager::loadGlobalLibrary(const QString& globalDbPath, QString &err)
{
    m_globalContext = QSharedPointer<GlobalLibraryContext>::create();

    QFileInfo globalDbInfo(globalDbPath);
    if (!globalDbInfo.dir().exists()) {
        if (!globalDbInfo.dir().mkpath(".")) {
            err = QString("无法创建全局数据库目录: %1").arg(globalDbInfo.dir().path());
            LOG_ERROR(err);
            m_globalContext.reset();
            return false;
        }
    }

    QString connectionName = "global_db_connection";
    QSqlError error;
    if (!SimpleDbManager::connect(globalDbPath, error, connectionName, m_globalContext->db)) {
        err = QString("无法连接或创建全局数据库 '%1': %2").arg(globalDbPath, error.text());
        LOG_ERROR(err);
        m_globalContext.reset(); // 连接失败，释放对象
        return false;
    }

    m_globalContext->manufacturerCtrl = QSharedPointer<ManufacturerCtrl>::create(m_globalContext->db);
    m_globalContext->categoriesCtrl = QSharedPointer<CategoriesCtrl>::create(m_globalContext->db);
    m_globalContext->attributeDefinitionsCtrl = QSharedPointer<AttributeDefinitionsCtrl>::create(m_globalContext->db);
    m_globalContext->categoryAttributeLinksCtrl = QSharedPointer<CategoryAttributeLinksCtrl>::create(m_globalContext->db);

    QList<SqlCtrlBase*> ctrls = {
        m_globalContext->manufacturerCtrl.data(), m_globalContext->categoriesCtrl.data(),
        m_globalContext->attributeDefinitionsCtrl.data(), m_globalContext->categoryAttributeLinksCtrl.data()
    };
    for (auto* ctrl : ctrls) {
        if (!checkAndCreateTable(ctrl, err)) {
            SimpleDbManager::close(connectionName);
            m_globalContext.reset();
            return false;
        }
    }

    LOG_INFO("Global library loaded successfully from " + globalDbPath);
    return true;
}

bool PartLibraryManager::loadPartLibrary(const QString& ptlibPath, QString &err)
{
    auto context = QSharedPointer<PartLibraryContext>::create();
    context->filePath = ptlibPath;

    QString connectionName = ptlibPath;
    QSqlError error;
    if (!SimpleDbManager::connect(ptlibPath, error, connectionName, context->db)) {
        err = QString("无法连接到零件库 '%1': %2").arg(ptlibPath, error.text());
        return false; // 内存自动释放
    }

    context->libraryInfoCtrl = QSharedPointer<LibraryInfoCtrl>::create(context->db);
    context->partCtrl = QSharedPointer<PartCtrl>::create(context->db);
    context->datasheetsCtrl = QSharedPointer<DatasheetsCtrl>::create(context->db);
    context->partDatasheetLinksCtrl = QSharedPointer<PartDatasheetLinksCtrl>::create(context->db);
    context->partImagesCtrl = QSharedPointer<PartImagesCtrl>::create(context->db);
    context->changeLogCtrl = QSharedPointer<ChangeLogCtrl>::create(context->db);

    QList<SqlCtrlBase*> ctrls = {
        context->libraryInfoCtrl.data(), context->partCtrl.data(), context->datasheetsCtrl.data(),
        context->partDatasheetLinksCtrl.data(), context->partImagesCtrl.data(), context->changeLogCtrl.data()
    };
    for (auto* ctrl : ctrls) {
        if (!checkAndCreateTable(ctrl, err)) {
            SimpleDbManager::close(connectionName);
            return false; // 内存自动释放
        }
    }

    LibraryInfoModelPtr info = context->libraryInfoCtrl->fetch();
    if (!info) {
        err = QString("无法从 '%1' 读取库信息。它可能不是一个有效的库文件。").arg(ptlibPath);
        SimpleDbManager::close(connectionName);
        return false;
    }
    context->libraryId = info->id;
    context->cachedInfo = info; // 缓存信息

    if (m_partLibraries.contains(context->libraryId)) {
        err = QString("发现重复的库ID: %1。跳过文件 '%2'。").arg(context->libraryId, ptlibPath);
        SimpleDbManager::close(connectionName);
        return false;
    }

    m_partLibraries.insert(context->libraryId, std::move(context));
    LOG_INFO("Loaded part library: " + info->libraryName + " (" + info->id + ") from " + ptlibPath);

    return true;
}

bool PartLibraryManager::checkAndCreateTable(SqlCtrlBase *sqlCtrl, QString &err)
{
    if (!sqlCtrl->isTableExist()) {
        QSqlError sqlErr;
        if (!sqlCtrl->createTable(sqlErr)) {
            err = QString("无法创建表 '%1': %2").arg(sqlCtrl->tableName(), sqlErr.text());
            LOG_ERROR(err);
            return false;
        }
    }
    return true;
}

QString PartLibraryContext::dataSheetsFolder() const
{
    QFileInfo fileInfo(filePath);
    return QDir(fileInfo.dir()).filePath("DataSheets");
}
