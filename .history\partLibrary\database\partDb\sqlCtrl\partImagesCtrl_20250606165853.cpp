#include "partImagesCtrl.h"
#include <QSqlQuery>
#include <QVariant>

PartImagesCtrl::PartImagesCtrl(const QSqlDatabase &db) : SqlCtrlBase(db)
{
}

bool PartImagesCtrl::add(const PartImageModel &image)
{
    return insertTableRecord(image);
}

bool PartImagesCtrl::update(const PartImageModel &image)
{
    return updateTableRecord(image);
}

bool PartImagesCtrl::remove(const QString &imageUuid)
{
    return deleteTableRecord(imageUuid);
}

bool PartImagesCtrl::softDelete(const QString &imageUuid)
{
    QSqlQuery query(mDatabase);
    QString sql = QString("UPDATE %1 SET %2 = 1 WHERE %3 = :id")
                      .arg(tableName(), PartImageModel::FIELD_IS_DELETED, primaryKeyField());
    query.prepare(sql);
    query.bindValue(":id", imageUuid);
    return _executeQuery(query, { {":id", imageUuid} }, "SOFT_DELETE");
}

PartImageModel PartImagesCtrl::getImage(const QString &imageUuid) const
{
    PartImageModel image;
    QSqlQuery query(mDatabase);
    QString sql = QString("SELECT * FROM %1 WHERE %2 = :id AND %3 = 0")
                      .arg(tableName(), primaryKeyField(), PartImageModel::FIELD_IS_DELETED);
    query.prepare(sql);
    query.bindValue(":id", imageUuid);

    if (query.exec() && query.next())
    {
        image.dbToModel(query.record());
    }
    return image;
}

QList<PartImageModel> PartImagesCtrl::getImagesForPart(const QString &partUuid) const
{
    QList<PartImageModel> images;
    QSqlQuery query(mDatabase);
    QString sql = QString("SELECT * FROM %1 WHERE %2 = :part_uuid AND %3 = 0 ORDER BY %4")
                      .arg(tableName(),
                           PartImageModel::FIELD_PART_UUID,
                           PartImageModel::FIELD_IS_DELETED,
                           PartImageModel::FIELD_DISPLAY_ORDER);
    query.prepare(sql);
    query.bindValue(":part_uuid", partUuid);

    if (query.exec())
    {
        while (query.next())
        {
            PartImageModel image;
            image.dbToModel(query.record());
            images.append(image);
        }
    }
    return images;
}

bool PartImagesCtrl::recordExist(const QString &id) const
{
    QSqlQuery query(mDatabase);
    query.prepare(QString("SELECT COUNT(*) FROM %1 WHERE %2 = :id").arg(tableName(), primaryKeyField()));
    query.bindValue(":id", id);
    if (query.exec() && query.next())
    {
        return query.value(0).toInt() > 0;
    }
    return false;
}

QString PartImagesCtrl::tableSql() const
{
    return R"(
        CREATE TABLE PartImages (
            ImageUUID       TEXT PRIMARY KEY,
            PartUUID        TEXT NOT NULL,
            Pixmap          BLOB NOT NULL,
            ImageType       TEXT,
            DisplayOrder    INTEGER DEFAULT 0,
            Description     TEXT,
            IsDeleted       INTEGER NOT NULL DEFAULT 0
        );
    )";
} 