#include "categoriesctrl.h"

#include "3rdParty/logBusiness/logbusiness.h"

#include <QApplication>

CategoriesCtrl::CategoriesCtrl(const QSqlDatabase &db) :
    SqlCtrlBase(db)
{
    if(!db.isValid())
    {
        LOG_ERROR("db not valid");
    }
    else
    {
        loadTable();
    }
}

CategoriesCtrl::~CategoriesCtrl()
{
    for(auto itor = mAllObj.begin(); itor != mAllObj.end(); )
    {
        auto obj = itor.value();
        obj.reset();

        mAllObj.erase(itor++);
    }
}

CategoriesModelList CategoriesCtrl::fetchAll() const
{
    return mAllObj;
}

CategoriesModelPtr CategoriesCtrl::fetch(const QString &id) const
{
    if (modelExist(id))
    {
        return mAllObj.value(id);
    }
    else
    {
        return nullptr;
    }
}

QList<CategoriesModelPtr> CategoriesCtrl::orderedList() const
{
    QList<CategoriesModelPtr> orderedList;
    for (auto itor = mAllObj.begin(); itor != mAllObj.end(); ++itor) {
        orderedList.append(itor.value());
    }
    std::sort(orderedList.begin(), orderedList.end(), [](const CategoriesModelPtr &a, const CategoriesModelPtr &b) {
        return a->name < b->name;
    });
    return orderedList;
}

QList<CategoriesModelPtr> CategoriesCtrl::topCategories() const
{
    QList<CategoriesModelPtr> orderedList;
    for (auto itor = mAllObj.begin(); itor != mAllObj.end(); ++itor) {
        if (itor.value()->parentCategoryUUID.isEmpty())
            orderedList.append(itor.value());
    }
    std::sort(orderedList.begin(), orderedList.end(), [](const CategoriesModelPtr &a, const CategoriesModelPtr &b) {
        return a->name < b->name;
    });
    return orderedList;
}

bool CategoriesCtrl::modelExist(const QString &id) const
{
    return mAllObj.contains(id);
}

bool CategoriesCtrl::insert(const CategoriesModelPtr &obj)
{
    if (obj.get() == nullptr)
        return false;

    if (insertTableRecord(*obj.get()))
    {
        mAllObj.insert(obj->id, obj);
        return true;
    }
    else
    {
        return false;
    }
}

bool CategoriesCtrl::modify(const CategoriesModelPtr &newValueObj)
{
    if (newValueObj.get() == nullptr)
        return false;

    QString id = newValueObj->id;
    if (!mAllObj.contains(id))
    {
        LOG_ERROR(QString("无法修改记录：该记录不存在 %1").arg(id));
        return false;
    }

    if (updateTableRecord(*newValueObj.get()))
    {
        auto obj = mAllObj.value(id);
        obj->name = newValueObj->name;
        obj->description = newValueObj->description;
        obj->parentCategoryUUID = newValueObj->parentCategoryUUID;
        obj->irdi = newValueObj->irdi;
        obj->usageUnits = newValueObj->usageUnits;

        return true;
    }
    else
    {
        return false;
    }

}

bool CategoriesCtrl::remove(const QString &id)
{
    if (!mAllObj.contains(id))
    {
        LOG_ERROR(QString("无法删除记录：该记录不存在 %1").arg(id));
        return false;
    }

    if (deleteTableRecord(id))
    {
        auto obj = mAllObj.take(id);
        obj.reset();

        return true;
    }
    else
    {
        return false;
    }
}

bool CategoriesCtrl::recordExist(const QString &id) const
{
    return mAllObj.contains(id);
}

QString CategoriesCtrl::tableSql() const
{
    return QString(R"(
        CREATE TABLE Categories (
            CategoryUUID        TEXT PRIMARY KEY, -- [主键] 元器件分类的全局唯一标识符，UUID格式
            Name                TEXT NOT NULL, -- [字段] 分类名称，如"连接器"、"电阻"，必须唯一
            Description         TEXT,             -- [字段] 对该分类的详细文字描述
            ParentCategoryUUID  TEXT,             -- [外键-逻辑] 指向父分类的UUID，用于实现层级嵌套结构
            IRDI                TEXT,      -- [字段] 国际注册数据标识符 (IEC 61360)，用于实现与行业标准的对接
            UsageUnit_JSON      TEXT,    -- [字段] 用量单位
            IsDeleted           INTEGER NOT NULL DEFAULT 0 -- [字段] 软删除标志
        );
    )");
}

void CategoriesCtrl::loadTable()
{
    QString sql = QString("select * from %1 where IsDeleted='0'").arg(tableName());
    QSqlQuery query(getCurrentDatabase());
    if(query.exec(sql))
    {
        while (query.next())
        {
            QSqlRecord record = query.record();

            CategoriesModelPtr obj;
            obj.reset(new CategoriesModel);
            obj->dbToModel(record);

            mAllObj.insert(obj->id, obj);
        }
    }
} 
