#ifndef TEST_GLOBALDB_H
#define TEST_GLOBALDB_H

/**
 * @brief Runs all tests for the Global Database controllers.
 *
 * This function will create a temporary 'global_test.db' in the application's
 * directory, connect to it, and run a series of tests for:
 * - ManufacturerCtrl
 * - CategoriesCtrl
 * - AttributeDefinitionsCtrl
 * - CategoryAttributeLinksCtrl
 *
 * All test output is logged via qDebug/qWarning.
 */
void run_globalDb_tests();

#endif // TEST_GLOBALDB_H 