#CONFIG (release, release|debug) {
#    !build_pass:message( 'Enabling asmCrashReport and including debug symbols' )

#    DEFINES += ASM_CRASH_REPORT

#    HEADERS += \
#        $$PWD/asmCrashReport.h

#    SOURCES += \
#        $$PWD/asmCrashReport.cpp

#    win32-g++* {
#        QMAKE_CFLAGS_RELEASE -= -O2
#        QMAKE_CXXFLAGS_RELEASE -= -O2

#        QMAKE_CFLAGS_RELEASE += -g -O0
#        QMAKE_CXXFLAGS_RELEASE += -g -O0
#        QMAKE_LFLAGS_RELEASE =

#        LIBS += "-L$$PWD/Win/WinDebug" -lDbghelp
#    }
#}

#CONFIG (debug, release|debug) {
#    message( 'NOTE: asmCrashReport is only valid for release builds' )
#}
