#include "attributedefinitionsctrl.h"

#include "3rdParty/logBusiness/logbusiness.h"

#include <QApplication>

AttributeDefinitionsCtrl::AttributeDefinitionsCtrl(const QSqlDatabase &db) :
    SqlCtrlBase(db)
{
    if(!db.isValid())
    {
        LOG_ERROR("db not valid");
    }
    else
    {
        loadTable();
    }
}

AttributeDefinitionsCtrl::~AttributeDefinitionsCtrl()
{
    for(auto itor = mAllObj.begin(); itor != mAllObj.end(); )
    {
        auto obj = itor.value();
        obj.reset();

        mAllObj.erase(itor++);
    }
}

AttributeDefinitionsModelList AttributeDefinitionsCtrl::fetchAll() const
{
    return mAllObj;
}

AttributeDefinitionsModelPtr AttributeDefinitionsCtrl::fetch(const QString &id) const
{
    if (modelExist(id))
    {
        return mAllObj.value(id);
    }
    else
    {
        return nullptr;
    }
}

bool AttributeDefinitionsCtrl::modelExist(const QString &id) const
{
    return mAllObj.contains(id);
}

bool AttributeDefinitionsCtrl::insert(const AttributeDefinitionsModelPtr &obj)
{
    if (obj.get() == nullptr)
        return false;

    if (insertTableRecord(*obj.get()))
    {
        mAllObj.insert(obj->id, obj);
        return true;
    }
    else
    {
        return false;
    }
}

bool AttributeDefinitionsCtrl::modify(const AttributeDefinitionsModelPtr &newValueObj)
{
    if (newValueObj.get() == nullptr)
        return false;

    QString id = newValueObj->id;
    if (!mAllObj.contains(id))
    {
        LOG_ERROR(QString("无法修改记录：该记录不存在 %1").arg(id));
        return false;
    }

    if (updateTableRecord(*newValueObj.get()))
    {
        auto obj = mAllObj.value(id);
        obj->name = newValueObj->name;
        obj->dataType = newValueObj->dataType;
        obj->options = newValueObj->options;
        obj->description = newValueObj->description;
        obj->irdi = newValueObj->irdi;
        obj->unit = newValueObj->unit;
        obj->defaultValue = newValueObj->defaultValue;
        obj->validationRule = newValueObj->validationRule;
        obj->isUserDefined = newValueObj->isUserDefined;

        return true;
    }
    else
    {
        return false;
    }
}

bool AttributeDefinitionsCtrl::remove(const QString &id)
{
    if (!mAllObj.contains(id))
    {
        LOG_ERROR(QString("无法删除记录：该记录不存在 %1").arg(id));
        return false;
    }

    if (deleteTableRecord(id))
    {
        auto obj = mAllObj.take(id);
        obj.reset();

        return true;
    }
    else
    {
        return false;
    }
}

bool AttributeDefinitionsCtrl::recordExist(const QString &id) const
{
    return mAllObj.contains(id);
}

QString AttributeDefinitionsCtrl::tableSql() const
{
    return QString(R"(
        CREATE TABLE AttributeDefinitions (
            AttributeUUID     TEXT PRIMARY KEY,     -- [主键] 属性定义的全局唯一标识符，UUID格式
            Name              TEXT NOT NULL UNIQUE, -- [字段] 属性的程序内名称，如"PinCount"、"Resistance"，必须唯一
            DataType          TEXT NOT NULL,        -- [字段] 属性值的数据类型，用于UI生成和数据验证
            Options			  TEXT,                 -- [字段] 属性的可选值列表; 仅当dataType为"Enumeration"时有意义
            Description       TEXT,                 -- [字段] 属性的友好名称或详细描述，用于在UI上展示给用户
            IRDI              TEXT UNIQUE,          -- [字段] 该属性的国际标准ID，用于数据交换和标准化
            Unit              TEXT,                 -- [字段] 属性的物理单位，如 "V", "A", "mm", "Ohm"
            DefaultValue      TEXT,                 -- [字段] 创建元器件时，该属性的默认值
            ValidationRule    TEXT,                 -- [字段] 用于验证用户输入的规则，如正则表达式或Min/Max范围
            IsUserDefined     INTEGER NOT NULL DEFAULT 0, -- [字段] 标志位，0表示官方预定义属性，1表示用户自定义属性
            IsDeleted         INTEGER NOT NULL DEFAULT 0  -- [字段] 软删除标志
        );
    )");
}

void AttributeDefinitionsCtrl::loadTable()
{
    QString sql = QString("select * from %1 where IsDeleted='0'").arg(tableName());
    QSqlQuery query(getCurrentDatabase());
    if(query.exec(sql))
    {
        while (query.next())
        {
            QSqlRecord record = query.record();

            AttributeDefinitionsModelPtr obj;
            obj.reset(new AttributeDefinitionsModel);
            obj->dbToModel(record);

            mAllObj.insert(obj->id, obj);
        }
    }
} 
