﻿/*
	Copyright 2006-2021 The QElectroTech Team
	This file is part of QElectroTech.

	QElectroTech is free software: you can redistribute it and/or modify
	it under the terms of the GNU General Public License as published by
	the Free Software Foundation, either version 2 of the License, or
	(at your option) any later version.

	QElectroTech is distributed in the hope that it will be useful,
	but WITHOUT ANY WARRANTY; without even the implied warranty of
	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
	GNU General Public License for more details.

	You should have received a copy of the GNU General Public License
	along with QElectroTech.  If not, see <http://www.gnu.org/licenses/>.
*/
#ifndef INTERFACEELEMENT_H
#define INTERFACEELEMENT_H

#include "../qetgraphicsitem/element.h"

#include "ade.h"

class ADELogicCable;
class ADEConnectRelation;
class EquipmentElement;
class ElementEventDockInterface;

/**
* @file
* @brief      ADE接口元素
* <AUTHOR>
* @version    1.0
* @date       2023/09/25
* @todo
*/
class InterfaceElement : public Element
{
    Q_OBJECT

public:
    explicit InterfaceElement(const ElementsLocation &,
                              int * = nullptr,
                              Element* parentElement = nullptr,
                              const QString &uuid = QString());
    ~InterfaceElement() override;

private:
    InterfaceElement(const Element&);

public:
    enum { ADEType = 2 };
    int adeType() const override {return ADEType;}

public:
    bool        fromXml(QDomElement&, QHash<int,Terminal*>&) override;
    QDomElement toXml(QDomDocument&, QHash<Terminal*,int>&) const override;
    void paint(QPainter*, const QStyleOptionGraphicsItem*, QWidget*w) override;

    void changeLocation(const ElementsLocation &newLocation) override;

protected:
    void mousePressEvent(QGraphicsSceneMouseEvent* event) override;
    void mouseMoveEvent(QGraphicsSceneMouseEvent* event) override;
    void mouseReleaseEvent(QGraphicsSceneMouseEvent* event) override;
    void hoverEnterEvent(QGraphicsSceneHoverEvent*event) override;

private:
    void updateInterfaceType();
    /// 调整接口尺寸至合适的大小
    void adjustSize();

     // 属性
public:
    QString adeMark() const ;
    QString adeName() const ;

    QString adeFatherUuid() const {return mADEFatherUuid;}
    void    setADEFatherUuid(const QString &uuid);

    ADELogicCable *cable() const {return mCable;}
    void setCable(ADELogicCable *cable);

    ADEConnectRelation *connectRelation() const {return mConnectRelation;}
    void setConnectRelation(ADEConnectRelation *cr);

    ADE::Diagram::InterfaceElementType getADEInterfaceType() const {return m_interface_type;}

    /// 接口是否是引用接口
    bool isCitedInterface();
    /// 接口是否是直连设备上的接口
    bool isDirectConnectInterface() const;

    // 功能
public:
    bool canRotate() const override;
    void passiveTriggerMousePressEvent(QGraphicsSceneMouseEvent* event);
    void triggerQETMouseMoveEvent(QGraphicsSceneMouseEvent* event);
    void adjustTextPosition();
    void setSearchResultHighLight(bool hl);
    void updateDefinition(const QString &definition);

private:
    // (接口)元素移动位置是否合法
    bool isElementMoveValid(const QPointF& pos);
    void updateADEFatherUuid();

private:
    ElementEventDockInterface *mElementEvent    = nullptr;
    ADELogicCable             *mCable           = nullptr;
    ADEConnectRelation        *mConnectRelation = nullptr;

    QColor m_highLight_yellow = QColor(225, 225, 0);      ///< 元素高亮颜色

    QString mADEFatherUuid;     ///< 接口所属设备/组件/电缆的uuid
    ADE::Diagram::InterfaceElementType m_interface_type = ADE::Diagram::InterfaceElementType::IETNull;

    bool mActiveTriggerEvent = true;        ///< 事件是否主动触发
    bool m_searchResulr_highlight = false;

    // 接口在设备上移动时
    bool m_first_move = true;               ///< 拖拽时是否为元素首次移动，用于判断是否应设置父元素高亮
    QPair<QPointF, int> m_first_move_pos;   ///< 鼠标开始拖拽时元素的位置
    bool m_moving_state = false;            ///< 元素移动状态
    bool m_move_valid = false;              ///< 元素移动后的位置是否合法
    bool m_move_on_rect = false;
};

#endif // INTERFACEELEMENT_H
