#ifndef CATEGORYATTRIBUTELINKSCTRL_H
#define CATEGORYATTRIBUTELINKSCTRL_H

#include "../../sqlCtrlBase.h"
#include "../sqlModel/categoryattributelinksmodel.h"

//============================================================================
/// file
/// brief      元器件分类属性表控制器
/// author     wm
/// version    1.0
/// date       2025/06/05
/// todo
//============================================================================

class CategoryAttributeLinksCtrl : public SqlCtrlBase
{
public:
    CategoryAttributeLinksCtrl(const QSqlDatabase& db);
    ~CategoryAttributeLinksCtrl();

    // 针对数据模型的操作
    CategoryAttributeLinksModelList fetchAll() const;
    CategoryAttributeLinksModelPtr  fetch(const QString &id) const;
    bool modelExist(const QString &id) const;
    // end

    // 针对数据库表的操作
    bool insert(const CategoryAttributeLinksModelPtr &obj);
    bool modify(const CategoryAttributeLinksModelPtr &newValueObj);
    bool remove(const QString &id);
    // end

public:
    QString tableName()       const override {return "Category_Attribute_Links";}
    QString primaryKeyField() const override {return "LinkUUID";}
    bool    recordExist(const QString &id) const override;
    virtual bool softDelete() const override { return true; }

private:
    QString tableSql()        const override;
    void loadTable();

private:
    CategoryAttributeLinksModelList mAllObj;
};

#endif // CATEGORYATTRIBUTELINKSCTRL_H