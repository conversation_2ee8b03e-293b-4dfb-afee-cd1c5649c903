﻿#include "interfacemodelelement.h"

#include "../diagram.h"
#include "../qetxml.h"

#include "../qetgraphicsitem/terminal.h"
#include "3rdParty/qet/sources/qetgraphicsitem/dynamicelementtextitem.h"
#include "../ElementsCollection/xmlelementcollection.h"

#include <algorithm>

InterfaceModelElement::InterfaceModelElement(const ElementsLocation &location,
                                   int *state, Element *parentElement, const QString &uuid) :
      Element(location, nullptr, state, Element::Simple, uuid, parentElement)
{
    for (auto t : terminals())
    {
        t->setDrawTermianl(true);
        t->setDrawHover(true);
    }

    if (parentElement)
    {
        textItem()->setFlag(QGraphicsItem::ItemSendsGeometryChanges, false);
        textItem()->setFlag(QGraphicsItem::ItemIsSelectable, false);
        textItem()->setFlag(QGraphicsItem::ItemIsMovable, false);

        setFlag(QGraphicsItem::ItemIsSelectable, false);
        setFlag(QGraphicsItem::ItemIsMovable, false);
    }
}

InterfaceModelElement::~InterfaceModelElement()
{

}

bool InterfaceModelElement::fromXml(QDomElement& e, QHash<int, Terminal*>& table_id_adr)
{
    bool res = Element::fromXml(e, table_id_adr);

    if (res)
    {
        if (e.hasAttribute("interfaceConnector"))
        {
            m_if_connector_id = e.attribute("interfaceConnector");
        }

        if (e.hasAttribute("ADEFatherUuid"))
            mADEFatherUuid = e.attribute("ADEFatherUuid");

        if (e.hasAttribute("allPinsInfo"))
        {
            QString pins = e.attribute("allPinsInfo");
            mAllPins = pins.split(",");
        }

        if (e.hasAttribute("allSortedPins"))
        {
            QString pins = e.attribute("allSortedPins");
            mAllSortedPoints = pins.split(",");
        }

        if (e.hasAttribute("mirrored"))
        {
            QString mirror = e.attribute("mirrored");
            if (mirror == "1")
                mIsMirrored = true;
            else if (mirror == "0")
                mIsMirrored = false;
        }

        if (e.hasAttribute("displayPoints"))
        {
            QString pins = e.attribute("displayPoints");

            if (pins.isEmpty())
                mDiaplayPoints = QStringList();
            else
                mDiaplayPoints = pins.split(",");

            mInitDisplayPoints = mDiaplayPoints;
        }
    }

    //子腔体设置不可选中，不可移动
    // if (!mADEFatherUuid.isEmpty())
    // {
    //     textItem()->setFlag(QGraphicsItem::ItemSendsGeometryChanges, false);
    //     textItem()->setFlag(QGraphicsItem::ItemIsSelectable, false);
    //     textItem()->setFlag(QGraphicsItem::ItemIsMovable, false);

    //     setFlag(QGraphicsItem::ItemIsSelectable, false);
    //     setFlag(QGraphicsItem::ItemIsMovable, false);
    // }

    return res;
}

QDomElement InterfaceModelElement::toXml(QDomDocument& document,
                                    QHash<Terminal*, int>& table_adr_id) const
{
    // 先在Element基类中创建xml元素, 再补充自定义信息
    QDomElement elmtXml = Element::toXml(document, table_adr_id);
    if (!m_if_connector_id.isEmpty())
    {
        elmtXml.setAttribute("interfaceConnector", m_if_connector_id);
    }

    if (!mADEFatherUuid.isEmpty())
        elmtXml.setAttribute("ADEFatherUuid", mADEFatherUuid);

    if (!mAllPins.isEmpty())
    {
        QString pins = mAllPins.join(",");
        elmtXml.setAttribute("allPinsInfo", pins);
    }

    if (!mAllSortedPoints.isEmpty())
    {
        QString pins = mAllSortedPoints.join(",");
        elmtXml.setAttribute("allSortedPins", pins);
    }

    if (mIsMirrored)
    {
        elmtXml.setAttribute("mirrored", true);
    }
    else
    {
        elmtXml.setAttribute("mirrored", false);
    }

    QString pins = mDiaplayPoints.join(",");
    elmtXml.setAttribute("displayPoints", pins);

    return elmtXml;
}

void InterfaceModelElement::changeLocation(const ElementsLocation &newLocation)
{
    Element::changeLocation(newLocation);
}

void InterfaceModelElement::setInterfaceConnectorId(const QString &uuid)
{
    m_if_connector_id = uuid;
}

void InterfaceModelElement::setADEFatherUuid(const QString &uuid)
{
    mADEFatherUuid = uuid;
}

void InterfaceModelElement::initSortedPoints(const QStringList points)
{
    mAllSortedPoints = points;

    //记录点位顺序
    if (this->childElements().isEmpty())
        return;

    QList<Element *> eleList = childElements();
    std::sort(eleList.begin(), eleList.end(), [](const Element* a, const Element* b) {
        return a->x() < b->x();
    });

    mDiaplayPoints.clear();
    for (auto childEle : eleList)
    {
        mDiaplayPoints << childEle->textItem()->text();
    }

    mInitDisplayPoints = mDiaplayPoints;
}

void InterfaceModelElement::recordSortedPoints(const QStringList points)
{
    if (points.isEmpty())
        return;

    mAllSortedPoints = points;
}

void InterfaceModelElement::setInitDisplayPoints(const QStringList points)
{
    mInitDisplayPoints = points;
}

void InterfaceModelElement::recordCurDiaplayPoints(const QStringList points)
{
    mDiaplayPoints = points;
}

QStringList InterfaceModelElement::getHidePoints()
{
    QStringList hidePins;
    for (auto childEle : childElements())
    {
        if (!childEle->isVisible())
        {
            hidePins << childEle->textItem()->text();
        }
    }

    return hidePins;
}

bool InterfaceModelElement::isContactorEleExist(const QString &pointName)
{
    bool isExist = false;

    if (childElements().isEmpty())
        return isExist;

    for (auto childEle : childElements())
    {
        if (childEle->textItem()->text() == pointName)
        {
            isExist = true;
            return isExist;
        }
    }

    return isExist;
}

Element *InterfaceModelElement::getChildEle(const QString &pointName)
{
    if (!isContactorEleExist(pointName))
        return nullptr;

    for (auto childEle : childElements())
    {
        if (childEle->textItem()->text() == pointName)
        {
            return childEle;
        }
    }

    return nullptr;
}

void InterfaceModelElement::updateSize(QDomElement &xml)
{
    //修改虚线框形状
    int w = 0, h = 0, hot_x = 0, hot_y = 0;
    if (!QET::attributeIsAnInteger(xml, QStringLiteral("width"), &w)         ||
            !QET::attributeIsAnInteger(xml, QStringLiteral("height"), &h)        ||
            !QET::attributeIsAnInteger(xml, QStringLiteral("hotspot_x"), &hot_x) ||
            !QET::attributeIsAnInteger(xml, QStringLiteral("hotspot_y"), &hot_y))
    {

    }
    setSize(w, h);
    setHotspot(QPoint(hot_x, hot_y));
}

void InterfaceModelElement::recordMirroredState(bool isMirrored)
{
    mIsMirrored = isMirrored;
}

QStringList InterfaceModelElement::getAllPoints()
{
    return mAllPins;
}

void InterfaceModelElement::initAllPins()
{
    mAllPins = mAllSortedPoints;
}

void InterfaceModelElement::changeChildPins()
{
    //记录点位顺序
    if (this->childElements().isEmpty())
        return;

    mAllPins.clear();
    for (auto childEle : this->childElements())
    {
        mAllPins << childEle->textItem()->text();
    }
}

void InterfaceModelElement::removeChildContactorElement(const QString &pointName)
{
    if (!this->isContactorEleExist(pointName))
        return;
    else
    {
        auto childEle = this->getChildEle(pointName);

        if (childEle)
        {
            childEle->setVisible(false);
            childEle->setParentItem(nullptr);
        }
    }
}

void InterfaceModelElement::addChildContactorElement(const QString &pointName)
{
    qreal angle;
    ContactorElement::ContactorType type;

    //刷新点位元素
    //隐藏的点位设为不可见
    if (!this->childElements().isEmpty())
    {
        for (auto childEle : this->childElements())
        {
            ContactorElement* child = static_cast<ContactorElement*>(childEle);

            angle = child->rotation();
            type = child->contactorType();
        }
    }
    else
    {
        angle = 180;
        type =  CreateAdeElement::getInstance()->getContactorTypeByUuid(this->diagram()->ade()->prjUuid(),
                                                                        this->uuid().toString());
    }

    if (!this->isContactorEleExist(pointName))
    {
        ContactorElement* ctEle = CreateAdeElement::getInstance()->buildContactorEle(this->location().project(),
                                                                                     this,
                                                                                     this->uuid().toString(),
                                                                                     type, pointName);
        if (!ctEle)
            return;

        ctEle->setPos(QPoint(0, 0), false);
        ctEle->setRotation(angle);

        ctEle->textItem()->setRotationToVisual();
        ctEle->adjustTextPos();
    }
}

void InterfaceModelElement::reorderContactorElements(const QStringList points)
{
    QPointF datumPoint;
    QPointF beginPos;

    if (!this->isMirroredState())
    {
        datumPoint = this->boundingRect().topLeft();
        beginPos = QPointF(datumPoint.rx() + POINT_INTERVAL + 5, datumPoint.ry() + this->boundingRect().height() - 5);
    }
    else
    {
        datumPoint = this->boundingRect().bottomLeft();
        beginPos = QPointF(datumPoint.rx() + POINT_INTERVAL + 5, datumPoint.ry() - this->boundingRect().height() + 5);
    }

    for (QString point : points)
    {
        if (this->isContactorEleExist(point))
        {
            auto childEle = this->getChildEle(point);

            if (childEle)
            {
                childEle->setVisible(true);
                childEle->textItem()->setVisible(true);
                childEle->setPos(beginPos, false);
                beginPos.setX(beginPos.rx() + POINT_INTERVAL);
            }
        }
        else
            continue;
    }
}

void InterfaceModelElement::updateModelRect(int pointsNum)
{
    QString xmlPath;
    QDomElement definitionXml;
    if (CreateAdeElement::getInstance()->getCtModelRectXmlFile(xmlPath, definitionXml,
                                                                this->uuid().toString(), pointsNum,
                                                                (PointLayoutType)0, false, 0,
                                                                0, 0, this->textItem()->text()))
    {
        QDomDocument qdoc;
        QDomElement tmpEle = qdoc.createElement("element");
        tmpEle.setAttribute("name", QString("%1").arg(this->location().fileName()));
        tmpEle.appendChild(definitionXml);

        ElementsLocation location = this->location().project()->embeddedElementCollection()->replaceElementDefinition(this->location(),
                                                                                                                      tmpEle);
        this->changeLocation(location);
        this->updateSize(definitionXml);
    }

    if (this->isMirroredState())
    {
        QDomElement mirrorXml = sketchLocation::mirrorElement(this->location().xml());

        QDomDocument qdoc;
        QDomElement tmpEle = qdoc.createElement("element");
        tmpEle.setAttribute("name", QString("%1").arg(this->location().fileName()));
        tmpEle.appendChild(mirrorXml);

        // 替换Element xml描述信息
        ElementsLocation newLo = this->location().project()->embeddedElementCollection()->replaceElementDefinition(this->location(),
                                                                                                                   tmpEle);
        this->changeLocation(newLo);
    }
}

QVariant InterfaceModelElement::itemChange(GraphicsItemChange change, const QVariant& value)
{
    if(change == QGraphicsItem::ItemChildAddedChange)
    {
        QGraphicsItem* child = value.value<QGraphicsItem*>();

        // 所有的接触件元素的端子都使用导线创建规则
        if (Element* e = qgraphicsitem_cast<Element*>(child))
        {
            if (e->terminal())
            {
                e->terminal()->enableConductorGenRule();
            }
        }
    }


    return Element::itemChange(change, value);
}
