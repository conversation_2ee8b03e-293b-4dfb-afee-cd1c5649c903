#ifndef LIBRARYINFOMODEL_H
#define LIBRARYINFOMODEL_H

#include "sqlModelBase.h"
#include <QString>
#include <QDateTime>

class LibraryInfoModel : public SqlModelBase
{
    Q_OBJECT
    Q_PROPERTY(QString id READ getId WRITE setId)
    Q_PROPERTY(QString libraryUUID READ getLibraryUUID WRITE setLibraryUUID)
    Q_PROPERTY(QString libraryName READ getLibraryName WRITE setLibraryName)
    Q_PROPERTY(QString version READ getVersion WRITE setVersion)
    Q_PROPERTY(bool isOfficial READ getIsOfficial WRITE setIsOfficial)
    Q_PROPERTY(QString description READ getDescription WRITE setDescription)
    Q_PROPERTY(QDateTime creationTimestamp READ getCreationTimestamp WRITE setCreationTimestamp)
    Q_PROPERTY(QString createdBy READ getCreatedBy WRITE setCreatedBy)
    Q_PROPERTY(QDateTime lastModifiedTimestamp READ getLastModifiedTimestamp WRITE setLastModifiedTimestamp)

public:
    explicit LibraryInfoModel(QObject *parent = nullptr);

    void fromJson(const QJsonObject &json) override;
    QJsonObject toJsonObject() const override;
    void fromVariantMap(const QVariantMap &map) override;
    QVariantMap toVariantMap() const override;
    void reset() override;

    QString getId() const;
    void setId(const QString &id);

    QString getLibraryUUID() const;
    void setLibraryUUID(const QString &libraryUUID);

    QString getLibraryName() const;
    void setLibraryName(const QString &libraryName);

    QString getVersion() const;
    void setVersion(const QString &version);

    bool getIsOfficial() const;
    void setIsOfficial(bool isOfficial);

    QString getDescription() const;
    void setDescription(const QString &description);

    QDateTime getCreationTimestamp() const;
    void setCreationTimestamp(const QDateTime &creationTimestamp);

    QString getCreatedBy() const;
    void setCreatedBy(const QString &createdBy);

    QDateTime getLastModifiedTimestamp() const;
    void setLastModifiedTimestamp(const QDateTime &lastModifiedTimestamp);

private:
    QString m_id;
    QString m_libraryUUID;
    QString m_libraryName;
    QString m_version;
    bool m_isOfficial;
    QString m_description;
    QDateTime m_creationTimestamp;
    QString m_createdBy;
    QDateTime m_lastModifiedTimestamp;
};

#endif // LIBRARYINFOMODEL_H 