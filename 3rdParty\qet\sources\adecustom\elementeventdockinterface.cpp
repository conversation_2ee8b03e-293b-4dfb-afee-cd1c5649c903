﻿#include "elementeventdockinterface.h"

#include <QGraphicsView>
#include <QToolTip>
#include <QDebug>

#include "../diagram.h"
#include "../qetgraphicsitem/element.h"
#include "../qetgraphicsitem/customconductor.h"

#include "project/projectNodeManager/interfacemanager.h"
#include "sql/sqlModel/projectmodelgetter.h"
#include "../undocommand/customCommand/customdockinterfacecommand.h"
#include "../diagramcommands.h"
#include "./connectorLib/ConnectorMatching/connectorvalidator.h"
#include "3rdParty/qet/sources/adecustom/adeconnectrelation.h"

#include "adelogiccable.h"
#include "ade.h"

#include "./CustomElements/interfaceelement.h"


using namespace ADE::Diagram;

ElementEventDockInterface::ElementEventDockInterface(InterfaceElement *element, QObject *parent) :
    QObject(parent),
    mElement(element)
{

}

ElementEventDockInterface::~ElementEventDockInterface()
{
    setDiagramMouseInteractionEnabled(true);

    if (mDockAnchorLine && mDockAnchorLine->scene())
    {
        if (mElement->scene())
            qobject_cast<Diagram*>(mElement->scene())->removeItem(mDockAnchorLine);
    }
    delete mDockAnchorLine;
}

Diagram *ElementEventDockInterface::diagram() const
{
    if (!mElement)
        return nullptr;

    return static_cast<Diagram*>(mElement->scene());
}

void ElementEventDockInterface::mousePressEvent(QGraphicsSceneMouseEvent *event)
{
    mInitialPos = mElement->pos();

    mFirstMovePos = mElement->scenePos();
    mFirstMoveRotation = mElement->rotation();

    mLastMoveRoatation = mElement->rotation();
    mLastMoveDocked = false;

    mDockedElement = nullptr;

    if (mElement->connectRelation())
        mCanDock = false;
    else
        mCanDock = true;

    if (event->modifiers() == redockKeyModifier())
    {
        mReDockMode = true;
    }

    mLastMovePos = mFirstMovePos;
}

void ElementEventDockInterface::mouseMoveEvent(QGraphicsSceneMouseEvent *event)
{
    /* 当上次移动对接后鼠标移动事件被其它软件（如截图）接管时，松开鼠标后，
       接口会偏离上次对接接口的位置，因此需要对这种行为进行检测。
     */
    {
        QPointF curMov = event->scenePos() - mLastMovePos;
        mLastMovePos = event->scenePos();
        qreal validMoveDist = 10.0;
        if (mLastMoveDocked && (curMov.x() > validMoveDist || curMov.y() > validMoveDist) )
        {
            mLastMoveDocked = false;
            return;
        }
    }

    if (!mCanDock)
        return;

    if (!mElement || !mElement->scene()) return;

    Diagram *diagram = qobject_cast<Diagram*>(mElement->scene());
    if (diagram->ade()->prjUuid().isEmpty())
        return;

    setDiagramMouseInteractionEnabled(false);

    // 在搜索半径范围内查找接口元素
    QList<InterfaceElement*> ifElements;
    QList<QGraphicsItem*> qgis = mElement->scene()->items(QET::circlePath(mElement->pos(), mDockSearchRadius));
    for (auto qgi : qgis)
    {
        if (qgi->type() != Element::Type)
            continue;

        Element *other = qgraphicsitem_cast<Element*>(qgi);
        if (other->adeType() != InterfaceElement::ADEType)
            continue;

        if (other == mElement)
            continue;

        ifElements << element_cast<InterfaceElement*>(other);
    }

    // 将已经不在搜索范围内的接口元素状态清除
    for (InterfaceElement *elmt : mLastMoveElements)
    {
        if (!ifElements.contains(elmt))
            elmt->showHint(Element::HintNone);
    }
    mLastMoveElements = ifElements;

    // 恢复上次移动时的旋转角度
    if (mLastMoveDocked) mElement->setRotation(mLastMoveRoatation);
    mLastMoveRoatation = mElement->rotation();

    // 对搜索范围内的接口元素做对接兼容检查
    QString prjUuid = diagram->ade()->prjUuid();
    if (!ifElements.isEmpty())
    {
        interfacePtr ifObj = PRJ_MD_GETTER->getInterfaceObj(prjUuid, mElement->uuid().toString());
        if (ifObj.get() == nullptr) return;

        QList<InterfaceElement *> dockableIfElements;
        for (InterfaceElement *other : ifElements)
        {
            // 判断接口是否存在连接关系
            if (other->connectRelation())
                continue;

            // 不对接刚断开的接口
            if (mUnDockedElemnet && other == mUnDockedElemnet)
                continue;

            // 判断接口是否属于同一条电缆
            interfacePtr otherObj = PRJ_MD_GETTER->getInterfaceObj(prjUuid, other->uuid().toString());
            if (otherObj.get() == nullptr) return;

            // 已经被引用的接口无法连接
            if (otherObj->cited)
            {
                bool canDock = true;
                if (PRJ_MD_GETTER->isInterfaceBelongsToCable(prjUuid, otherObj->id))
                {
                    // 电缆上的接口被引用，这个接口只有在引用它的设备上才能连接
                    if (!other->parentElement()) canDock = false;
                }
                else
                {
                    // 组件上的接口被引用，这个接口只有在引用它的设备上才能连接
                    if (otherObj->fatherUuid == other->parentElement()->uuid().toString()) canDock = false;
                }

                if (!canDock)
                {
                    other->showHint(Element::HintWrong);
                    QToolTip::showText(event->screenPos(), "该接口已被引用");
                    continue;
                }
            }

            if (ifObj->fatherUuid == otherObj->fatherUuid)
            {
                other->showHint(Element::HintWrong);
                QToolTip::showText(event->screenPos(), "无法连接相同电缆上的接口");
                continue;
            }

            // 判断两条电缆是否已经存在其它的连接关系
            if (mElement->cable() && other->cable())
            {
                QString reason;
                if (!cableCanConnect(mElement->cable(), other->cable(), reason))
                {
                    other->showHint(Element::HintWrong);
                    QToolTip::showText(event->screenPos(), reason);
                    continue;
                }
            }

            //接口连接器兼容性限制
            if (connectorDockLimited())
            {
                // 根据接口类型判断是否能对接
                if (connectorCanDock(ifObj->cmUuid, otherObj->cmUuid))
                {
                    other->showHint(Element::HintCorrect);
                    dockableIfElements << other;
                }
                else
                {
                    other->showHint(Element::HintWrong);

                    // 显示接口连接器类型对应文本
                    QPoint scenePos = QPoint(other->scenePos().toPoint().x(),
                                             other->scenePos().toPoint().y() - 20);
                    QPoint viewPos = diagram->views().first()->mapFromScene(scenePos);
                    QPoint tipPos  = diagram->views().first()->viewport()->mapToGlobal(viewPos);
                    QToolTip::showText(tipPos, "连接器不兼容");
                }
            }
            else
            {
                other->showHint(Element::HintCorrect);
                dockableIfElements << other;
            }
        }

        // 与距离最近的接口元素对接
        if (!dockableIfElements.isEmpty())
        {
            QHash<InterfaceElement *, double> distInfo;
            for (InterfaceElement *other : dockableIfElements)
                distInfo.insert(other, QET::calcDistanceBetweenPoints(mElement->scenePos(), other->scenePos()));

            // 查找距离最近的接口元素
            QList<double> distList = distInfo.values();
            qSort(distList);
            double closestDist = distList.first();
            InterfaceElement *closestElmt = distInfo.key(closestDist);

            // 最近的元素间画一条引导线
            drawAnchorLine(closestElmt);

            // 当最近距离小于预设对接距离时对接
            if (closestDist < mDockDistance)
            {       
                QPair<QPointF, int> posRot = getInterfaceDockPos(mElement, closestElmt);
                mElement->setPos(posRot.first, false); // 接口对接时不对齐网格
                mElement->setRotation(posRot.second);
                mElement->showHint(Element::HintCorrect);

                mLastMoveDocked = true;
                mDockedElement = closestElmt;
                hideAnchorLine();
            }
            else
            {
                mElement->showHint(Element::HintNone);

                mLastMoveDocked = false;
                mDockedElement = nullptr;
            }
        }
        else
        {
            hideAnchorLine();
        }
    }
    else
    {
        hideAnchorLine();
    }
}

void ElementEventDockInterface::redockMouseMoveEvent(QGraphicsSceneMouseEvent *event)
{
    //qPDebug() << event->scenePos();
    if (mElement->connectRelation() && !mElement->connectRelation()->isDirectConnect())
    {
        QPointF dockPoint = mElement->scenePos();;
        Qet::Orientation ori = Qet::getItemSceneOrientation(mElement);
        int offset = CB_IF_UPPER_HEIGHT;
        if      (ori == Qet::North) {dockPoint = QPointF(dockPoint.x(), dockPoint.y() + offset);}
        else if (ori == Qet::East)  {dockPoint = QPointF(dockPoint.x() - offset, dockPoint.y());}
        else if (ori == Qet::South) {dockPoint = QPointF(dockPoint.x(), dockPoint.y() - offset);}
        else if (ori == Qet::West)  {dockPoint = QPointF(dockPoint.x() + offset, dockPoint.y()); }

        if (!mUnDockRangeCircle)
        {
            QRectF circleRect = QRectF(dockPoint.x() - (mDockSearchRadius),
                                       dockPoint.y() - (mDockSearchRadius),
                                       mDockSearchRadius * 2,
                                       mDockSearchRadius * 2);
            mUnDockRangeCircle = new QGraphicsEllipseItem(circleRect);
            mUnDockRangeCircle->setZValue(1000000);

            QPen pen(Qt::NoBrush, 0.4, Qt::DashLine);
            pen.setColor(Qt::gray);
            mUnDockRangeCircle->setPen(pen);

            //mElement->scene()->addItem(mUnDockRangeCircle);
        }

        qreal dist = QET::calcDistanceBetweenPoints(event->scenePos(), dockPoint);
//        if (dist > mDockSearchRadius + 1)
        if (dist > mDockDistance + 1)
        {
            // 断开连接关系
            mUnDockedElemnet = mElement->connectRelation()->getAnotherInterfaceElement(mElement);
            QPointF dockPos = mUnDockedElemnet->pos();
            diagram()->undoStack().beginMacro("重新连接接口");
            diagram()->undoStack().push(new CustomUndockInterfaceCommand(mElement->connectRelation(), diagram()->ade()->getConnectedIfHighLightState()));
            mUnDockedElemnet->setPos(dockPos);
            mElement->setPos(event->scenePos());
            mCanDock = true;
            mUnDocked = true;

//            diagram()->removeItem(mUnDockRangeCircle);
//            delete  mUnDockRangeCircle;
        }
    }
    else
    {
        mElement->triggerQETMouseMoveEvent(event);
        mouseMoveEvent(event);
    }
}

QPointF ElementEventDockInterface::validMovment() const
{
    return (mElement->pos() - mInitialPos);
}

void ElementEventDockInterface::mouseReleaseEvent(QGraphicsSceneMouseEvent *event)
{
    //qPDebug() << event->scenePos();
    Q_UNUSED(event)

    // 使能布置图鼠标右键功能
    setDiagramMouseInteractionEnabled(true);

    // 松开鼠标后还原接口显示状态
    for (InterfaceElement *elmt : mLastMoveElements)
        elmt->showHint(Element::HintNone);
    mLastMoveElements.clear();

    mElement->showHint(Element::HintNone);

    // 将辅助线移除显示
    if (mDockAnchorLine)
        mElement->scene()->removeItem(mDockAnchorLine);

    // 发生对接行为时创建撤销命令
    QUndoStack *undoStack = &(qobject_cast<Diagram*>(mElement->scene())->undoStack());
    // 添加撤销命令
    if (mDockedElement && mLastMoveDocked && !mUnDocked)
    {
        // 将移动接口生成的MoveElementsCommand命令出栈
        // const QUndoCommand *lastCmd = undoStack->command(undoStack->count() - 1);
        // if (dynamic_cast<const MoveElementsCommand *>(lastCmd))
        // {
        //     QPointF pos = mElement->pos();
        //     undoStack->undo();
        //     mElement->setPos(pos, false); // 被动移动位置不对齐网格
        // }

        // 将对接命令入栈
        CustomDockInterfaceCommand *cmd = new CustomDockInterfaceCommand(mElement, mDockedElement,
                                                                         mFirstMovePos, diagram()->ade()->getConnectedIfHighLightState(),
                                                                         mFirstMoveRotation);
        undoStack->push(cmd);
    }

    if (mUnDocked)
        undoStack->endMacro();    
}

QPair<QPointF, int> ElementEventDockInterface::getInterfaceDockPos(InterfaceElement *trigger,
                                                                   InterfaceElement *other)
{
    if (!trigger || !other)
        return qMakePair(QPointF(), 0);

    // 获取对接接口元素相关信息
    ADE::Diagram::InterfaceElementCat otherCat = ADE::Diagram::getInterfaceElementCatByType(other->getADEInterfaceType());
    ADE::Diagram::InterfaceElementCat triggerCat = ADE::Diagram::getInterfaceElementCatByType(trigger->getADEInterfaceType());
    if (otherCat == IECNull || triggerCat == IECNull)
    {
        qDebug() << Q_FUNC_INFO << "获取接口类型失败";
        return qMakePair(trigger->pos(), trigger->rotation());
    }

    Qet::Orientation otherOri = other->sceneOrientation();
    const QPointF otherPos = other->scenePos();

    QPointF dockPos = trigger->pos();
    int     dockRot = trigger->rotation();

    if (otherCat == Plug)
    {
        // 插头与插头对接
        if (triggerCat == Plug)
        {
            int offset = CB_IF_UPPER_HEIGHT * 2;

            if      (otherOri == Qet::North) {dockPos = QPointF(otherPos.x(), otherPos.y() + offset); dockRot = 180;}
            else if (otherOri == Qet::East)  {dockPos = QPointF(otherPos.x() - offset, otherPos.y()); dockRot = 270;}
            else if (otherOri == Qet::South) {dockPos = QPointF(otherPos.x(), otherPos.y() - offset); dockRot = 0;}
            else if (otherOri == Qet::West)  {dockPos = QPointF(otherPos.x() + offset, otherPos.y()); dockRot = 90;}
        }
        // 插座与插头对接
        else if (triggerCat == Socket)
        {
            // 插座在设备上时计算其相对位置
            if (trigger->parentElement())
            {
                int offset = (EQ_IF_HEIGHT / 2) + CB_IF_UPPER_HEIGHT;

                // 设备上插座移动时计算连接的插头位置
                if      (otherOri == Qet::North) {dockPos = QPointF(otherPos.x(), otherPos.y() + offset); dockRot = 0;}
                else if (otherOri == Qet::East)  {dockPos = QPointF(otherPos.x() - offset, otherPos.y()); dockRot = 90;}
                else if (otherOri == Qet::South) {dockPos = QPointF(otherPos.x(), otherPos.y() - offset); dockRot = 180;}
                else if (otherOri == Qet::West)  {dockPos = QPointF(otherPos.x() + offset, otherPos.y()); dockRot = 270;}

                dockPos  = trigger->parentElement()->mapFromScene(dockPos);
                dockRot += trigger->parentElement()->rotation();
            }
            else
            {
                int offset = CB_IF_UPPER_HEIGHT * 2;

                if      (otherOri == Qet::North) {dockPos = QPointF(otherPos.x(), otherPos.y() + offset); dockRot = 180;}
                else if (otherOri == Qet::East)  {dockPos = QPointF(otherPos.x() - offset, otherPos.y()); dockRot = 270;}
                else if (otherOri == Qet::South) {dockPos = QPointF(otherPos.x(), otherPos.y() - offset); dockRot = 0;}
                else if (otherOri == Qet::West)  {dockPos = QPointF(otherPos.x() + offset, otherPos.y()); dockRot = 90;}
            }
        }
    }
    else if (otherCat == Socket)
    {
        // 插头与插座对接
        if (triggerCat == Plug)
        {
            if (other->parentElement())
            {
                int offset = (EQ_IF_HEIGHT / 2) + CB_IF_UPPER_HEIGHT;

                if      (otherOri == Qet::North) {dockPos = QPointF(otherPos.x(), otherPos.y() - offset); dockRot = 0;}
                else if (otherOri == Qet::East)  {dockPos = QPointF(otherPos.x() + offset, otherPos.y()); dockRot = 90;}
                else if (otherOri == Qet::South) {dockPos = QPointF(otherPos.x(), otherPos.y() + offset); dockRot = 180;}
                else if (otherOri == Qet::West)  {dockPos = QPointF(otherPos.x() - offset, otherPos.y()); dockRot = 270;}
            }
            else
            {
                int offset = CB_IF_UPPER_HEIGHT * 2;

                if      (otherOri == Qet::North) {dockPos = QPointF(otherPos.x(), otherPos.y() + offset); dockRot = 180;}
                else if (otherOri == Qet::East)  {dockPos = QPointF(otherPos.x() - offset, otherPos.y()); dockRot = 270;}
                else if (otherOri == Qet::South) {dockPos = QPointF(otherPos.x(), otherPos.y() - offset); dockRot = 0;}
                else if (otherOri == Qet::West)  {dockPos = QPointF(otherPos.x() + offset, otherPos.y()); dockRot = 90;}
            }
        }
        else if (triggerCat == Socket)
        {
            if (other->parentElement())
            {
                int offset = (EQ_IF_HEIGHT / 2) + CB_IF_UPPER_HEIGHT;

                if      (otherOri == Qet::North) {dockPos = QPointF(otherPos.x(), otherPos.y() - offset); dockRot = 0;}
                else if (otherOri == Qet::East)  {dockPos = QPointF(otherPos.x() + offset, otherPos.y()); dockRot = 90;}
                else if (otherOri == Qet::South) {dockPos = QPointF(otherPos.x(), otherPos.y() + offset); dockRot = 180;}
                else if (otherOri == Qet::West)  {dockPos = QPointF(otherPos.x() - offset, otherPos.y()); dockRot = 270;}
            }
            else
            {
                int offset = CB_IF_UPPER_HEIGHT * 2;

                if      (otherOri == Qet::North) {dockPos = QPointF(otherPos.x(), otherPos.y() + offset); dockRot = 180;}
                else if (otherOri == Qet::East)  {dockPos = QPointF(otherPos.x() - offset, otherPos.y()); dockRot = 270;}
                else if (otherOri == Qet::South) {dockPos = QPointF(otherPos.x(), otherPos.y() - offset); dockRot = 0;}
                else if (otherOri == Qet::West)  {dockPos = QPointF(otherPos.x() + offset, otherPos.y()); dockRot = 90;}
            }
        }
    }

    return qMakePair(dockPos, dockRot);
}

QPointF ElementEventDockInterface::getCableInterfaceUndockPos(InterfaceElement *interfaceElmt)
{
    if (interfaceElmt->parentElement())
        return interfaceElmt->pos();

    QPointF pos = interfaceElmt->pos();

    Qet::Orientation ori = interfaceElmt->sceneOrientation();

    int undockOffset = 5; // 接口断开后偏移距离

    if      (ori == Qet::North) {pos = QPointF(pos.x(), pos.y() - undockOffset);}
    else if (ori == Qet::East)  {pos = QPointF(pos.x() + undockOffset, pos.y());}
    else if (ori == Qet::South) {pos = QPointF(pos.x(), pos.y() + undockOffset);}
    else if (ori == Qet::West)  {pos = QPointF(pos.x() - undockOffset, pos.y());}

    return pos;
}

bool ElementEventDockInterface::cableCanConnect(ADELogicCable *cable1, ADELogicCable *cable2, QString &reason)
{
    QList<ADEConnectRelation *> thisCableConnectRelations = cable1->relatedConnectRelations();
    QList<ADEConnectRelation *> otherCableConnectRelations = cable2->relatedConnectRelations();
    for (auto cr : thisCableConnectRelations)
    {
        if (otherCableConnectRelations.contains(cr))
        {
            reason = "已存在其它接口与该电缆连接";
            return false;
        }
    }
    return true;
}

void ElementEventDockInterface::setDiagramMouseInteractionEnabled(bool enabled)
{
    if (mElement && mElement->scene())
    {
        Qt::ContextMenuPolicy policy;
        if (enabled)
            policy = Qt::DefaultContextMenu;
        else
            policy = Qt::NoContextMenu;

        for (QGraphicsView *view : mElement->scene()->views())
            view->setContextMenuPolicy(policy);
    }
}

void ElementEventDockInterface::drawAnchorLine(InterfaceElement *target)
{
    if (!mElement || !mElement->scene() || !target)
        return;

    if (!mDockAnchorLine)
    {
        mDockAnchorLine = new QGraphicsLineItem;
        mDockAnchorLine->setZValue(1000000);

        QPen pen(Qt::NoBrush, 0.4, Qt::DashLine);
        pen.setColor(Qt::gray);
        mDockAnchorLine->setPen(pen);

        mElement->scene()->addItem(mDockAnchorLine);
    }

    QPointF p1 = getElementAnchorLinePoint(mElement);
    QPointF p2 = getElementAnchorLinePoint(target);

    mDockAnchorLine->setLine(QLineF(p1, p2));
    mDockAnchorLine->show();
}

QPointF ElementEventDockInterface::getElementAnchorLinePoint(InterfaceElement *elmt)
{
    ADE::Diagram::InterfaceElementCat elmtCat = ADE::Diagram::getInterfaceElementCatByType(elmt->getADEInterfaceType());
    Qet::Orientation ori = elmt->sceneOrientation();

    QPointF scencePos = elmt->scenePos();
    QPointF point = scencePos;

    if (elmtCat == Plug)
    {
        int offset = CB_IF_UPPER_HEIGHT;

        if      (ori == Qet::North) {point = QPointF(scencePos.x(), scencePos.y() + offset);}
        else if (ori == Qet::East)  {point = QPointF(scencePos.x() - offset, scencePos.y());}
        else if (ori == Qet::South) {point = QPointF(scencePos.x(), scencePos.y() - offset);}
        else if (ori == Qet::West)  {point = QPointF(scencePos.x() + offset, scencePos.y());}
    }
    else if (elmtCat == Socket)
    {
        int offset = EQ_IF_HEIGHT / 2;

        if      (ori == Qet::North) {point = QPointF(scencePos.x(), scencePos.y() - offset);}
        else if (ori == Qet::East)  {point = QPointF(scencePos.x() + offset, scencePos.y());}
        else if (ori == Qet::South) {point = QPointF(scencePos.x(), scencePos.y() + offset);}
        else if (ori == Qet::West)  {point = QPointF(scencePos.x() - offset, scencePos.y());}
    }

    return point;
}

void ElementEventDockInterface::hideAnchorLine()
{
    if (mDockAnchorLine)
        mDockAnchorLine->hide();
}

bool ElementEventDockInterface::connectorCanDock(const QString &uuid1, const QString &uuid2)
{
    // 一个接口连接器不存在时允许对接
    if (uuid1.isEmpty() || uuid2.isEmpty())
        return true;

    return CONNECTORVALIDATOR->getInstance()->isConnectorCompatible(uuid1, uuid2);
}

bool ElementEventDockInterface::connectorDockLimited()
{
    bool isLimited;

    if (diagram()->ade()->mConnectorDockLimit)
        isLimited = true;
    else
        isLimited = false;

    return isLimited;
}
