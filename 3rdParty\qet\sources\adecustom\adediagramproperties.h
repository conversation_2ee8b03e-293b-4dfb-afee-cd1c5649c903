﻿#ifndef ADEDIAGRAMPROPERTIES_H
#define ADEDIAGRAMPROPERTIES_H

#include "adetextproperties.h"
#include "../CustomExtend/adeelementstyle.h"

#include <QDomElement>
#include <QDomDocument>
#include <QMap>
#include <QJsonObject>

class ADEDiagramProperties
{
public:
    ADEDiagramProperties();
    ~ADEDiagramProperties();

    static QString tagName(){return QString("ADEProperties");}

public:
    ADETextProperties::ADEItemTextShowState mEquipmentTextShowState;  ///< 设备/组件文本显示状态
    ADETextProperties::ADEItemTextShowState mInterfaceTextShowState;  ///< 接口文本显示状态
    ADETextProperties::ADEItemTextShowState mCableTextShowState;      ///< 电缆文本显示状态

    QMap<QString, QPair<AdeElementStyle, QJsonObject>> mBelongingSysStyles;  ///< 所属系统样式信息，QMap<所属系统名称, 样式>

public:
    QDomElement toXml(QDomDocument &doc) const;
    bool fromXml(QDomElement &e);

    ADETextProperties::ADEItemTextShowState getTextShowStateByItemType(Ade::DiagramItemType type);

    void updateDiagramBelongingSystem(const QStringList &belongingSystem);
    void updateBelongingSysStyle(const QString &belongingSysName, AdeElementStyle style, QJsonObject fontStyle);

};

#endif // ADEDIAGRAMPROPERTIES_H
