﻿/*
	Copyright 2006-2021 The QElectroTech Team
	This file is part of QElectroTech.

	QElectroTech is free software: you can redistribute it and/or modify
	it under the terms of the GNU General Public License as published by
	the Free Software Foundation, either version 2 of the License, or
	(at your option) any later version.

	QElectroTech is distributed in the hope that it will be useful,
	but WITHOUT ANY WARRANTY; without even the implied warranty of
	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
	GNU General Public License for more details.

	You should have received a copy of the GNU General Public License
	along with QElectroTech.  If not, see <http://www.gnu.org/licenses/>.
*/
#ifndef BRANCHENDELEMENT_H
#define BRANCHENDELEMENT_H

#include "../qetgraphicsitem/element.h"


/**
* @file
* @brief      ADE电缆分支图端点元素
* <AUTHOR>
* @version    1.0
* @date       2023/09/25
* @todo
*/
class BranchEndElement : public Element
{
    Q_OBJECT

public:
    explicit BranchEndElement(const ElementsLocation &,
                              int * = nullptr);
    ~BranchEndElement() override;

private:
    BranchEndElement(const Element&);

public:
    enum { ADEType = 5 };
    int adeType() const override {return ADEType;}

public:
    QRectF      boundingRect() const override;
    bool        fromXml(QDomElement&, QHash<int,Terminal*>&) override;
    QDomElement toXml(QDomDocument&, QHash<Terminal*,int>&) const override;

private:
    int mHalfLength = 10;

};

#endif // BRANCHENDELEMENT_H
