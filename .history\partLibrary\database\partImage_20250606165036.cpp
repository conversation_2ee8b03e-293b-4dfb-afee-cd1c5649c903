#include "partImage.h"
#include <QVariant>

namespace part_library
{
namespace database
{

QString PartImage::tableName() const
{
    return "PartImages";
}

QString PartImage::primaryKeyField() const
{
    return "ImageUUID";
}

QVariantMap PartImage::modelToDbMap() const
{
    QVariantMap map;
    map[":ImageUUID"] = imageUUID;
    map[":PartUUID"] = partUUID;
    map[":Pixmap"] = pixmap;
    map[":ImageType"] = imageType;
    map[":DisplayOrder"] = displayOrder;
    map[":Description"] = description;
    map[":IsDeleted"] = isDeleted;
    return map;
}

void PartImage::dbToModel(const QSqlRecord& record)
{
    imageUUID = record.value("ImageUUID").toString();
    partUUID = record.value("PartUUID").toString();
    pixmap = record.value("Pixmap").toByteArray();
    imageType = record.value("ImageType").toString();
    displayOrder = record.value("DisplayOrder").toInt();
    description = record.value("Description").toString();
    isDeleted = record.value("IsDeleted").toBool();
}

} // namespace database
} // namespace part_library 