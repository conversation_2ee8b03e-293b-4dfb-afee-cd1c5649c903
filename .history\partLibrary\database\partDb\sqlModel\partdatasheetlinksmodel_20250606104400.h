#ifndef PARTDATASHEETLINKSMODEL_H
#define PARTDATASHEETLINKSMODEL_H

//============================================================================
/// file
/// brief      元器件与Datasheet关联数据模型
/// author     wm
/// version    1.0
/// date       2025/06/05
/// todo
//============================================================================

#include "sqlModelBase.h"
#include <memory>
#include <QUuid>

class PartDatasheetLinksModel : public SqlModelBase
{
public:
    PartDatasheetLinksModel() : id(QUuid::createUuid().toString()){}

public:
    // 模型属性
    QString id;                 ///< 链接uuid
    QString partUUID;           ///< 元器件UUID
    QString datasheetUUID;      ///< DatasheetUUID

public:
    QString tableName()       const override {return "Part_Datasheet_Links";}
    QString primaryKeyField() const override {return "LinkUUID";}
    QVariantMap modelToDbMap() const override;
    void dbToModel(const QSqlRecord& record) override;

public:
    // 字段名称常量
    static const QString FIELD_ID;
    static const QString FIELD_PART_UUID;
    static const QString FIELD_DATASHEET_UUID;
    static const QString FIELD_IS_DELETED;

    static std::shared_ptr<PartDatasheetLinksModel> createPtr()
    {
        return std::make_shared<PartDatasheetLinksModel>();
    }
};

using PartDatasheetLinksModelPtr = std::shared_ptr<PartDatasheetLinksModel>;
using PartDatasheetLinksModelList = QHash<QString, PartDatasheetLinksModelPtr>;

#endif // PARTDATASHEETLINKSMODEL_H 