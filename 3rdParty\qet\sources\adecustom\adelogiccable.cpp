﻿#include "adelogiccable.h"

#include <QDebug>

#include "sql/sqlModel/projectmodelgetter.h"
#include "3rdParty/logBusiness/logbusiness.h"

#include "../adecustom/adecableforkpointitem.h"
#include "../adecustom/adecabletextitem.h"
#include "../adecustom/adecablemanager.h"
#include "../adecustom/adeconnectrelation.h"
#include "../CustomExtend/conductorvertexitem.h"

#include "../diagram.h"
#include "../qetgraphicsitem/terminal.h"
#include "../qetgraphicsitem/element.h"
#include "../qetgraphicsitem/customconductor.h"

#include "CustomElements/interfaceelement.h"
#include "CustomElements/cablespliceelement.h"

ADELogicCable::ADELogicCable(Diagram *diagram, const QString &uuid, const QString &srcText, QObject *parent) : QObject(parent),
    mDiagram(diagram),
    mUuid(uuid),
    mSrcName(srcText)
{
    mTextItem = new ADECableTextItem(this, srcText);
    mTextItem->setFont(ADE::getFontFromStr(mDiagram->ade()->fontFormat().cbTextFont));
    mTextItem->setColor(mDiagram->ade()->fontFormat().cbTextColor);

    // 同步字体显示设置
    ADETextProperties prop = mTextItem->getTextProp();
    prop.setBasicTextShowState(mDiagram->ade()->mADEDiagramProperties.mCableTextShowState);
    mTextItem->setTextProperty(prop);

    diagram->addItem(mTextItem);
}

ADELogicCable::~ADELogicCable()
{
    // qDebug() << Q_FUNC_INFO;
    deleteConnectRelations();

    // for (auto e : mInterfaceElmts)
    for (auto e : cableSelfInterfaceElements())
    {
        mDiagram->removeItem(e);
        delete e;
        e = nullptr;
    }
    mInterfaceElmts.clear();

    QList<CableSpliceElement *> spliceElmts = mSpliceElmts;
    for (auto e : spliceElmts)
    {
        mDiagram->removeItem(e);
        delete e;
        e = nullptr;
    }
    mSpliceElmts.clear();

    if (mTextItem)
    {
        mDiagram->removeItem(mTextItem);
        delete mTextItem;
    }
    //qDebug() << Q_FUNC_INFO << "end";
}

void ADELogicCable::deleteConnectRelations()
{
    QList<ADEConnectRelation *> CRs = mConnectRelations;
    qDeleteAll(CRs);
    mConnectRelations.clear();
}

QMap<QString, InterfaceElement *> ADELogicCable::interfaceElements() const
{
    return mInterfaceElmts;
}

bool ADELogicCable::fromXml(const QDomElement &e)
{
    if (!diagram())
        return false;

    if (e.tagName() != "cable")
         return false;

    //ADE::printQDomElement("CCC", e);

    mUuid = e.attribute("uuid");

    for (auto xmlInterfaceElements : QET::findInDomElement(e, "interfaceElements"))
    {
        for (auto xmlInterface : QET::findInDomElement(xmlInterfaceElements, "interfaceElement"))
        {
            QString uuid = xmlInterface.attribute("uuid");
            Element *elmt = diagram()->getElementByUuid(uuid);
            if (!elmt || elmt->adeType() != InterfaceElement::ADEType)
            {
                LOG_ERROR(QString("获取元素对象失败：电缆(%1)、接口(%2)").arg(mUuid).arg(uuid));
                return false;
            }
            addInterfaceElement(static_cast<InterfaceElement *>(elmt));
        }
    }

    for (auto xmlSpliceElements : QET::findInDomElement(e, "spliceElements"))
    {
        for (auto xmlSplice : QET::findInDomElement(xmlSpliceElements, "spliceElement"))
        {
            Element *elmt = diagram()->getElementByUuid(xmlSplice.attribute("uuid"));
            if (!elmt || elmt->adeType() != CableSpliceElement::ADEType)
            {
                LOG_ERROR("获取电缆端点元素失败");
                return false;
            }
            addSpliceElement(static_cast<CableSpliceElement *>(elmt));
        }
    }

    for (auto xmlConnectRelations : QET::findInDomElement(e, "connectRelations"))
    {
        for (auto xmlCr : QET::findInDomElement(xmlConnectRelations, "connectRelation"))
        {
            ADEConnectRelation *cr = new ADEConnectRelation(this);
            if (!cr->fromXml(xmlCr))
            {
                LOG_ERROR("读取连接关系信息失败");
                delete cr;
                return false;
            }
        }
    }

    for (auto xmlCableText : QET::findInDomElement(e, ADECableTextItem::xmlTagName()))
    {
        mTextItem->fromXml(xmlCableText);
    }

    return true;
}

QDomElement ADELogicCable::toXml(QDomDocument &doc, bool &ok)
{
    ok = true;

    // 根节点
    QDomElement xmlElement = doc.createElement("cable");
    xmlElement.setAttribute("uuid", mUuid);

    // 接口信息
    QDomElement xmlInterfaceElememts = doc.createElement("interfaceElements");
    for (auto elmt : mInterfaceElmts.values())
    {
        QDomElement xmlInterfaceElement = doc.createElement("interfaceElement");
        xmlInterfaceElement.setAttribute("uuid", elmt->uuid().toString());
        xmlInterfaceElememts.appendChild(xmlInterfaceElement);
    }
    xmlElement.appendChild(xmlInterfaceElememts);

    // 交点信息
    QDomElement xmlSpliceElements = doc.createElement("spliceElements");
    for (auto elmt : mSpliceElmts)
    {
        QDomElement xmlSpliceElement = doc.createElement("spliceElement");
        xmlSpliceElement.setAttribute("uuid", elmt->uuid().toString());
        xmlSpliceElements.appendChild(xmlSpliceElement);
    }
    xmlElement.appendChild(xmlSpliceElements);

    // 连接关系信息
    QDomNodeList nodeList = doc.elementsByTagName("element");
    QStringList eleIds;   //布置图上所有的元素id
    for(int i = 0; i < nodeList.size();i++)
    {
        QDomElement ele = nodeList.at(i).toElement();
        eleIds << ele.attribute("uuid");
    }
    QDomElement xmlConnectRelations = doc.createElement("connectRelations");
    for (auto cr : mConnectRelations)
    {
        // 连接的接口不存在于布置图上跳过该连接关系
        if(cr->otherInterface() &&
            !eleIds.contains(cr->otherInterface()->uuid().toString()))
        {
            continue;
        }

        QDomElement xmlConnectRelation = cr->toXml(doc, ok);
        if (ok) xmlConnectRelations.appendChild(xmlConnectRelation);
    }
    xmlElement.appendChild(xmlConnectRelations);

    // 电缆文本信息
    QDomElement xmlTextItem = mTextItem->toXml(doc);
    xmlElement.appendChild(xmlTextItem);

    return xmlElement;
}

void ADELogicCable::addInterfaceElement(InterfaceElement *elmt)
{
    if (!elmt)
        return;

    if (mInterfaceElmts.contains(elmt->uuid().toString()))
        return;

    elmt->setADEFatherUuid(mUuid);

    mInterfaceElmts.insert(elmt->uuid().toString(), elmt);
    elmt->setCable(this);
}

void ADELogicCable::addSpliceElement(CableSpliceElement *elmt)
{
    if (!elmt)
        return;

    if (mSpliceElmts.contains(elmt))
        return;

    mSpliceElmts << elmt;
    elmt->setCable(this);
}

void ADELogicCable::addConnectRelation(ADEConnectRelation *cr)
{
    if (!cr)
        return;

    if (mConnectRelations.contains(cr))
        return;

    mConnectRelations << cr;
}

void ADELogicCable::removeElement(Element *elmt)
{
    //LOG_INFO(QString("removeElement cbUuid: %1 elmtUuid: %2 ifCount: %3").arg(mUuid).arg(elmt->uuid().toString()).arg(QString::number(mInterfaceElmts.size())));
    if (!elmt)
        return;

    if (elmt->adeType() == InterfaceElement::ADEType)
    {
        if (mInterfaceElmts.contains(elmt->uuid().toString()))
        {
            ADEConnectRelation *affectedCr = nullptr;
            for (auto cr : mConnectRelations)
                if (cr->cableInterface() == elmt)
                    affectedCr = cr;

            if (affectedCr)
            {
                delete affectedCr;
            }

            mInterfaceElmts.remove(elmt->uuid().toString());
            static_cast<InterfaceElement*>(elmt)->setCable(nullptr);
        }
    }
    else if (elmt->adeType() == CableSpliceElement::ADEType)
    {
        CableSpliceElement *cpElmt = static_cast<CableSpliceElement*>(elmt);
        if (mSpliceElmts.contains(cpElmt))
        {
            mSpliceElmts.removeAll(cpElmt);
            cpElmt->setCable(nullptr);
        }
    }
    else
    {
        return;
    }
}

void ADELogicCable::removeDcInterface(const QString &uuid)
{
    if (mInterfaceElmts.contains(uuid))
    {
        mInterfaceElmts.value(uuid)->setCable(nullptr);
        mInterfaceElmts.remove(uuid);
    }
}

void ADELogicCable::removeConnectRelation(ADEConnectRelation *cr)
{
    if (!cr)
        return;

    if (mConnectRelations.contains(cr))
    {
        mConnectRelations.removeAll(cr);
    }
}

ADEConnectRelation *ADELogicCable::getInterfaceConnectRelation(InterfaceElement *elmt) const
{
    for (auto cr : mConnectRelations)
    {
        if (cr->validElements().contains(elmt))
            return cr;
    }

    return nullptr;
}

ADEConnectRelation *ADELogicCable::getConnectRelationByCableCr(const cable::ConnectRelation &cbCr)
{
    for (auto cr : mConnectRelations)
    {
        if (cr->cableInterface()->uuid().toString() == cbCr.cableIfUuid)
        {
            if (cr->otherInterface() && (cr->otherInterface()->uuid().toString() == cbCr.otherIfUuid))
                return cr;
            else if (!cr->otherInterface() && cbCr.otherIfUuid.isEmpty())
                return cr;
            else
                return nullptr;
        }
        else
            continue;
    }

    return nullptr;
}

QList<CustomConductor *> ADELogicCable::allConductors() const
{
    QList<CustomConductor *> list;
//    QList<Element *> allElmts = mInterfaceElmts.values() + mSpliceElmts;

    QList<Element *> allElmts;
    for (auto e : mSpliceElmts)
        allElmts << e;
    for (auto e : mInterfaceElmts.values())
        allElmts << e;

    for (auto e : mInterfaceElmts.values())
    {
        for (auto c : e->conductors())
        {
            if (c->lineType() == CustomConductor::LineType)
            {
                CustomConductor *cc = static_cast<CustomConductor *>(c);
                // 只有当导线两端接口都属于电缆时导线自身才属于电缆
                if (allElmts.contains(cc->terminal1->parentElement()) &&
                        allElmts.contains(cc->terminal2->parentElement()))
                {
                    if (!list.contains(cc))
                        list << cc;
                }
            }
        }
    }

    for (auto e : mSpliceElmts)
    {
        for (auto cc : e->customConductors())
        {
            if (!list.contains(cc))
                list << cc;
        }
    }

    // 电缆由两个设备上的接口直连构成
    if (list.isEmpty() && (mInterfaceElmts.size() == 2))
    {
        for (auto c1 : mInterfaceElmts.first()->customConductors())
        {
            for (auto c2 : mInterfaceElmts.last()->customConductors())
            {
                if (c1 == c2)
                {
                    list << c1;
                    break;
                }
            }
        }
    }

    return list;
}

void ADELogicCable::adjustCableTextPositon() const
{
    qreal xSum = 0.0;
    qreal ySum = 0.0;
    int interfaceCount = 0;
    for (auto e : mInterfaceElmts)
    {
        xSum += e->scenePos().x();
        ySum += e->scenePos().y();
        interfaceCount++;
    }

    if (interfaceCount == 0)
        return;

    // 只有一个接口时将交点元素坐标加入计算
    if (interfaceCount == 1)
    {
        for (auto e : mSpliceElmts)
        {
            xSum += e->scenePos().x();
            ySum += e->scenePos().y();
            interfaceCount++;
        }
    }

    int verticalOffset = 15;
    QPointF pos = QPointF((xSum / interfaceCount), (ySum / interfaceCount) + verticalOffset);
    QSizeF textSize = mTextItem->document()->size();
    pos = QPointF(pos.x() - (textSize.width() / 2), pos.y() - (textSize.height() / 2));
    mTextItem->setPos(pos);
}

void ADELogicCable::setHighlighted(bool hl, bool ifEleHl)
{
    //ifEleHl 是否电缆端的接口同步高亮
    //布置图搜索时不同步，其他同步
    if (ifEleHl)
    {
        // 高亮接口元素
        for (auto e : mInterfaceElmts)
            e->setHighlighted(hl);
    }

    // 高亮导线
    //Conductor::Highlight cHl = hl ? Conductor::SearchResult : Conductor::None;
    Conductor::Highlight cHl;
    if (hl && !ifEleHl)
        cHl = Conductor::SearchResult;
    else if (hl && ifEleHl)
        cHl = Conductor::Normal;
    else
        cHl = Conductor::None;

    if (mLastHighlightConductors.isEmpty())
        mLastHighlightConductors = allConductors();
    for (auto c : mLastHighlightConductors)
        c->setHighlighted(cHl);

    if (!hl) mLastHighlightConductors.clear();
}

void ADELogicCable::showConductorHoverEffect(bool hovered)
{
    if (mLastHighlightConductors.isEmpty())
        mLastHighlightConductors = allConductors();

    Conductor::Highlight cHl = hovered ? Conductor::Hover : Conductor::None;
    for (auto c : mLastHighlightConductors)
        c->setHighlighted(cHl);

    if (!hovered) mLastHighlightConductors.clear();
}

void ADELogicCable::showTextHoverEffect(bool hovered)
{
    if (mTextItem)
        mTextItem->setHovered(hovered);
}

QList<InterfaceElement *> ADELogicCable::cableSelfInterfaceElements() const
{
    interfaceList ifObjs = PRJ_MD_GETTER->getAllIfObjs(mDiagram->ade()->prjUuid());

    QList<InterfaceElement *> list;
    for (auto e : mInterfaceElmts)
    {
        QString uuid = e->uuid().toString();
        if (ifObjs.contains(uuid))
        {
            interfacePtr ifObj = ifObjs.value(uuid);
            if (ifObj->fatherUuid == mUuid)
                list << e;
        }
    }
    return list;
}

QList<ADEConnectRelation*> ADELogicCable::relatedConnectRelations() const
{
    QList<ADEConnectRelation*> crs;
    for (auto e : interfaceElements())
    {
        if (e->connectRelation())
            crs << e->connectRelation();
    }

    return crs;
}

cable::ConnectRelation ADELogicCable::adeCrMapToCableCr(ADEConnectRelation *adeCr)
{
    cable::ConnectRelation cbCr;
    if (!adeCr)
        return cbCr;

    if (adeCr->cableInterface()) cbCr.cableIfUuid = adeCr->cableInterface()->uuid().toString();
    if (adeCr->isDirectConnect())
    {
        cbCr.ifType = cable::NotSelf;
        cbCr.otherIfUuid = QString();
    }
    else
    {
        cbCr.ifType = cable::Self;
        cbCr.otherIfUuid = adeCr->otherInterface()->uuid().toString();
    }

    return cbCr;
}

QPointF ADELogicCable::getSpliceElmtPos(Element *interfaceElmt)
{
    QPointF pos;

    int offset = 100;
    qreal xPos = interfaceElmt->scenePos().x();
    qreal yPos = interfaceElmt->scenePos().y();

    if      (interfaceElmt->sceneOrientation() == Qet::North) pos = QPointF(xPos, yPos - offset);
    else if (interfaceElmt->sceneOrientation() == Qet::East)  pos = QPointF(xPos + offset, yPos);
    else if (interfaceElmt->sceneOrientation() == Qet::South) pos = QPointF(xPos, yPos + offset);
    else if (interfaceElmt->sceneOrientation() == Qet::West)  pos = QPointF(xPos - offset, yPos);

    return pos;
}

QString ADELogicCable::adeMark() const
{
    if (mTextItem)
    {
        return mTextItem->getTextProp().getContentByTextName("代号");
    }

    return QString();
}

QString ADELogicCable::adeName() const
{
    if (mTextItem)
    {
        return mTextItem->getTextProp().getContentByTextName("名称");
    }

    return QString();
}
