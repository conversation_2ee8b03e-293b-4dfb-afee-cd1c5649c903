#ifndef PARTIMAGESCTRL_H
#define PARTIMAGESCTRL_H

#include "../../sqlCtrlBase.h"
#include "../sqlModel/partImageModel.h"
#include <QList>

class PartImagesCtrl : public SqlCtrlBase
{
public:
    PartImagesCtrl(const QSqlDatabase& db);
    ~PartImagesCtrl() = default;

    // 数据库操作
    bool add(const PartImageModel& image);
    bool update(const PartImageModel& image);
    bool remove(const QString& imageUuid); // Hard delete by ID
    bool softDelete(const QString& imageUuid); // Soft delete by ID

    // 查询
    PartImageModel getImage(const QString& imageUuid) const;
    QList<PartImageModel> getImagesForPart(const QString& partUuid) const;

public:
    QString tableName() const override { return "PartImages"; }
    QString primaryKeyField() const override { return "ImageUUID"; }
    bool recordExist(const QString& id) const override;

private:
    QString tableSql() const override;
};

#endif // PARTIMAGESCTRL_H 