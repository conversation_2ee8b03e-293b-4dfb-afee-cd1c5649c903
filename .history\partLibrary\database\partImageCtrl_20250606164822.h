#pragma once

#include <QList>
#include <QSqlDatabase>
#include "partImage.h"

namespace part_library
{
namespace database
{

/**
 * @brief Provides stateless data access functions for the PartImages table.
 *
 * This class contains static methods to perform CRUD operations on PartImages.
 * It does not hold any state (like a database connection or cached data)
 * and is intended for on-demand database interaction, which is suitable for
 * large data like images.
 */
class PartImageCtrl
{
public:
    /**
     * @brief Adds a new part image to the database.
     * @param db The database connection.
     * @param image The PartImage object to add.
     * @return True on success, false otherwise.
     */
    static bool addImage(QSqlDatabase& db, const PartImage& image);

    /**
     * @brief Updates an existing part image in the database.
     * @param db The database connection.
     * @param image The PartImage object with updated data.
     * @return True on success, false otherwise.
     */
    static bool updateImage(QSqlDatabase& db, const PartImage& image);

    /**
     * @brief Soft-deletes a part image from the database by its UUID.
     * @param db The database connection.
     * @param imageUUID The UUID of the image to remove.
     * @return True on success, false otherwise.
     */
    static bool removeImage(QSqlDatabase& db, const QString& imageUUID);

    /**
     * @brief Retrieves all non-deleted images for a given part UUID.
     * @param db The database connection.
     * @param partUUID The UUID of the part.
     * @return A list of PartImage objects.
     */
    static QList<PartImage> getImagesForPart(QSqlDatabase& db, const QString& partUUID);
};

} // namespace database
} // namespace part_library 