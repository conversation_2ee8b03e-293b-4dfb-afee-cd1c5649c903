#include "PartLibraryManager.h"
#include <QDir>
#include <QFileInfo>
#include <QDebug>
#include <QUuid>

// Include all required Ctrl headers
#include "globalDb/sqlCtrl/manufacturerctrl.h"
#include "globalDb/sqlCtrl/categoriesctrl.h"
#include "globalDb/sqlCtrl/attributedefinitionsctrl.h"
#include "globalDb/sqlCtrl/categoryattributelinksctrl.h"
#include "partDb/sqlCtrl/libraryinfoctrl.h"
#include "partDb/sqlCtrl/partctrl.h"
#include "partDb/sqlCtrl/datasheetsctrl.h"
#include "partDb/sqlCtrl/partdatasheetlinksctrl.h"
#include "partDb/sqlCtrl/partimagesctrl.h"
#include "partDb/sqlCtrl/changelogctrl.h"

PartLibraryManager::PartLibraryManager(QObject *parent) : QObject(parent)
{
}

PartLibraryManager::~PartLibraryManager()
{
    shutdown();
}

PartLibraryManager* PartLibraryManager::instance()
{
    static PartLibraryManager inst;
    return &inst;
}

bool PartLibraryManager::initialize(const QString& rootPath)
{
    m_rootPath = rootPath;
    QDir rootDir(m_rootPath);
    if (!rootDir.exists()) {
        qWarning() << "Part library root path does not exist:" << rootPath;
        return false;
    }

    // 1. 加载全局库
    QString globalDbPath = QDir(rootPath).filePath("global/global_data.db");
    if (!loadGlobalLibrary(globalDbPath)) {
        qCritical() << "Failed to load global library!";
        return false;
    }

    // 2. 发现并加载所有零件库
    for (const QFileInfo& dirInfo : rootDir.entryInfoList(QDir::Dirs | QDir::NoDotAndDotDot))
    {
        QDir libDir(dirInfo.absoluteFilePath());
        // 在子文件夹中查找 .ptlib 文件
        for (const QFileInfo& fileInfo : libDir.entryInfoList(QStringList() << "*.ptlib", QDir::Files))
        {
            if (!loadPartLibrary(fileInfo.absoluteFilePath())) {
                qWarning() << "Failed to load part library, skipping:" << fileInfo.absoluteFilePath();
            }
        }
    }

    qInfo() << "PartLibraryManager initialized, loaded" << m_partLibraries.count() << "part libraries.";
    return true;
}

void PartLibraryManager::shutdown()
{
    // Close all part library connections
    // Make a copy of keys because closePartLibrary modifies the hash
    const auto keys = m_partLibraries.keys();
    for (const QString& libraryId : keys) {
        closePartLibrary(libraryId);
    }
    m_partLibraries.clear();

    // Close global library connection
    if (m_globalContext.db.isOpen()) {
        QString connectionName = m_globalContext.db.connectionName();
        m_globalContext = GlobalLibraryContext(); // Reset context
        SimpleDbManager::close(connectionName);
        qInfo() << "Global library connection closed:" << connectionName;
    }
}

QString PartLibraryManager::createNewPartLibrary(const QString& libraryName, const QString& directoryPath, QString& errorInfo)
{
    QDir dir(directoryPath);
    if (!dir.exists()) {
        if (!dir.mkpath(".")) {
            errorInfo = QString("无法创建目录: %1").arg(directoryPath);
            return QString();
        }
    }

    // 从完整路径中提取最后的文件夹名作为文件名
    QString libFileName = QFileInfo(directoryPath).fileName() + ".ptlib";
    QString filePath = dir.filePath(libFileName);

    if (QFile::exists(filePath)) {
        errorInfo = QString("库文件已存在: %1").arg(filePath);
        return QString();
    }

    // 1. Create and connect to the new database
    PartLibraryContext context;
    context.filePath = filePath;
    QString connectionName = filePath; // Use file path for unique connection name
    QSqlError dbError;

    if (!SimpleDbManager::connect(filePath, dbError, connectionName, context.db)) {
        errorInfo = QString("无法创建或连接到新的数据库文件: %1. 错误: %2").arg(filePath, dbError.text());
        return QString();
    }

    // 2. Instantiate all controllers
    context.libraryInfoCtrl = std::make_unique<LibraryInfoCtrl>(context.db);
    context.partCtrl = std::make_unique<PartCtrl>(context.db);
    context.datasheetsCtrl = std::make_unique<DatasheetsCtrl>(context.db);
    context.partDatasheetLinksCtrl = std::make_unique<PartDatasheetLinksCtrl>(context.db);
    context.partImagesCtrl = std::make_unique<PartImagesCtrl>(context.db);
    context.changeLogCtrl = std::make_unique<ChangeLogCtrl>(context.db);

    // 3. Create all tables
    bool ok = true;
    ok &= context.libraryInfoCtrl->createTable(dbError);
    ok &= context.partCtrl->createTable(dbError);
    ok &= context.datasheetsCtrl->createTable(dbError);
    ok &= context.partDatasheetLinksCtrl->createTable(dbError);
    ok &= context.partImagesCtrl->createTable(dbError);
    ok &= context.changeLogCtrl->createTable(dbError);

    if (!ok) {
        errorInfo = QString("在新库中创建数据表失败: %1").arg(dbError.text());
        SimpleDbManager::close(connectionName);
        QFile::remove(filePath); // Clean up failed creation
        return QString();
    }

    // 4. Create and insert initial LibraryInfo
    LibraryInfoModel info;
    info.id = QUuid::createUuid().toString(QUuid::WithoutBraces);
    info.libraryName = libraryName;
    info.fileFormatVersion = 1; // Or some constant
    info.isOfficial = false;
    info.creationTime = QDateTime::currentDateTime();
    info.updateTime = info.creationTime;
    info.description = QString("'%1'的用户创建库").arg(libraryName);

    if (!context.libraryInfoCtrl->insert(info)) {
        errorInfo = "无法向新库中写入元信息(LibraryInfo)。";
        SimpleDbManager::close(connectionName);
        QFile::remove(filePath);
        return QString();
    }

    context.libraryId = info.id;

    // 5. Store the context
    m_partLibraries.insert(context.libraryId, std::move(context));

    qInfo() << "Successfully created new part library:" << libraryName << "with ID:" << info.id;
    return info.id;
}


bool PartLibraryManager::deletePartLibrary(const QString& libraryId)
{
    if (!m_partLibraries.contains(libraryId)) {
        return false; // Or should we return true if it doesn't exist?
    }

    QString filePath = m_partLibraries.value(libraryId).filePath;

    // First, close connection and remove from manager
    closePartLibrary(libraryId);

    // Then, delete the physical file
    if (QFile::remove(filePath)) {
         qInfo() << "Successfully deleted library file:" << filePath;
        // Optional: try to remove the parent directory if it is empty
        QDir parentDir = QFileInfo(filePath).dir();
        if (parentDir.isEmpty()) {
            parentDir.rmdir(".");
        }
        return true;
    } else {
        qWarning() << "Failed to delete library file:" << filePath;
        return false;
    }
}

QList<LibraryInfoModelPtr> PartLibraryManager::getAllLoadedLibraries() const
{
    QList<LibraryInfoModelPtr> result;
    for (const auto& context : m_partLibraries) {
        // The LibraryInfo is fetched on load, let's fetch it again for latest data
        if (context.libraryInfoCtrl) {
            result.append(context.libraryInfoCtrl->fetch());
        }
    }
    return result;
}

PartViewModel PartLibraryManager::getPartViewModel(const QString& libraryId, const QString& partId)
{
    if (!m_partLibraries.contains(libraryId)) {
        qWarning() << "getPartViewModel called with invalid libraryId:" << libraryId;
        return PartViewModel();
    }

    const auto& partContext = m_partLibraries.value(libraryId);
    PartModelPtr partModel = partContext.partCtrl->fetch(partId);
    if (!partModel) {
        return PartViewModel();
    }

    if (!m_globalContext.attributeDefinitionsCtrl) {
        qCritical() << "Global context is not initialized, cannot create PartViewModel.";
        return PartViewModel();
    }

    return PartViewModelAssembler::createViewModel(partModel, *m_globalContext.attributeDefinitionsCtrl);
}

PartModelList PartLibraryManager::getPartsInLibrary(const QString& libraryId) const
{
    if (!m_partLibraries.contains(libraryId)) {
        qWarning() << "getPartsInLibrary called with invalid libraryId:" << libraryId;
        return PartModelList();
    }
    const auto& partContext = m_partLibraries.value(libraryId);
    return partContext.partCtrl->fetchAll();
}

ManufacturerModelList PartLibraryManager::getManufacturers() const
{
    if (m_globalContext.manufacturerCtrl) {
        return m_globalContext.manufacturerCtrl->fetchAll();
    }
    return ManufacturerModelList();
}

CategoriesModelList PartLibraryManager::getCategories() const
{
    if (m_globalContext.categoriesCtrl) {
        return m_globalContext.categoriesCtrl->fetchAll();
    }
    return CategoriesModelList();
}

bool PartLibraryManager::loadGlobalLibrary(const QString& globalDbPath)
{
    if (!QFile::exists(globalDbPath)) {
        qCritical() << "Global database file not found at" << globalDbPath;
        return false;
    }
    
    QString connectionName = "global_db_connection";
    QSqlError error;

    if (!SimpleDbManager::connect(globalDbPath, error, connectionName, m_globalContext.db)) {
        qCritical() << "Failed to connect to global database:" << error.text();
        return false;
    }

    m_globalContext.manufacturerCtrl = std::make_unique<ManufacturerCtrl>(m_globalContext.db);
    m_globalContext.categoriesCtrl = std::make_unique<CategoriesCtrl>(m_globalContext.db);
    m_globalContext.attributeDefinitionsCtrl = std::make_unique<AttributeDefinitionsCtrl>(m_globalContext.db);
    m_globalContext.categoryAttributeLinksCtrl = std::make_unique<CategoryAttributeLinksCtrl>(m_globalContext.db);

    qInfo() << "Global library loaded successfully from" << globalDbPath;
    return true;
}

bool PartLibraryManager::loadPartLibrary(const QString& ptlibPath)
{
    PartLibraryContext context;
    context.filePath = ptlibPath;

    QString connectionName = ptlibPath;
    QSqlError error;

    if (!SimpleDbManager::connect(ptlibPath, error, connectionName, context.db)) {
        qWarning() << "Failed to connect to part library" << ptlibPath << ":" << error.text();
        return false;
    }

    // Instantiate all controllers for the part library
    context.libraryInfoCtrl = std::make_unique<LibraryInfoCtrl>(context.db);
    context.partCtrl = std::make_unique<PartCtrl>(context.db);
    context.datasheetsCtrl = std::make_unique<DatasheetsCtrl>(context.db);
    context.partDatasheetLinksCtrl = std::make_unique<PartDatasheetLinksCtrl>(context.db);
    context.partImagesCtrl = std::make_unique<PartImagesCtrl>(context.db);
    context.changeLogCtrl = std::make_unique<ChangeLogCtrl>(context.db);

    LibraryInfoModelPtr info = context.libraryInfoCtrl->fetch();
    if (!info) {
        qWarning() << "Could not fetch library info from" << ptlibPath << ". Is it a valid library file?";
        SimpleDbManager::close(connectionName);
        return false;
    }
    context.libraryId = info->id;

    if (m_partLibraries.contains(context.libraryId)) {
        qWarning() << "Duplicate library ID found:" << context.libraryId << ". Skipping" << ptlibPath;
        SimpleDbManager::close(connectionName);
        return false;
    }

    m_partLibraries.insert(context.libraryId, std::move(context));
    qInfo() << "Loaded part library:" << info->libraryName << "(" << info->id << ") from" << ptlibPath;

    return true;
}

void PartLibraryManager::closePartLibrary(const QString& libraryId)
{
    if (!m_partLibraries.contains(libraryId)) {
        return;
    }

    PartLibraryContext context = m_partLibraries.take(libraryId);
    QString connectionName = context.db.connectionName();
    context = PartLibraryContext(); // Reset context, releasing all unique_ptrs
    SimpleDbManager::close(connectionName);
    qInfo() << "Part library connection closed:" << connectionName;
} 