#include <QCoreApplication>
#include <QDebug>
#include <QUuid>
#include <QSqlDatabase>
#include <QSqlError>
#include <stdexcept>

// Assume TEST_INFO and TEST_ERROR are defined somewhere globally
#ifndef TEST_INFO
#define TEST_INFO(msg) qDebug("%s", qPrintable(msg))
#endif
#ifndef TEST_ERROR
#define TEST_ERROR(msg) qWarning("%s", qPrintable(msg))
#endif

// Include Global DB Controllers
#include "../database/globalDb/sqlCtrl/manufacturerctrl.h"
#include "../database/globalDb/sqlCtrl/categoriesctrl.h"
#include "../database/globalDb/sqlCtrl/attributedefinitionsctrl.h"
#include "../database/globalDb/sqlCtrl/categoryattributelinksctrl.h"

// Include Global DB Models
#include "../database/globalDb/sqlModel/manufacturermodel.h"
#include "../database/globalDb/sqlModel/categoriesmodel.h"
#include "../database/globalDb/sqlModel/attributedefinitionsmodel.h"
#include "../database/globalDb/sqlModel/categoryattributelinksmodel.h"

// A simple mock for the SQL_DB->connect, assuming it manages connections.
// In a real scenario, this would be part of your application's database manager.
namespace SimpleDbManager {
    bool connect(const QString& path, QString& err, const QString& connectionName, QSqlDatabase& db) {
        db = QSqlDatabase::addDatabase("QSQLITE", connectionName);
        db.setDatabaseName(path);
        if (!db.open()) {
            err = db.lastError().text();
            return false;
        }
        return true;
    }
    void close(const QString& connectionName) {
        QSqlDatabase::removeDatabase(connectionName);
    }
}


void testManufacturerCtrl(QSqlDatabase& db) {
    qDebug() << "--- Testing ManufacturerCtrl ---";
    ManufacturerCtrl ctrl(db);
    if (!ctrl.isTableExist()) {
        QSqlError err;
        if (!ctrl.createTable(err)) {
            TEST_ERROR(err.text());
            throw std::runtime_error(err.text().toStdString());
        }
        qDebug() << "Manufacturer table created.";
    }

    // Insert
    QString newMfId;
    {
        auto mfObj = ManufacturerModel::createPtr();
        mfObj->name = "Test Manufacturer";
        mfObj->website = "www.test.com";
        mfObj->description = "A test manufacturer.";
        if (ctrl.insert(mfObj)) {
            newMfId = mfObj->id;
            qDebug() << "Inserted new manufacturer with ID:" << newMfId;
        } else {
            qDebug() << "Insert failed";
        }
    }

    // Read
    {
        auto objs = ctrl.fetchAll();
        qDebug() << "Reading all manufacturers, count:" << objs.count();
        for (const auto& obj : objs) {
            qDebug() << "  - ID:" << obj->id << "Name:" << obj->name;
        }
    }

    // Modify
    if(!newMfId.isEmpty())
    {
        auto mfObj = ctrl.fetch(newMfId);
        if (mfObj) {
            mfObj->name = "Modified Manufacturer";
            if(ctrl.modify(mfObj)) {
                qDebug() << "Modified manufacturer with ID:" << newMfId;
            } else {
                 qDebug() << "Modify failed for ID:" << newMfId;
            }
        }
    }


    // Delete
    if(!newMfId.isEmpty())
    {
        if (ctrl.remove(newMfId)) {
            qDebug() << "Deleted manufacturer with ID:" << newMfId;
        } else {
            qDebug() << "Delete failed for ID:" << newMfId;
        }
    }
     qDebug() << "--- Finished Testing ManufacturerCtrl ---";
}

void testCategoriesCtrl(QSqlDatabase& db, QString& outParentCategoryId, QString& outChildCategoryId) {
    qDebug() << "--- Testing CategoriesCtrl ---";
    CategoriesCtrl ctrl(db);
    if (!ctrl.isTableExist()) {
        QSqlError err;
        if (!ctrl.createTable(err)) {
            TEST_ERROR(err.text());
            throw std::runtime_error(err.text().toStdString());
        }
        qDebug() << "Categories table created.";
    }

    // Insert Parent
    {
        auto catObj = CategoriesModel::createPtr();
        catObj->name = "Parent Category";
        catObj->description = "This is a parent category.";
        if(ctrl.insert(catObj)) {
            outParentCategoryId = catObj->id;
            qDebug() << "Inserted new parent category with ID:" << outParentCategoryId;
        } else {
            qDebug() << "Insert parent category failed.";
        }
    }

    // Insert Child
    if(!outParentCategoryId.isEmpty())
    {
        auto catObj = CategoriesModel::createPtr();
        catObj->name = "Child Category";
        catObj->description = "This is a child category.";
        catObj->parentCategoryUUID = outParentCategoryId;
        if(ctrl.insert(catObj)) {
            outChildCategoryId = catObj->id;
            qDebug() << "Inserted new child category with ID:" << outChildCategoryId;
        } else {
            qDebug() << "Insert child category failed.";
        }
    }

    // Read
    {
        auto objs = ctrl.fetchAll();
        qDebug() << "Reading all categories, count:" << objs.count();
        for (const auto& obj : objs) {
            qDebug() << "  - ID:" << obj->id << "Name:" << obj->name << "ParentID:" << obj->parentCategoryUUID;
        }
    }

    // Modify
    if(!outChildCategoryId.isEmpty())
    {
        auto catObj = ctrl.fetch(outChildCategoryId);
        if(catObj) {
            catObj->name = "Modified Child Category";
            if(ctrl.modify(catObj)) {
                qDebug() << "Modified category with ID:" << outChildCategoryId;
            } else {
                qDebug() << "Modify category failed for ID:" << outChildCategoryId;
            }
        }
    }

    // Delete
    if(!outChildCategoryId.isEmpty()) {
        if(ctrl.remove(outChildCategoryId)) {
             qDebug() << "Deleted category with ID:" << outChildCategoryId;
        } else {
            qDebug() << "Delete category failed for ID:" << outChildCategoryId;
        }
    }
    if(!outParentCategoryId.isEmpty()) {
        if(ctrl.remove(outParentCategoryId)) {
             qDebug() << "Deleted category with ID:" << outParentCategoryId;
        } else {
             qDebug() << "Delete category failed for ID:" << outParentCategoryId;
        }
    }
    qDebug() << "--- Finished Testing CategoriesCtrl ---";
}

void testAttributeDefinitionsCtrl(QSqlDatabase& db, QString& outAttrId) {
    qDebug() << "--- Testing AttributeDefinitionsCtrl ---";
    AttributeDefinitionsCtrl ctrl(db);
    if (!ctrl.isTableExist()) {
        QSqlError err;
        if (!ctrl.createTable(err)) {
            TEST_ERROR(err.text());
            throw std::runtime_error(err.text().toStdString());
        }
        qDebug() << "AttributeDefinitions table created.";
    }

    // Insert
    {
        auto attrObj = AttributeDefinitionsModel::createPtr();
        attrObj->name = "Voltage";
        attrObj->dataType = "double";
        attrObj->unit = "V";
        if(ctrl.insert(attrObj)) {
            outAttrId = attrObj->id;
            qDebug() << "Inserted new attribute with ID:" << outAttrId;
        } else {
            qDebug() << "Insert attribute failed.";
        }
    }

    // Read
    {
        auto objs = ctrl.fetchAll();
        qDebug() << "Reading all attributes, count:" << objs.count();
        for (const auto& obj : objs) {
            qDebug() << "  - ID:" << obj->id << "Name:" << obj->name;
        }
    }

    // Modify
    if(!outAttrId.isEmpty()){
        auto attrObj = ctrl.fetch(outAttrId);
        if(attrObj) {
            attrObj->name = "Voltage (DC)";
            if(ctrl.modify(attrObj)){
                qDebug() << "Modified attribute with ID:" << outAttrId;
            } else {
                qDebug() << "Modify attribute failed for ID:" << outAttrId;
            }
        }
    }

    // Delete
    if(!outAttrId.isEmpty()) {
        if(ctrl.remove(outAttrId)) {
            qDebug() << "Deleted attribute with ID:" << outAttrId;
        } else {
            qDebug() << "Delete attribute failed for ID:" << outAttrId;
        }
    }
     qDebug() << "--- Finished Testing AttributeDefinitionsCtrl ---";
}

void testCategoryAttributeLinksCtrl(QSqlDatabase& db, const QString& categoryId, const QString& attributeId)
{
    qDebug() << "--- Testing CategoryAttributeLinksCtrl ---";

    if(categoryId.isEmpty() || attributeId.isEmpty()) {
        qDebug() << "Category or Attribute ID is empty, skipping test.";
        return;
    }

    CategoryAttributeLinksCtrl ctrl(db);
    if (!ctrl.isTableExist()) {
        QSqlError err;
        if (!ctrl.createTable(err)) {
            TEST_ERROR(err.text());
            throw std::runtime_error(err.text().toStdString());
        }
        qDebug() << "Category_Attribute_Links table created.";
    }
    
    QString newLinkId;
    // Insert
    {
        auto linkObj = CategoryAttributeLinksModel::createPtr();
        linkObj->categoryUUID = categoryId;
        linkObj->attributeUUID = attributeId;
        if(ctrl.insert(linkObj)) {
            newLinkId = linkObj->id;
            qDebug() << "Inserted new link with ID:" << newLinkId;
        } else {
            qDebug() << "Insert link failed.";
        }
    }

    // Read
    {
        auto objs = ctrl.fetchAll();
        qDebug() << "Reading all links, count:" << objs.count();
        for (const auto& obj : objs) {
            qDebug() << "  - ID:" << obj->id << "CatID:" << obj->categoryUUID << "AttrID:" << obj->attributeUUID;
        }
    }
    
    // Modify - Links are often not modified, but let's test if possible (e.g., changing default value)
    if(!newLinkId.isEmpty()){
        auto linkObj = ctrl.fetch(newLinkId);
        if(linkObj) {
            linkObj->defaultValue = "1.0";
            if(ctrl.modify(linkObj)) {
                qDebug() << "Modified link with ID:" << newLinkId;
            } else {
                qDebug() << "Modify link failed for ID:" << newLinkId;
            }
        }
    }

    // Delete
    if(!newLinkId.isEmpty()){
        if(ctrl.remove(newLinkId)) {
            qDebug() << "Deleted link with ID:" << newLinkId;
        } else {
            qDebug() << "Delete link failed for ID:" << newLinkId;
        }
    }

    qDebug() << "--- Finished Testing CategoryAttributeLinksCtrl ---";
}


void run_globalDb_tests()
{
    qDebug() << "\n\n===== Starting Global DB Tests... =====";

    QString err;
    QString uuid = QUuid::createUuid().toString();
    QSqlDatabase db;
    // An application instance is expected to exist, so applicationDirPath is valid.
    QString path = QString("%1/global_test.db").arg(QCoreApplication::applicationDirPath());

    if (!SimpleDbManager::connect(path, err, uuid, db))
    {
        QString errInfo = QString("Database connection failed: %1").arg(err);
        TEST_ERROR(errInfo);
        return;
    }

    qDebug() << "Database connected:" << path;

    try {
        testManufacturerCtrl(db);

        QString parentCatId, childCatId, attrId;
        testCategoriesCtrl(db, parentCatId, childCatId);
        testAttributeDefinitionsCtrl(db, attrId);

        // Re-create a category and attribute to ensure they exist for the link test
        CategoriesCtrl catCtrl(db);
        auto cat = CategoriesModel::createPtr();
        cat->name = "Link Test Category";
        catCtrl.insert(cat);

        AttributeDefinitionsCtrl attrCtrl(db);
        auto attr = AttributeDefinitionsModel::createPtr();
        attr->name = "Link Test Attribute";
        attrCtrl.insert(attr);

        testCategoryAttributeLinksCtrl(db, cat->id, attr->id);

        // Clean up link test items
        catCtrl.remove(cat->id);
        attrCtrl.remove(attr->id);

    } catch (const std::runtime_error& e) {
        TEST_ERROR(QString("A runtime error occurred: %1").arg(e.what()));
        SimpleDbManager::close(uuid);
        return;
    }


    SimpleDbManager::close(uuid);
    qDebug() << "Database connection closed.";
    qDebug() << "===== Finished Global DB Tests. =====";
} 
