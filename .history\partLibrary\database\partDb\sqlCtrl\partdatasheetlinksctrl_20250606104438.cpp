#include "partdatasheetlinksctrl.h"

#include "3rdParty/logBusiness/logbusiness.h"

#include <QApplication>

PartDatasheetLinksCtrl::PartDatasheetLinksCtrl(const QSqlDatabase &db) :
    SqlCtrlBase(db)
{
    if(!db.isValid())
    {
        LOG_ERROR("db not valid");
    }
    else
    {
        loadTable();
    }
}

PartDatasheetLinksCtrl::~PartDatasheetLinksCtrl()
{
    for(auto itor = mAllObj.begin(); itor != mAllObj.end(); )
    {
        auto obj = itor.value();
        obj.reset();

        mAllObj.erase(itor++);
    }
}

PartDatasheetLinksModelList PartDatasheetLinksCtrl::fetchAll() const
{
    return mAllObj;
}

PartDatasheetLinksModelPtr PartDatasheetLinksCtrl::fetch(const QString &id) const
{
    if (modelExist(id))
    {
        return mAllObj.value(id);
    }
    else
    {
        return nullptr;
    }
}

bool PartDatasheetLinksCtrl::modelExist(const QString &id) const
{
    return mAllObj.contains(id);
}

bool PartDatasheetLinksCtrl::insert(const PartDatasheetLinksModelPtr &obj)
{
    if (obj.get() == nullptr)
        return false;

    if (insertTableRecord(*obj.get()))
    {
        mAllObj.insert(obj->id, obj);
        return true;
    }
    else
    {
        return false;
    }
}

bool PartDatasheetLinksCtrl::modify(const PartDatasheetLinksModelPtr &newValueObj)
{
    if (newValueObj.get() == nullptr)
        return false;

    QString id = newValueObj->id;
    if (!mAllObj.contains(id))
    {
        LOG_ERROR(QString("无法修改记录：该记录不存在 %1").arg(id));
        return false;
    }

    if (updateTableRecord(*newValueObj.get()))
    {
        auto obj = mAllObj.value(id);
        obj->partUUID = newValueObj->partUUID;
        obj->datasheetUUID = newValueObj->datasheetUUID;

        return true;
    }
    else
    {
        return false;
    }
}

bool PartDatasheetLinksCtrl::remove(const QString &id)
{
    if (!mAllObj.contains(id))
    {
        LOG_ERROR(QString("无法删除记录：该记录不存在 %1").arg(id));
        return false;
    }

    if (deleteTableRecord(id))
    {
        auto obj = mAllObj.take(id);
        obj.reset();

        return true;
    }
    else
    {
        return false;
    }
}

bool PartDatasheetLinksCtrl::recordExist(const QString &id) const
{
    return mAllObj.contains(id);
}

QString PartDatasheetLinksCtrl::tableSql() const
{
    return QString(R"(
        CREATE TABLE Part_Datasheet_Links (
            LinkUUID        TEXT PRIMARY KEY,       -- [主键] 关联记录自身的唯一标识符, UUID格式
            PartUUID   TEXT NOT NULL,          -- [外键] 指向 Parts(PartUUID)
            DatasheetUUID   TEXT NOT NULL,          -- [外键] 指向 Datasheets(DatasheetUUID)
            IsDeleted       INTEGER NOT NULL DEFAULT 0, -- [字段] 软删除标志，0表示未删除，1表示已删除

            FOREIGN KEY (PartUUID) REFERENCES Parts(PartUUID) ON DELETE CASCADE,
            FOREIGN KEY (DatasheetUUID) REFERENCES Datasheets(DatasheetUUID) ON DELETE CASCADE,
            UNIQUE (PartUUID, DatasheetUUID) -- 防止重复关联
        );
    )");
}

void PartDatasheetLinksCtrl::loadTable()
{
    QString sql = QString("select * from %1 where IsDeleted='0'").arg(tableName());
    QSqlQuery query(getCurrentDatabase());
    if(query.exec(sql))
    {
        while (query.next())
        {
            QSqlRecord record = query.record();

            PartDatasheetLinksModelPtr obj;
            obj.reset(new PartDatasheetLinksModel);
            obj->dbToModel(record);

            mAllObj.insert(obj->id, obj);
        }
    }
} 