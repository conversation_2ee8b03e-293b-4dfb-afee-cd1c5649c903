#ifndef CHANGELOGMODEL_H
#define CHANGELOGMODEL_H

#include "../../sqlModelBase.h"
#include <QUuid>
#include <QDateTime>

class ChangeLogModel : public SqlModelBase
{
public:
    ChangeLogModel() : id(QUuid::createUuid().toString()), timestamp(QDateTime::currentDateTime()){}

public:
    // 模型属性
    QString   id;            ///< 日志条目自身的唯一标识符，UUID格式
    QDateTime timestamp;     ///< 变更发生的时间戳
    QString   userName;      ///< 执行变更操作的用户名
    QString   action;        ///< 操作类型（CREATE, UPDATE, DELETE）
    QString   recordUuid;    ///< 被修改记录的UUID，通常是 PartUUID
    QString   fieldName;     ///< (当Action为UPDATE时) 被修改的具体字段名
    QString   oldValue;      ///< (当Action为UPDATE/DELETE时) 字段的旧值
    QString   newValue;      ///< (当Action为CREATE/UPDATE时) 字段的新值
    QString   description;   ///< 用户对本次变更的备注或系统自动生成的摘要

public:
    QString tableName() const override { return "ChangeLog"; }
    QString primaryKeyField() const override { return "ChangeLogUUID"; }
    QVariantMap modelToDbMap() const override;
    void dbToModel(const QSqlRecord& record) override;

public:
    // 字段名称常量
    static const QString FIELD_CHANGELOG_UUID;
    static const QString FIELD_TIMESTAMP;
    static const QString FIELD_USER_NAME;
    static const QString FIELD_ACTION;
    static const QString FIELD_RECORD_UUID;
    static const QString FIELD_FIELD_NAME;
    static const QString FIELD_OLD_VALUE;
    static const QString FIELD_NEW_VALUE;
    static const QString FIELD_DESCRIPTION;
};

#endif // CHANGELOGMODEL_H 