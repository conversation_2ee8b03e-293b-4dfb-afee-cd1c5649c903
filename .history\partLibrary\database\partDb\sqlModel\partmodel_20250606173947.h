#ifndef PARTMODEL_H
#define PARTMODEL_H

//============================================================================
/// file
/// brief      元器件信息主数据模型
/// author     wm
/// version    1.0
/// date       2025/06/05
/// todo
//============================================================================

#include "../../sqlModelBase.h"
#include <memory>
#include <QUuid>
#include <QDateTime>
#include <QList>
#include <QVariant>
#include <QStringList>

class PartModel : public SqlModelBase
{
public:
    PartModel() : id(QUuid::createUuid().toString()),
        creationTimestamp(QDateTime::currentDateTime()),
        lastModifiedTimestamp(QDateTime::currentDateTime()),
        revision(1){}

    struct CoreAttrInfo
    {
        QString  uuid;   ///< 核心属性uuid, 为AttributeDefinitions表主键
        QVariant value;  ///< 属性值
    };

    struct CustomAttrInfo
    {
        QString name;  ///< 自定义属性名称
        QString value; ///< 自定义属性值
    };

public:
    // 模型属性
    QString id;                 ///< 元器件uuid
    QString partNumber;         ///< 零件号、名称
    QString categoryUUID;       ///< 分类UUID
    QString manufacturerUUID;   ///< 制造商UUID
    QString description;        ///< 描述
    QString lifecycleStatus;    ///< 生命周期状态
    QList<CoreAttrInfo> coreAttributes; ///< 核心属性
    QList<CustomAttrInfo> customAttributes;///< 自定义属性
    QStringList tags;           ///< 标签
    QDateTime creationTimestamp;    ///< 创建时间
    QString createdBy;          ///< 创建者
    QDateTime lastModifiedTimestamp;///< 最后修改时间
    QString lastModifiedBy;     ///< 最后修改者
    int     revision;           ///< 修订版本号

public:
    QString tableName()       const override {return "Parts";}
    QString primaryKeyField() const override {return "PartUUID";}
    QVariantMap modelToDbMap() const override;
    void dbToModel(const QSqlRecord& record) override;

public:


public:
    // 字段名称常量
    static const QString FIELD_ID;
    static const QString FIELD_PART_NUMBER;
    static const QString FIELD_CATEGORY_UUID;
    static const QString FIELD_MANUFACTURER_UUID;
    static const QString FIELD_DESCRIPTION;
    static const QString FIELD_LIFECYCLE_STATUS;
    static const QString FIELD_CORE_ATTRIBUTES_JSON;
    static const QString FIELD_CUSTOM_ATTRIBUTES_JSON;
    static const QString FIELD_TAGS_JSON;
    static const QString FIELD_CREATION_TIMESTAMP;
    static const QString FIELD_CREATED_BY;
    static const QString FIELD_LAST_MODIFIED_TIMESTAMP;
    static const QString FIELD_LAST_MODIFIED_BY;
    static const QString FIELD_REVISION;
    static const QString FIELD_IS_DELETED;

    /*
     * createPtr()函数主要有两个方面的作用:
     * 1. 提供统一且方便的创建对象方式；
     * 2. 提供可定位断点；
    */
    static std::shared_ptr<PartModel> createPtr()
    {
        return std::make_shared<PartModel>();
    }

private:
    void coreAttrFromJson(const QString& json);
    QString coreAttrToJson() const;

    void customAttrFromJson(const QString& json);
    QString customAttrToJson() const;

    void tagsFromJson(const QString& json);
    QString tagsToJson() const;
};

using PartsModelPtr = std::shared_ptr<PartModel>;
using PartsModelList = QHash<QString, PartsModelPtr>;

#endif // PARTMODEL_H
