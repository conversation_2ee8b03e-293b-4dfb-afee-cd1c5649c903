#include "categoriesmodel.h"
#include <QJsonDocument>
#include <QJsonArray>
#include <QJsonObject>

// 字段名称定义
const QString CategoriesModel::FIELD_ID = "CategoryUUID";
const QString CategoriesModel::FIELD_NAME = "Name";
const QString CategoriesModel::FIELD_DESCRIPTION = "Description";
const QString CategoriesModel::FIELD_PARENT_CATEGORY_UUID = "ParentCategoryUUID";
const QString CategoriesModel::FIELD_IRDI = "IRDI";
const QString CategoriesModel::FIELD_USAGE_UNIT_JSON = "UsageUnit_JSON";
const QString CategoriesModel::FIELD_IS_DELETED = "IsDeleted";

QVariantMap CategoriesModel::modelToDbMap() const
{
    QVariantMap map;
    map.insert(FIELD_ID, id);
    map.insert(FIELD_NAME, name);
    map.insert(FIELD_DESCRIPTION, description);
    map.insert(FIELD_PARENT_CATEGORY_UUID, parentCategoryUUID);
    map.insert(FIELD_IRDI, irdi);
    map.insert(FIELD_USAGE_UNIT_JSON, usageUnitToJson());
    return map;
}

void CategoriesModel::dbToModel(const QSqlRecord &record)
{
    QMap<QString, QVariant> infos = getRecordValues(record);

    if (infos.contains(FIELD_ID)) this->id = infos[FIELD_ID].toString();
    if (infos.contains(FIELD_NAME)) this->name = infos[FIELD_NAME].toString();
    if (infos.contains(FIELD_DESCRIPTION)) this->description = infos[FIELD_DESCRIPTION].toString();
    if (infos.contains(FIELD_PARENT_CATEGORY_UUID)) this->parentCategoryUUID = infos[FIELD_PARENT_CATEGORY_UUID].toString();
    if (infos.contains(FIELD_IRDI)) this->irdi = infos[FIELD_IRDI].toString();
    if (infos.contains(FIELD_USAGE_UNIT_JSON)) this->usageUnitFromJson(infos[FIELD_USAGE_UNIT_JSON].toString());
}

void CategoriesModel::usageUnitFromJson(const QString &json)
{
    usageUnits.clear();
    QJsonDocument doc = QJsonDocument::fromJson(json.toUtf8());
    if (doc.isArray()) {
        QJsonArray jsonArray = doc.array();
        for (const QJsonValue &value : jsonArray) {
            QJsonObject jsonObj = value.toObject();
            UsageUnitInfo info;
            if (jsonObj.contains("name")) {
                info.name = jsonObj["name"].toString();
            }
            if (jsonObj.contains("value")) {
                info.value = jsonObj["value"].toString();
            }
            usageUnits.append(info);
        }
    }
}

QString CategoriesModel::usageUnitToJson() const
{
    QJsonArray jsonArray;
    for (const auto& info : usageUnits) {
        QJsonObject jsonObj;
        jsonObj["name"] = info.name;
        jsonObj["value"] = info.value;
        jsonArray.append(jsonObj);
    }
    return QJsonDocument(jsonArray).toJson(QJsonDocument::Compact);
} 