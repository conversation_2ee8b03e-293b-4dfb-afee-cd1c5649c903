#include "categoriesctrl.h"
#include <QSqlQuery>
#include <QSqlError>
#include <QDebug>

CategoriesCtrl::CategoriesCtrl(const QString &dbPath, QObject *parent)
    : SqlCtrlBase(dbPath, "Categories", parent)
{
    m_uuidKeyName = "CategoryUUID";
}

bool CategoriesCtrl::checkAndCreateTable()
{
    if (!m_db.isOpen()) {
        qCritical() << "Database not open!";
        return false;
    }

    QSqlQuery query(m_db);
    QString createTableQuery = R"(
        CREATE TABLE IF NOT EXISTS Categories (
            CategoryUUID        TEXT PRIMARY KEY,
            Name                TEXT NOT NULL UNIQUE,
            Description         TEXT,
            ParentCategoryUUID  TEXT,
            IRDI                TEXT UNIQUE,
            UsageUnit_JSON      TEXT NOT NULL,
            IsDeleted           INTEGER NOT NULL DEFAULT 0
        )
    )";

    if (!query.exec(createTableQuery)) {
        qCritical() << "Failed to create Categories table:" << query.lastError();
        return false;
    }
    return true;
}

bool CategoriesCtrl::insert(const CategoriesModel &model)
{
    return SqlCtrlBase::insert(model.toVariantMap());
}

bool CategoriesCtrl::update(const CategoriesModel &model)
{
    return SqlCtrlBase::update(model.getCategoryUUID(), model.toVariantMap());
}

bool CategoriesCtrl::softDelete(const QString &uuid)
{
    return SqlCtrlBase::softDelete(uuid);
}

QSharedPointer<CategoriesModel> CategoriesCtrl::getByUUID(const QString &uuid)
{
    QVariantMap map = SqlCtrlBase::getByUUID(uuid);
    if (map.isEmpty()) {
        return nullptr;
    }
    auto model = QSharedPointer<CategoriesModel>::create();
    model->fromVariantMap(map);
    return model;
}

QList<QSharedPointer<CategoriesModel>> CategoriesCtrl::getAll()
{
    QList<QVariantMap> maps = SqlCtrlBase::getAll();
    QList<QSharedPointer<CategoriesModel>> models;
    for (const auto &map : maps) {
        auto model = QSharedPointer<CategoriesModel>::create();
        model->fromVariantMap(map);
        models.append(model);
    }
    return models;
}

QList<QSharedPointer<CategoriesModel>> CategoriesCtrl::getAllActive()
{
    QList<QVariantMap> maps = SqlCtrlBase::getAllActive();
    QList<QSharedPointer<CategoriesModel>> models;
    for (const auto &map : maps) {
        auto model = QSharedPointer<CategoriesModel>::create();
        model->fromVariantMap(map);
        models.append(model);
    }
    return models;
} 