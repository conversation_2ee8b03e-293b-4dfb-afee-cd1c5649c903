#ifndef CATEGORYATTRIBUTELINKSCTRL_H
#define CATEGORYATTRIBUTELINKSCTRL_H

#include "sqlCtrlBase.h"
#include "../sqlModel/categoryattributelinksmodel.h"
#include <QSharedPointer>
#include <QList>

class CategoryAttributeLinksCtrl : public SqlCtrlBase
{
    Q_OBJECT
public:
    explicit CategoryAttributeLinksCtrl(const QString &dbPath, QObject *parent = nullptr);

    bool checkAndCreateTable() override;

    bool insert(const CategoryAttributeLinksModel &model);
    bool update(const CategoryAttributeLinksModel &model);

    QSharedPointer<CategoryAttributeLinksModel> getByUUID(const QString &uuid);
    QList<QSharedPointer<CategoryAttributeLinksModel>> getAll();

    // specific queries
    QList<QSharedPointer<CategoryAttributeLinksModel>> getLinksForCategory(const QString& categoryUUID);
    bool deleteByCategoryAndAttribute(const QString& categoryUUID, const QString& attributeUUID);
};

#endif // CATEGORYATTRIBUTELINKSCTRL_H 