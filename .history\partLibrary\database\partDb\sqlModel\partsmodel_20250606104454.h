#ifndef PARTSMODEL_H
#define PARTSMODEL_H

//============================================================================
/// file
/// brief      元器件信息主数据模型
/// author     wm
/// version    1.0
/// date       2025/06/05
/// todo
//============================================================================

#include "sqlModelBase.h"
#include <memory>
#include <QUuid>
#include <QDateTime>

class PartsModel : public SqlModelBase
{
public:
    PartsModel() : id(QUuid::createUuid().toString()), revision(1){}

public:
    // 模型属性
    QString id;                 ///< 元器件uuid
    QString partNumber;         ///< 零件号
    QString categoryUUID;       ///< 分类UUID
    QString manufacturerUUID;   ///< 制造商UUID
    QString description;        ///< 描述
    QString lifecycleStatus;    ///< 生命周期状态
    QString coreAttributesJson; ///< 核心属性JSON
    QString customAttributesJson;///< 自定义属性JSON
    QString tagsJson;           ///< 标签JSON
    QDateTime creationTimestamp;    ///< 创建时间
    QString createdBy;          ///< 创建者
    QDateTime lastModifiedTimestamp;///< 最后修改时间
    QString lastModifiedBy;     ///< 最后修改者
    int     revision;           ///< 修订版本号

public:
    QString tableName()       const override {return "Parts";}
    QString primaryKeyField() const override {return "PartUUID";}
    QVariantMap modelToDbMap() const override;
    void dbToModel(const QSqlRecord& record) override;

public:
    // 字段名称常量
    static const QString FIELD_ID;
    static const QString FIELD_PART_NUMBER;
    static const QString FIELD_CATEGORY_UUID;
    static const QString FIELD_MANUFACTURER_UUID;
    static const QString FIELD_DESCRIPTION;
    static const QString FIELD_LIFECYCLE_STATUS;
    static const QString FIELD_CORE_ATTRIBUTES_JSON;
    static const QString FIELD_CUSTOM_ATTRIBUTES_JSON;
    static const QString FIELD_TAGS_JSON;
    static const QString FIELD_CREATION_TIMESTAMP;
    static const QString FIELD_CREATED_BY;
    static const QString FIELD_LAST_MODIFIED_TIMESTAMP;
    static const QString FIELD_LAST_MODIFIED_BY;
    static const QString FIELD_REVISION;
    static const QString FIELD_IS_DELETED;

    static std::shared_ptr<PartsModel> createPtr()
    {
        return std::make_shared<PartsModel>();
    }
};

using PartsModelPtr = std::shared_ptr<PartsModel>;
using PartsModelList = QHash<QString, PartsModelPtr>;

#endif // PARTSMODEL_H 