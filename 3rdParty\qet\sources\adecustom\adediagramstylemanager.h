#ifndef ADEDIAGRAMSTYLEMANAGER_H
#define ADEDIAGRAMSTYLEMANAGER_H

#include <QMap>
#include <QColor>
#include <QGraphicsItem>

#include "../CustomExtend/adeelementstyle.h"
#include "sql/sqlCtrl/compartmentctrl.h"
#include "sql/sqlCtrl/subsystemctrl.h"

class Diagram;

class ADEDiagramEqStyleManager
{
public:
    ADEDiagramEqStyleManager(Diagram *diagram);
    ~ADEDiagramEqStyleManager();

    enum DiagramStyle
    {
        Custom = 0,
        SubSystem,
        Compartment,
        Empty
    };

public:
    void initFromPrjUuid(const QString &prjUuid);

    void switchDiagramStyle(DiagramStyle style, QList<QGraphicsItem *> qgis);  ///< 主动切换布置图样式

    void updateSubSystemStyleOrCompartmentStyleInfo(Diagram *currentDgm);  ///< 系统或位置样式改变，主动刷新
    void updateCustomStyleInfo(QString uuid, AdeElementStyle style);   ///< 更新自定义样式

    QDomElement toXml(QDomElement &root);
    bool fromXml(const QDomElement &root);

    void setCurrentStyle(DiagramStyle style);
    DiagramStyle getCurrentStyle() {return mCurrentStyle;}

    void defaultCustomDiagramStyleWithoutXmlInfo(QList<QGraphicsItem *> qgis);

    void clearCustomStyleContainers();

    QMap<QString, AdeElementStyle> getCustomElementStyles() {return customElementStyles;}

private:
    void initSubSysytemStylesAndCompartmentStyles(const QString &prjUuid);

private:
    QMap<QString, AdeElementStyle> mSubSystemStyles;     ///< (所属系统名称, 颜色)
    QMap<QString, AdeElementStyle> mCompartmentStyles;       ///< (所属位置名称, 颜色)

    DiagramStyle mCurrentStyle = Custom;

    compartmentCtrl *cptCtrl;
    subsystemCtrl * sysCtrl;

    AdeElementStyle mDefaultNoStyle;

    Diagram *mDiagram;

    QMap<QString, AdeElementStyle> customElementStyles;       ///< (eleUuid, AdeElementStyle)
};

#endif // ADEDIAGRAMSTYLEMANAGER_H
