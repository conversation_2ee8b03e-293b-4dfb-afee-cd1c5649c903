# -------------------------------------------------
# --- Project file for the Part DB Test ---
# -------------------------------------------------

QT      += core sql
CONFIG  += console c++11
TEMPLATE = app

# The name of the test executable
TARGET = test_partdb

# Add the root of the partLibrary as an include path to find headers
INCLUDEPATH += $$PWD/..

# The source file for the test itself
SOURCES += $$PWD/test_partdb.cpp

# Add the required source files from the library that this test depends on.
# These are relative to this .pro file's location (partLibrary/tests/)
SOURCES += \
    $$PWD/../database/sqlCtrlBase.cpp \
    $$PWD/../database/sqlModelBase.cpp \
    $$PWD/../database/partDb/sqlCtrl/datasheetsctrl.cpp \
    $$PWD/../database/partDb/sqlCtrl/libraryinfoctrl.cpp \
    $$PWD/../database/partDb/sqlCtrl/partdatasheetlinksctrl.cpp \
    $$PWD/../database/partDb/sqlCtrl/partsctrl.cpp \
    $$PWD/../database/partDb/sqlModel/datasheetsmodel.cpp \
    $$PWD/../database/partDb/sqlModel/libraryinfomodel.cpp \
    $$PWD/../database/partDb/sqlModel/partdatasheetlinksmodel.cpp \
    $$PWD/../database/partDb/sqlModel/partsmodel.cpp

# Note: The test executable will be placed in the build directory. 