#include "notifymsgwrap.h"

#include "notifymanager.h"

notifyMsgWrap *notifyMsgWrap::mInstance = nullptr;

notifyMsgWrap::notifyMsgWrap():
    mNotifyManager(new NotifyManager())
{

}

notifyMsgWrap::~notifyMsgWrap()
{
    if(mNotifyManager != nullptr)
    {
        delete mNotifyManager;
        mNotifyManager = nullptr;
    }
}

void notifyMsgWrap::showMsg(const QString &msg, const MsgType &msgType)
{
    mMsgMutex.lock();
    if(MsgType::ErrMsg == msgType)
        mNotifyManager->notify("提示", msg, ":/icon/errMsg.png", "");
    else if(MsgType::RightMsg == msgType)
        mNotifyManager->notify("提示", msg, ":/icon/rightMsg.png", "");
    else if(MsgType::WaringMsg == msgType)
        mNotifyManager->notify("提示", msg, ":/icon/waringMsg.png", "");
    mMsgMutex.unlock();
}
