#ifndef PARTVIEWMODEL_H
#define PARTVIEWMODEL_H

#include "../sqlModel/partmodel.h"
#include "../../globalDb/sqlModel/attributedefinitionsmodel.h"

#include <QList>
#include <QString>
#include <QVariant>
#include <QStringList>
#include <QDateTime>


/**
 * @brief 用于UI展示的、被"丰富化"的单个核心属性的模型
 */
struct PartAttributeViewModel
{
    // 来自 PartModel::CoreAttrInfo 的数据
    QVariant value;

    // 来自 AttributeDefinitionsModel 的数据
    QString id;
    QString irdi;
    QString name;
    QString dataType;
    QString description;
    QString unit;
    // ... 可以按需添加更多来自 AttributeDefinitionsModel 的字段
};

/**
 * @brief 最终提供给UI使用的、完整的元器件视图模型
 */
struct PartViewModel
{
    // 直接从 PartModel 复制过来的基础信息
    QString id;
    QString partNumber;
    QString categoryUUID;
    QString manufacturerUUID;
    QString description;
    QString lifecycleStatus;
    QStringList tags;
    QDateTime creationTimestamp;    ///< 创建时间
    QString createdBy;          ///< 创建者
    QDateTime lastModifiedTimestamp;///< 最后修改时间
    QString lastModifiedBy;     ///< 最后修改者
    int     revision;           ///< 修订版本号

    // 使用被丰富化的属性模型列表
    QList<PartAttributeViewModel> coreAttributes;

    // 自定义属性通常不需要丰富化，直接使用即可
    QList<PartModel::CustomAttrInfo> customAttributes;
};


#endif // PARTVIEWMODEL_H 
