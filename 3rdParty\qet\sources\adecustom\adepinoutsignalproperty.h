﻿#ifndef ADEPINOUTSIGNALPROPERTY_H
#define ADEPINOUTSIGNALPROPERTY_H

#include <QDomElement>
#include <QPair>

class ADEPinoutSignalProperty
{
public:
    ADEPinoutSignalProperty();

    void init(const QString &signalName ,
              const QString &type       ,
              const QString &current    ,
              const QString &voltage    ,
              const QString &frequency  ,
              QPair<QString, QString> category,
              bool used);
    void init(const QDomElement &xmlElmt);

    QDomElement toXmlElement(QDomDocument &document) const;

    /**
     * @brief propToString 信号属性使用字符串表示
     * @param pinOutNum    点位名称
     * @return
     */
    QString propToString(const QString &pinOutNum) const;

public:
    QString mSignalName;        ///< 信号名称
    QString mType;              ///< 类型（电源、单端、差分、接壳）
    QString mCurrent;           ///< 电流
    QString mVoltage;           ///< 电压
    QString mFrequency;         ///< 频率

    QPair<QString, QString> mCustomCategory;///< 自定义信号类型<categoryName, categoryColor>

    bool mUsedState = false;
};

#endif // ADEPINOUTSIGNALPROPERTY_H
