#pragma once

#include <QString>
#include <QByteArray>
#include <QUuid>

namespace part_library
{
namespace database
{

/**
 * @brief Represents a record in the PartImages table.
 * @note Corresponds to the PartImages table in the database design.
 */
struct PartImage
{
    QString imageUUID{QUuid::createUuid().toString(QUuid::WithoutBraces)};
    QString partUUID;
    QByteArray pixmap;
    QString imageType;
    int displayOrder = 0;
    QString description;
    bool isDeleted = false;
};

} // namespace database
} // namespace part_library 